# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

.env

# dependencies
node_modules/
android/
ios/

# Expo
.expo/
dist/
web-build/

# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local
.env.*

# typescript
*.tsbuildinfo


.prettierrc