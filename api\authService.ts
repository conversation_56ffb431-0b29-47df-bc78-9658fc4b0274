import { Dispatch } from "@reduxjs/toolkit";
import apiClient from "./apiClient";
import { setUser } from "../reduxStore/userSlice";
import { USER_ROLES } from "../constants/AuthConstants";

interface RegisterUserData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  firebaseUID: string;
  profilePic?: string;
  role?: string[];
}

export const registerUser = async (userData: RegisterUserData, dispatch: Dispatch) => {
  try {
    console.log("Registering user:", userData);
    const response = await apiClient.post("/auth/register", userData);
    return response.data;
  } catch (error: any) {
    console.error("Error registering user:", error.response?.data || error.message);
    throw error;
  }
};

export const getUserProfile = async (dispatch: Dispatch) => {
  try {
    console.log("Getting user profile");
    const response = await apiClient.get("/users/me");
    const user = {
      ...response.data,
      isOrganizer:
        response.data?.role?.includes(USER_ROLES.JOURNEY_CAPTAIN) ||
        response.data?.role?.includes(USER_ROLES.HANGOUT_HOST),
    };
    dispatch(setUser(user));
    return response.data;
  } catch (error: any) {
    console.error("Error getting user profile:", error.response?.data || error.message);
    throw error;
  }
};
