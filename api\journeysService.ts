import apiClient from "./apiClient";

export interface Journey {
  _id: string;
  title: string;
  shortDescription: string;
  destination: string;
  destinationCountry: string;
  destinationCity: string;
  startDate: string;
  endDate: string;
  travelMode: string[];
  basePrice: number;
  coverImage: string;
  difficultyLevel: string;
  averageRating: number;
  status: string;
}

export interface JourneyDetail extends Journey {
  slug: string;
  description: string;
  locations: Location[];
  duration: number;
  departureLocation: Location;
  returnLocation: Location;
  organizer: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
    profilePic: string;
  };
  organizerInstagramProfile?: string;
  coOrganizers: any[];
  minParticipants: number;
  maxParticipants: number;
  currentParticipants: number;
  status: string;
  isPublic: boolean;
  isFeatured: boolean;
  pricingTiers: PricingTier[];
  currency: string;
  depositAmount: number;
  depositDueDate: string;
  finalPaymentDueDate: string;
  earlyBirdDiscount: number;
  earlyBirdDeadline: string;
  groupDiscounts: GroupDiscount[];
  cancellationPolicy: CancellationPolicy;
  itinerary: ItineraryDay[];
  images: string[];
  videos: string[];
  difficultyLevel: string;
  ageRestriction: {
    minAge: number;
    maxAge: number;
  };
  accessibility: {
    wheelchairAccessible: boolean;
    mobilityRestrictions: string;
    specialNeeds: string;
  };
  included: string[];
  excluded: string[];
  requiredDocuments: string[];
  packingList: string[];
  weatherInfo: string;
  emergencyContacts: EmergencyContact[];
  safetyGuidelines: string;
  travelInsuranceRequired: boolean;
  tags: string[];
  ratings: any[];
  promoCode?: string;
  promoDiscount?: number;
  viewsCount?: number;
}

interface Location {
  name: string;
  coordinates: {
    type: string;
    coordinates: number[];
  };
  address: string;
  country: string;
  city: string;
  state: string;
  zipCode: string;
  countryCode: string;
  _id: string;
}

interface PricingTier {
  name: string;
  price: number;
  description: string;
  availableUntil: string;
  maxParticipants: number;
  benefits: string[];
  _id: string;
}

interface GroupDiscount {
  minPeople: number;
  discountPercentage: number;
  _id: string;
}

interface CancellationPolicy {
  fullRefundDays: number;
  partialRefundDays: number;
  partialRefundPercentage: number;
  termsAndConditions: string;
}

interface ItineraryDay {
  dayNumber: number;
  date: string;
  title: string;
  description: string;
  activities: Activity[];
  accommodation: Accommodation;
  meals: {
    breakfast: boolean;
    lunch: boolean;
    dinner: boolean;
    includedInPrice: boolean;
  };
  mapEmbedLink: string;
  _id: string;
}

interface Activity {
  name: string;
  startTime: string;
  endTime: string;
  location: Location;
  includedInPrice: boolean;
  additionalCost: number;
  activityType: string;
  images: string[];
  _id: string;
}

interface Accommodation {
  name: string;
  address: string;
  checkInTime: string;
  checkOutTime: string;
  amenities: string[];
  roomType: string;
  images: string[];
}

interface EmergencyContact {
  name: string;
  phone: string;
  role: string;
  _id: string;
}

export interface JourneysResponse {
  success: boolean;
  count: number;
  data: Journey[];
  pagination?: {
    total: number;
    page: number;
    pages: number;
    limit: number;
  };
}

export interface JourneyDetailResponse {
  success: boolean;
  data: JourneyDetail;
  participantCount?: number;
  userBookingStatus: {
    isBooked: boolean;
    status: string;
  };
}

export interface JourneyFilters {
  page?: number;
  limit?: number;
  // Search & Location
  search?: string;
  destination?: string;
  destinationCountry?: string;
  destinationCity?: string;

  // Dates & Duration
  startDate?: string;
  endDate?: string;
  maxDuration?: number;

  // Categories & Tags
  categories?: string | string[];
  tags?: string | string[];

  // Travel Details
  travelMode?: string | string[];

  // Price
  minBudget?: number;
  maxBudget?: number;

  // Difficulty
  difficultyLevel?: string;

  // Ratings & Organizer
  minRating?: number;
  organizerId?: string;

  // Sorting
  sortBy?: string;
  sortOrder?: "asc" | "desc";

  // Quick filters
  filter?: string;
}

class JourneysService {
  async getFeaturedJourneys(limit: number = 5): Promise<JourneysResponse> {
    const { data } = await apiClient.get<JourneysResponse>(`/journey/featured`, {
      params: { limit },
    });
    return data;
  }

  async getUpcomingJourneys(filters: JourneyFilters = {}): Promise<JourneysResponse> {
    const { data } = await apiClient.get<JourneysResponse>(`/journey/upcoming`, {
      params: filters,
    });
    return data;
  }

  async getJourneyById(id: string): Promise<JourneyDetailResponse> {
    const { data } = await apiClient.get<JourneyDetailResponse>(`/journey/${id}`);
    return data;
  }

  async bookJourney(journeyId: string, pricingTierId?: string): Promise<any> {
    const { data } = await apiClient.post(`/journey/${journeyId}/book`, { pricingTierId });
    return data;
  }

  async searchJourneys(filters: JourneyFilters = {}): Promise<JourneysResponse> {
    const { data } = await apiClient.get<JourneysResponse>(`/journey/filter`, {
      params: filters,
    });
    return data;
  }

  async getMyJourneys(): Promise<JourneysResponse> {
    const { data } = await apiClient.get<JourneysResponse>(`/journey/user/journeys`);
    return data;
  }

  // participant check-in
  async checkInParticipant(participantId: string): Promise<any> {
    const { data } = await apiClient.post(`/journey/participants/${participantId}/check-in`);
    return data;
  }

  async submitJourneyUpiPayment(
    journeyId: string,
    upiPaymentScreenshotUrl: string,
    pricingTier: string,
    promoCode?: string
  ) {
    const { data } = await apiClient.post(`/journey/${journeyId}/upi-payment`, {
      upiPaymentScreenshotUrl,
      pricingTier,
      promoCode,
    });
    return data;
  }

  async getJourneyParticipants(journeyId: string) {
    const { data } = await apiClient.get(`/journey/${journeyId}/participants`);
    return data;
  }

  async approveJourneyParticipant(journeyId: string, participantId: string) {
    const { data } = await apiClient.patch(`/journey/${journeyId}/participants/${participantId}/approve`);
    return data;
  }

  async rejectJourneyParticipant(journeyId: string, participantId: string, reason: string) {
    const { data } = await apiClient.patch(`/journey/${journeyId}/participants/${participantId}/reject`, { reason });
    return data;
  }
}

export const journeysService = new JourneysService();
