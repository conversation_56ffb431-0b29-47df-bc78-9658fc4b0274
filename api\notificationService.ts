import apiClient from "./apiClient";

export interface NotificationResponse {
  success: boolean;
  data: Notification[];
  pagination: {
    total: number;
    page: number;
    pages: number;
    limit: number;
  };
}

export interface NotificationCountResponse {
  success: boolean;
  data: {
    unreadCount: number;
  };
}

export interface MarkReadResponse {
  success: boolean;
  data: {
    _id: string;
    isRead: boolean;
    readAt: string;
  };
}

export interface MarkAllReadResponse {
  success: boolean;
  data: {
    modifiedCount: number;
  };
}

export interface Notification {
  _id: string;
  user: string;
  title: string;
  message: string;
  type: string;
  isRead: boolean;
  actionType?: string;
  actionText?: string;
  actionUrl?: string;
  createdAt: string;
  readAt?: string;
}

export interface NotificationFilters {
  page?: number;
  limit?: number;
  isRead?: boolean;
  type?: string;
}

class NotificationService {
  async getNotifications(filters: NotificationFilters = {}): Promise<NotificationResponse> {
    const { data } = await apiClient.get<NotificationResponse>("/notifications", {
      params: filters,
    });
    return data;
  }

  async getNotificationCount(): Promise<NotificationCountResponse> {
    const { data } = await apiClient.get<NotificationCountResponse>("/notifications/count");
    return data;
  }

  async markAsRead(notificationId: string): Promise<MarkReadResponse> {
    const { data } = await apiClient.put<MarkReadResponse>(`/notifications/${notificationId}/read`);
    return data;
  }

  async markAllAsRead(): Promise<MarkAllReadResponse> {
    const { data } = await apiClient.put<MarkAllReadResponse>("/notifications/read-all");
    return data;
  }
}

export const notificationService = new NotificationService();
