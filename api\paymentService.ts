import axios from "./apiClient";

export const paymentService = {
  // Create payment order for a hangout
  createPaymentOrder: async (hangoutId: string, customAmount?: number, userData?: any) => {
    const response = await axios.post(`/payments/hangout/${hangoutId}/order`, {
      customAmount,
    });

    // Handle both response structures - direct data or wrapped in data property
    const responseData = response.data?.data || response.data;

    // Use backend payuData if present, otherwise construct
    if (responseData.payuData) {
      // Use backend-provided payuData as-is
      // (do not reconstruct or overwrite)
    } else if (responseData?.orderId && responseData?.key && responseData?.amount) {
      responseData.payuData = {
        key: responseData.key,
        txnid: responseData.orderId,
        amount: responseData.amount.toString(),
        productinfo: `Hangout Booking`,
        firstname: userData?.firstName || "User",
        email: userData?.email || "<EMAIL>",
        phone: userData?.phone || "1234567890",
        surl: "https://api.logoutloud.com/api/payments/success",
        furl: "https://api.logoutloud.com/api/payments/failure",
        hash: responseData.orderToken || "",
        udf1: responseData.paymentId || "",
        udf2: hangoutId,
        udf3: responseData.paymentId || "",
        udf4: "hangout",
        udf5: responseData.bookingReference || "",
        payment_related_details_for_mobile_sdk: responseData.payment_related_details_for_mobile_sdk,
        vas_for_mobile_sdk: responseData.vas_for_mobile_sdk,
        payment: responseData.payment,
      };
    } else {
      throw new Error("Invalid payment order response: missing required fields");
    }

    return response.data;
  },

  // Verify payment after PayU completion
  verifyPayment: async (paymentData: {
    paymentId: string;
    orderId: string;
    signature: string;
    paymentDetails: any;
  }) => {
    const response = await axios.post("/payments/hangout/verify", paymentData);
    return response.data;
  },

  // Get payment status for a hangout
  getPaymentStatus: async (hangoutId: string) => {
    const response = await axios.get(`/hangouts/${hangoutId}/payment-status`);
    return response.data;
  },

  validatePromoCode: async (journeyId: string, promoCode: string) => {
    const response = await axios.post(`/payments/journey/${journeyId}/validate-promo`, {
      promoCode,
    });
    return response.data;
  },

  // Create payment order for a journey
  createJourneyPaymentOrder: async (journeyId: string, pricingTier?: string, promoCode?: string, userData?: any) => {
    const response = await axios.post(`/payments/journey/${journeyId}/order`, {
      pricingTier,
      promoCode,
    });

    // Handle both response structures - direct data or wrapped in data property
    const responseData = response.data?.data || response.data;

    // Use backend payuData if present, otherwise construct
    if (responseData.payuData) {
      // Use backend-provided payuData as-is
    } else if (responseData?.orderId && responseData?.key && responseData?.amount) {
      responseData.payuData = {
        key: responseData.key,
        txnid: responseData.orderId,
        amount: responseData.amount.toString(),
        productinfo: `Journey Booking`,
        firstname: userData?.firstName || "User",
        email: userData?.email || "<EMAIL>",
        phone: userData?.phone || "1234567890",
        surl: "https://api.logoutloud.com/api/payments/success",
        furl: "https://api.logoutloud.com/api/payments/failure",
        hash: responseData.orderToken || "",
        udf1: responseData.paymentId || "",
        udf2: journeyId,
        udf3: responseData.paymentId || "",
        udf4: "journey",
        udf5: responseData.bookingReference || "",
        payment_related_details_for_mobile_sdk: responseData.payment_related_details_for_mobile_sdk,
        vas_for_mobile_sdk: responseData.vas_for_mobile_sdk,
        payment: responseData.payment,
      };
    } else {
      throw new Error("Invalid payment order response: missing required fields");
    }

    return response.data;
  },

  // Verify payment after PayU completion
  verifyJourneyPayment: async (paymentData: {
    paymentId: string;
    orderId: string;
    signature: string;
    paymentDetails: any;
  }) => {
    const response = await axios.post("/payments/journey/verify", paymentData);
    return response.data;
  },

  // Get payment status for a journey
  getJourneyPaymentStatus: async (journeyId: string) => {
    const response = await axios.get(`/journey/${journeyId}/payment-status`);
    return response.data;
  },

  // Get journey ticket
  getJourneyTicket: async (journeyId: string) => {
    const response = await axios.get(`/payments/journey/${journeyId}/ticket`);
    return response.data.data;
  },

  generateResponseHash: async (hashName: string, hashString: string, postSalt?: string) => {
    const response = await axios.post("/payments/payu/generate-hash", { hashString, postSalt });
    if (response.data && response.data.hash) {
      return response.data.hash;
    }
    throw new Error("Failed to generate hash from backend");
  },
};
