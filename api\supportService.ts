import apiClient from "./apiClient";

export interface SupportRequest {
  subject: string;
  message: string;
  category: 'bug' | 'app_crash' | 'login_issues' | 'feature_request' | 'account' | 'booking_issues' | 'payment' | 'other';
  priority: 'low' | 'normal' | 'high';
  attachments?: string[];
}

export const supportService = {
  submitSupportRequest: async (requestData: SupportRequest) => {
    const response = await apiClient.post("/support", requestData);
    return response.data;
  }
};