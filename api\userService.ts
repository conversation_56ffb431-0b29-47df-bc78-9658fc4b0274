import apiClient from "./apiClient";

interface ProfileData {
  coverPic?: string;
  bio?: string;
  dob?: string;
  gender?: string;
  [key: string]: any;
}

interface UpdateUserData {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  profilePic?: string;
  profile?: ProfileData;
  [key: string]: any;
}

export const updateUserProfile = async (userData: UpdateUserData) => {
  try {
    const response = await apiClient.put("/users/me", userData);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const updateNotificationPreferencesInBackend = async (
  notificationPrefs: Record<string, boolean>,
  pushEnabled: boolean
) => {
  try {
    const response = await apiClient.patch("/users/preferences/notifications", {
      notifications: {
        ...notificationPrefs,
        pushNotifications: pushEnabled,
      },
    });
    console.log("Notification preferences updated:", response.data);

    return response.data;
  } catch (error) {
    throw error;
  }
};
