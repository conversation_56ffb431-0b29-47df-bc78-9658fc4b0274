{"expo": {"name": "logoutloud", "slug": "logoutloud", "scheme": "logoutloud", "version": "1.0.1", "orientation": "portrait", "icon": "./assets/images/main-logo.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/images/main-logo.png", "resizeMode": "cover", "backgroundColor": "#000000"}, "ios": {"supportsTablet": true, "newArchEnabled": true, "config": {"googleMapsApiKey": "AIzaSyA7L042Zd6d4_bwg5arkGOvirfp5BLStf4", "usesAppleSignIn": true}, "googleServicesFile": "./GoogleService-Info.plist", "bundleIdentifier": "com.logoutloud.app", "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "NSCameraUsageDescription": "Allow Logoutloud to access your camera for scanning QR codes and taking photos", "NSPhotoLibraryUsageDescription": "Allow Logoutloud to access your photos to save and share tickets", "NSPhotoLibraryAddUsageDescription": "Allow Logoutloud to save photos to your library", "NSLocationWhenInUseUsageDescription": "Allow Logoutloud to use your location to find nearby hangouts and journeys"}, "splash": {"image": "./assets/images/main-logo.png", "resizeMode": "contain", "backgroundColor": "#000000"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#000000"}, "newArchEnabled": true, "package": "com.logoutloud.app", "config": {"googleMaps": {"apiKey": "AIzaSyA7L042Zd6d4_bwg5arkGOvirfp5BLStf4"}}, "googleServicesFile": "./google-services.json", "permissions": ["android.permission.CAMERA", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.READ_MEDIA_IMAGES"]}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["expo-router", "expo-font", ["expo-camera", {"cameraPermission": "Allow Logoutloud to access your camera so you can scan QR codes for check-ins."}], ["expo-notifications", {"icon": "./assets/images/notification-icon.png", "color": "#FFFFFF", "sounds": ["./assets/sounds/notification_sound.wav"]}], "./plugins/withPayU.js"], "fonts": [{"asset": "assets/fonts/Poppins-Regular.ttf"}, {"asset": "assets/fonts/Poppins-Medium.ttf"}, {"asset": "assets/fonts/Poppins-SemiBold.ttf"}, {"asset": "assets/fonts/Poppins-Bold.ttf"}, {"asset": "assets/fonts/Raleway-Light.ttf"}, {"asset": "assets/fonts/Raleway-Regular.ttf"}, {"asset": "assets/fonts/Raleway-Medium.ttf"}, {"asset": "assets/fonts/Roboto-Light.ttf"}, {"asset": "assets/fonts/Roboto-Regular.ttf"}, {"asset": "assets/fonts/Roboto-Medium.ttf"}], "owner": "chethanb", "extra": {"router": {"origin": false}, "eas": {"projectId": "506671e7-4723-4f96-b77a-c6a5225adbb1"}}, "updates": {"enabled": true, "fallbackToCacheTimeout": 0, "url": "https://u.expo.dev/506671e7-4723-4f96-b77a-c6a5225adbb1"}, "runtimeVersion": {"policy": "appVersion"}}}