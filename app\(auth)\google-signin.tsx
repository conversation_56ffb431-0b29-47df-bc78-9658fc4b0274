import { View, Text, ImageBackground, Platform } from "react-native";
import React from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { AUTH_SCREEN_IMAGES } from "../../constants/OnBoarding/OnBoardingConstants";
import PrimaryButton from "../../components/Buttons/PrimaryButton";
import HorizontalLine from "../../components/Common/HorizontalLine";
import { Link, useNavigation } from "expo-router";
import { icons } from "../../constants/common";
import { NavigationProp } from "../../lib/types/commonTypes";
import useGoogleAuth from "../../hooks/useGoogleAuth";
import useAppleAuth from "../../hooks/useAppleAuth";
import * as AppleAuthentication from "expo-apple-authentication";
import { StatusBar } from "expo-status-bar";

const GoogleSignIn = () => {
  const navigation = useNavigation<NavigationProp>();
  const { signInWithGoogle, loading: googleLoading } = useGoogleAuth();
  const { signInWithApple, loading: appleLoading } = useAppleAuth();
  const [appleAuthAvailable, setAppleAuthAvailable] = React.useState(false);

  React.useEffect(() => {
    // Check if Apple authentication is available on this device
    AppleAuthentication.isAvailableAsync().then((isAvailable: boolean) => setAppleAuthAvailable(isAvailable));
  }, []);

  const loading = googleLoading || appleLoading;

  return (
    <View className="w-full h-full flex justify-center items-center">
      <StatusBar style="dark" />
      <ImageBackground source={AUTH_SCREEN_IMAGES.signup_bg} className="w-full h-full"></ImageBackground>
      <SafeAreaView className="w-full h-full flex justify-end absolute bottom-6">
        <View className="flex p-6 gap-y-4">
          <View className="gap-y-2">
            <View className="flex-row justify-center items-center">
              <Text className="text-white text-3xl text-center font-heading">Logout</Text>
              <Text className="text-primary text-3xl text-center font-heading">loud</Text>
            </View>
            <View>
              <Text className="text-white text-[12px] text-center font-body">
                The cure for loneliness isn't Wi-Fi. It's people.
              </Text>
            </View>
          </View>
          <View>
            <PrimaryButton
              buttonText={googleLoading ? "Loading..." : "Continue with Google"}
              bgColor={"bg-white"}
              disabled={loading}
              icon={icons.google}
              borderRadius={"xl"}
              onPressHandler={() => {
                signInWithGoogle();
              }}
            />
          </View>

          {appleAuthAvailable && (
            <View>
              <PrimaryButton
                buttonText={appleLoading ? "Loading..." : "Continue with Apple"}
                bgColor={"bg-white"}
                disabled={loading}
                icon={icons.apple}
                borderRadius={"xl"}
                onPressHandler={() => {
                  signInWithApple();
                }}
              />
            </View>
          )}

          <View>
            <HorizontalLine text={"or"} />
          </View>
          <View>
            <PrimaryButton
              onPressHandler={() => {
                navigation.navigate("sign-in");
              }}
              buttonText={loading ? "Loading..." : "Log In with Email"}
              borderRadius={"xl"}
            />
          </View>
          <Link href={"/sign-up"} className="text-white text-center text-sm font-subheading">
            Don't have an account? Sign Up
          </Link>
        </View>
      </SafeAreaView>
    </View>
  );
};

export default GoogleSignIn;
