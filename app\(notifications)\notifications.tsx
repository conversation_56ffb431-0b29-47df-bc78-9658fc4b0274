import React, { useState } from "react";
import { View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter } from "expo-router";
import { LinearGradient } from "expo-linear-gradient";
import { StatusBar } from "expo-status-bar";

// Components
import PageHeader from "../../components/Common/PageHeader";
import NotificationList from "../../components/Notification/NotificationList";

// Hooks and Services
import {
  useNotifications,
  useMarkNotificationAsRead,
  useMarkAllNotificationsAsRead,
} from "../../hooks/useNotifications";
import { Notification as NotificationType } from "../../api/notificationService";
import { showToast } from "../../lib/utils/showToast";

const NotificationScreen = () => {
  const router = useRouter();
  const [refreshing, setRefreshing] = useState(false);

  // Get notifications from API
  const { data, isLoading, fetchNextPage, hasNextPage, isFetchingNextPage, refetch } = useNotifications();

  // Mutations for marking notifications as read
  const markAsRead = useMarkNotificationAsRead();
  const markAllAsRead = useMarkAllNotificationsAsRead();

  // Flatten the pages of notifications
  const notifications = data?.pages.flatMap((page) => page.data) || [];

  // Handle refreshing
  const handleRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  };

  // Handle marking a notification as read
  const handleMarkAsRead = (notification: NotificationType) => {
    if (!notification.isRead) {
      markAsRead.mutate(notification._id, {
        onSuccess: () => {
          // Handle any navigation based on action type
          if (notification.actionType === "link" && notification.actionUrl) {
            router.push(notification.actionUrl);
          }
        },
        onError: () => {
          showToast("error", "Failed to mark notification as read");
        },
      });
    } else if (notification.actionType === "link" && notification.actionUrl) {
      // If already read, just navigate
      router.push(notification.actionUrl);
    }
  };

  // Handle marking all notifications as read
  const handleMarkAllAsRead = () => {
    markAllAsRead.mutate(undefined, {
      onSuccess: (data) => {
        showToast("success", `Marked ${data.data.modifiedCount} notifications as read`);
      },
      onError: () => {
        showToast("error", "Failed to mark all notifications as read");
      },
    });
  };

  return (
    <SafeAreaView className="flex-1 bg-white" edges={["top"]}>
      <StatusBar style="dark" />

      {/* Background gradient */}
      <LinearGradient
        colors={["rgba(255, 222, 89, 0.05)", "rgba(255, 255, 255, 0)"]}
        style={{ position: "absolute", height: 200, left: 0, right: 0, top: 0 }}
      />

      {/* Header */}
      <View className="z-10">
        <PageHeader title={"Notifications"} color="black" showIcon={false} />
      </View>

      {/* Notification List */}
      <NotificationList
        notifications={notifications}
        isLoading={isLoading}
        isFetchingNextPage={isFetchingNextPage}
        refreshing={refreshing}
        onRefresh={handleRefresh}
        onEndReached={() => {
          if (hasNextPage && !isFetchingNextPage) {
            fetchNextPage();
          }
        }}
        onMarkAsRead={handleMarkAsRead}
        onMarkAllAsRead={handleMarkAllAsRead}
        isMarkingAllAsRead={markAllAsRead.isPending}
      />
    </SafeAreaView>
  );
};

export default NotificationScreen;
