import { FlatList, Text, View, Dimensions, Image } from "react-native";
import React from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { dummyData } from "../../constants/HomePageConstants"; // Make sure the type of `dummyData` is defined properly in the constants
import { LinearGradient } from "expo-linear-gradient";
import PageHeader from "../../components/Common/PageHeader";
import PlanOrPackageDetails from "../../components/Reels/PlanOrPackageDetails";
import { PackageItem } from "../../lib/types/Reels/planOrPackageReelTypes";

const PackageReels: React.FC = () => {
  const { height } = Dimensions.get("window");

  // Render item function for FlatList
  const renderItem = ({ item }: { item: PackageItem }) => {
    return (
      <View className="relative" style={{ height: height }}>
        <Image source={{ uri: item.image }} resizeMode="cover" className="w-full h-full" />
        <LinearGradient
          colors={["rgba(0,0,0,0)", "rgba(0,0,0,1)"]}
          start={{ x: 0.5, y: 0 }}
          end={{ x: 0, y: 1 }}
          className="absolute top-0 w-full h-full"
        >
          <View className="h-full justify-between">
            <PageHeader />
            <PlanOrPackageDetails item={item} />
          </View>
        </LinearGradient>
      </View>
    );
  };

  return (
    <SafeAreaView className="flex-1">
      <FlatList
        data={dummyData?.popularPackages} // assuming `popularPackages` is an array of `PackageItem` in `dummyData`
        renderItem={renderItem}
        keyExtractor={(item) => item.place}
        pagingEnabled
        snapToAlignment="start"
        decelerationRate="fast"
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
};

export default PackageReels;
