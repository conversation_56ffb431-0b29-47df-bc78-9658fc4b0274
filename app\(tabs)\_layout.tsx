import { View, Text, Platform } from "react-native";
import React from "react";
import { Redirect, Tabs } from "expo-router";
import { Ionicons } from "@expo/vector-icons"; // or any other icon library
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import MaterialCommunityIcons from "@expo/vector-icons/MaterialCommunityIcons";
import { toTitleCase } from "../../lib/utils/commonUtils";
import { useAppSelector } from "../../reduxStore/hooks";
import { RootState } from "../../reduxStore/store";
import { USER_ROLES } from "../../constants/AuthConstants";

const TabsLayout = () => {
  const { user } = useAppSelector((state: RootState) => state.user);

  // More explicit check for organizer status
  const isHangoutHost = Array.isArray(user?.role) && user?.role.includes(USER_ROLES.HANGOUT_HOST);
  const isJourneyCaptain = Array.isArray(user?.role) && user?.role.includes(USER_ROLES.JOURNEY_CAPTAIN);
  const isOrganizerFlag = user?.isOrganizer === true;

  // Combined check
  const isOrganizer = isOrganizerFlag || isHangoutHost || isJourneyCaptain;

  if (!user) {
    return <Redirect href="/google-signin" />;
  }

  return (
    <>
      <Tabs
        screenOptions={({ route }) => ({
          tabBarIcon: ({ focused }) => {
            // Center search button
            // if (route.name === "search") {
            //   return (
            //     <View className="absolute bottom-1 ">
            //       <TouchableOpacity className="w-20 h-20 bg-midnightBlue rounded-full items-center justify-center border-4 border-white">
            //         <Ionicons name="search" size={28} color="#FFDE59" />
            //       </TouchableOpacity>
            //     </View>
            //   );
            // }

            return (
              <View className={`flex-col items-center justify-center w-20`}>
                {route.name === "home" ? (
                  <Ionicons
                    name={`${focused ? "home" : "home-outline"}`}
                    size={18}
                    color={focused ? "#2e2e2e " : "#6b7280"}
                  />
                ) : route.name === "hangouts" ? (
                  <MaterialIcons name={"group"} size={22} color={focused ? "#2e2e2e " : "#6b7280"} />
                ) : route.name === "profile" ? (
                  <MaterialCommunityIcons
                    name={`${focused ? "account-settings" : "account-settings-outline"}`}
                    size={22}
                    color={focused ? "#2e2e2e " : "#6b7280"}
                  />
                ) : route.name === "journeys" ? (
                  <MaterialCommunityIcons name="beach" size={22} color={focused ? "#2e2e2e " : "#6b7280"} />
                ) : (
                  route.name === "scan" && (
                    <Ionicons
                      name={`${focused ? "qr-code" : "qr-code-outline"}`}
                      size={22}
                      color={focused ? "#2e2e2e " : "#6b7280"}
                    />
                  )
                )}
                <Text className={`font-body ${focused ? "text-textColor font-bold" : "text-gray-500"} text-sm`}>
                  {toTitleCase(route.name)}
                </Text>
              </View>
            );
          },
          tabBarLabelPosition: "beside-icon",
          tabBarLabelStyle: { display: "none" },
          tabBarStyle: {
            backgroundColor: "white",
            height: Platform.OS === "ios" ? 90 : 65,
            justifyContent: "center",
            alignItems: "center",
          },
        })}
      >
        <Tabs.Screen name="home" options={{ title: "Home Screen", headerShown: false }} />
        <Tabs.Screen
          name="hangouts"
          options={{
            title: "Hangouts",
            headerShown: false,
            href: user?.isOrganizer ? null : "/hangouts",
          }}
        />
        <Tabs.Screen
          name="scan"
          options={{
            title: "Scan",
            headerShown: false,
            href: isOrganizer === false ? null : "/scan",
          }}
        />

        <Tabs.Screen
          name="journeys"
          options={{ title: "Journeys Screen", headerShown: false, href: user?.isOrganizer ? null : "/journeys" }}
        />

        <Tabs.Screen name="profile" options={{ title: "profile Screen", headerShown: false }} />
      </Tabs>
    </>
  );
};

export default TabsLayout;
