import React, { useEffect, useRef, useState } from "react";
import { View, ScrollView, Animated, Text, RefreshControl, TouchableOpacity } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { StatusBar } from "expo-status-bar";
import { LinearGradient } from "expo-linear-gradient";
import SearchBar from "../../components/HomePage/SearchBar";
import { useIsFocused } from "@react-navigation/native";
import FeaturedHangoutsList from "../../components/Hangouts/FeaturedHangoutsList";
import UpComingHangoutsList from "../../components/Hangouts/UpComingHangoutsList";
import HomePageHeader from "../../components/HomePage/HomePageHeader";
import { LoadMoreSkeleton } from "../../components/Hangouts/SkeletonLoaders";
import { useHangouts } from "../../hooks/useHangouts";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";

const HangoutsPage = () => {
  const [refreshing, setRefreshing] = useState(false);
  const { data, isLoading, error, refetch, fetchNextPage, hasNextPage, isFetchingNextPage, isFetching } = useHangouts({
    page: 1,
    limit: 10,
    filter: "popular",
  });

  const isFocused = useIsFocused();
  const scrollViewRef = useRef(null);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  const fadeAnim = useRef(new Animated.Value(0)).current;
  const translateYAnim = useRef(new Animated.Value(-50)).current;

  useEffect(() => {
    if (isFocused) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(translateYAnim, {
          toValue: 0,
          duration: 500,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      fadeAnim.setValue(0);
      translateYAnim.setValue(-50);
    }
  }, [isFocused]);

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await refetch();
    } catch (error) {
      console.error("Error refreshing:", error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleLoadMore = async () => {
    if (hasNextPage && !isFetchingNextPage && !isLoadingMore) {
      try {
        setIsLoadingMore(true);
        await fetchNextPage();
      } catch (error) {
        console.error("Error loading more:", error);
      } finally {
        setIsLoadingMore(false);
      }
    }
  };

  const handleScroll = ({
    nativeEvent,
  }: {
    nativeEvent: {
      layoutMeasurement: { height: number };
      contentOffset: { y: number };
      contentSize: { height: number };
    };
  }) => {
    const { layoutMeasurement, contentOffset, contentSize } = nativeEvent;
    const paddingToBottom = 120;
    const isCloseToBottom = layoutMeasurement.height + contentOffset.y >= contentSize.height - paddingToBottom;

    if (isCloseToBottom && !isFetchingNextPage && !isLoadingMore) {
      handleLoadMore();
    }
  };

  if (error) return <Text>Error: {error.message}</Text>;

  // Flatten the pages data
  const allHangouts = data?.pages.flatMap((page) => page.data) || [];

  // Split Hangouts into featured and regular
  const featuredHangouts = allHangouts.slice(0, 3) || [];
  const regularHangouts = featuredHangouts.length > 1 ? allHangouts.slice(1, allHangouts.length) : [];

  // Get total pages and current page from the latest response
  const totalPages = data?.pages[data.pages.length - 1]?.pagination?.pages || 0;
  const currentPage = data?.pages[data.pages.length - 1]?.pagination?.page || 0;

  return (
    <SafeAreaView className="flex-1 bg-white">
      {/* Animated Header: Fades & Slides Down */}
      <View className="p-4 pb-0">
        <HomePageHeader />
        <View className="mt-4 flex-row items-center">
          <TouchableOpacity className="flex-1" onPress={() => router.push("/hangouts/search")} activeOpacity={0.7}>
            <SearchBar
              showTitle={false}
              border={true}
              placeholder="Discover fun meetups this weekend"
              disabled={true}
            />
          </TouchableOpacity>
          <TouchableOpacity
            className="ml-3 border-[1px] border-slate-200 p-3 rounded-xl"
            onPress={() => router.push("/hangouts/map")}
          >
            <Ionicons name="location-outline" size={22} color="black" />
          </TouchableOpacity>
        </View>
      </View>
      {/* <Animated.View
        style={{
          opacity: fadeAnim,
          transform: [{ translateY: translateYAnim }],
        }}
      >
        <LinearGradient
          colors={["#0A0F1D", "#2B2B2B"]}
          className="h-[200px] rounded-b-2xl p-4 pt-16 flex-col justify-between"
        >
          <HomePageHeader darkBackground={true} />
          <View className="flex-row items-center">
            <View className="flex-1">
              <SearchBar showTitle={false} />
            </View>
            <TouchableOpacity
              className="ml-3 bg-white/20 backdrop-blur-md p-3 rounded-full"
              onPress={() => router.push("/hangouts/map")}
            >
              <Ionicons name="map" size={22} color="white" />
            </TouchableOpacity>
          </View>
        </LinearGradient>
      </Animated.View> */}

      <ScrollView
        ref={scrollViewRef}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 30 }}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} tintColor="#4A6FFF" colors={["#4A6FFF"]} />
        }
        onScroll={handleScroll}
        scrollEventThrottle={200}
      >
        <View className="p-4">
          {/* Featured Hangouts - Horizontal Scrolling */}
          <FeaturedHangoutsList hangouts={featuredHangouts} isLoading={isLoading && !isFetchingNextPage} />

          {/* Regular Hangouts List */}
          <UpComingHangoutsList hangouts={regularHangouts} isLoading={isLoading && !isFetchingNextPage} />

          {/* Loading skeleton for infinite scroll */}
          {(isFetchingNextPage || isLoadingMore) && <LoadMoreSkeleton />}

          {/* End of results message */}
          {!hasNextPage && allHangouts.length > 0 && currentPage >= totalPages && (
            <View className="py-6 items-center">
              <View className="w-12 h-1 bg-gray-200 rounded-full mb-4" />
              <Text className="text-gray-500 text-center font-body">You've seen all hangouts</Text>
            </View>
          )}

          {/* No results message */}
          {!isLoading && !isFetching && allHangouts.length === 0 && (
            <View className="py-12 items-center">
              <View className="w-16 h-16 bg-gray-100 rounded-full items-center justify-center mb-4">
                <Ionicons name="calendar-outline" size={32} color="#9CA3AF" />
              </View>
              <Text className="text-gray-500 text-center font-body-medium">No hangouts found</Text>
              <Text className="text-gray-400 text-center text-sm mt-1 font-body">Try changing your filters</Text>
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default HangoutsPage;
