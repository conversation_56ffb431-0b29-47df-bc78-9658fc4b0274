import { View, Text, RefreshControl, TouchableOpacity, Image, ScrollView, Dimensions } from "react-native";
import React, { useState } from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { StatusBar } from "expo-status-bar";
import HomePageHeader from "../../components/HomePage/HomePageHeader";
import { useHangouts } from "../../hooks/useHangouts";
import { useJourneys } from "../../hooks/useJourneys";
import { router } from "expo-router";
import Ionicons from "@expo/vector-icons/Ionicons";
import { useAppSelector } from "../../reduxStore/hooks";
import { RootState } from "../../reduxStore/store";
import OrganizerHomeScreen from "../../components/Organizer/Organizer";
import NotificationPrompt from "../../components/Notification/NotificationPrompt";
import AutoLocationHandler from "../../components/Location/AutoLocationHandler";

const { width } = Dimensions.get("window");
const cardWidth = width * 0.75;

type IconName =
  | "people-outline"
  | "compass-outline"
  | "restaurant-outline"
  | "color-palette-outline"
  | "beer-outline"
  | "football-outline"
  | "sunny-outline"
  | "musical-notes-outline";

interface CategoryItem {
  title: string;
  icon: IconName;
  color: string;
  route: string;
}

interface MixedContentItem {
  _id: string;
  title: string;
  type: "hangout" | "journey";
  location?: {
    address: string;
  };
  images?: string[];
  featuredImage?: string;
  coverImage?: string;
  date?: string;
  startDate?: string;
  participants?: any[];
  destination?: string;
  destinationCity?: string;
  basePrice?: number;
  category?: string;
  difficultyLevel?: string;
  price?: number;
}

const HomePage = () => {
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState("trending");
  const { user } = useAppSelector((state: RootState) => state.user);

  // API hooks
  const {
    data: hangoutsData,
    isLoading: isHangoutsLoading,
    refetch: refetchHangouts,
  } = useHangouts({
    page: 1,
    limit: 3,
    filter: "popular",
  });

  const { featured: featuredJourneys, upcoming: upcomingJourneys } = useJourneys();

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await Promise.all([refetchHangouts(), featuredJourneys.refetch(), upcomingJourneys.refetch()]);
    } catch (error) {
      console.error("Error refreshing:", error);
    } finally {
      setRefreshing(false);
    }
  };

  const renderTopSection = () => (
    <View className="mb-5">
      <HomePageHeader />
      {/* <View className="mt-3">
        <SearchBar border={true} title="Find your next adventure..." />
      </View> */}
    </View>
  );

  const renderQuickAccess = () => (
    <View className="mb-6">
      <Text className="font-heading text-headline-500 mb-3">Quick Access</Text>
      <View className="flex-row space-x-3">
        <TouchableOpacity
          className="bg-[#FFF8E1] rounded-xl p-4 flex-1 justify-between"
          style={{ height: 100, borderLeftWidth: 3, borderLeftColor: "#FFDE59" }}
          onPress={() => router.push("/hangouts/map")}
        >
          <View className="flex-row justify-between items-center">
            <Text className="font-heading text-[15px] texttextColor">Nearby</Text>
            <View className="bg-[#FFDE59] rounded-full w-8 h-8 items-center justify-center">
              <Ionicons name="navigate-outline" size={18} color="#222" />
            </View>
          </View>
          <Text className="text-xs text-gray-700 mt-1">Find hangouts around you</Text>
        </TouchableOpacity>

        <TouchableOpacity
          className="bg-[#FFEDED] rounded-xl p-4 flex-1 justify-between"
          style={{ height: 100, borderLeftWidth: 3, borderLeftColor: "#D72638" }}
          onPress={() => router.push("/journeys")}
        >
          <View className="flex-row justify-between items-center">
            <Text className="font-heading text-[15px] texttextColor">Weekend</Text>
            <View className="bg-[#D72638] rounded-full w-8 h-8 items-center justify-center">
              <Ionicons name="calendar-outline" size={18} color="white" />
            </View>
          </View>
          <Text className="text-xs text-gray-700 mt-1">Plan a weekend journey</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderCategories = () => {
    const categories: CategoryItem[] = [
      { title: "Social", icon: "people-outline", color: "#D72638", route: "/hangouts/search?category=social" },
      { title: "Adventure", icon: "compass-outline", color: "#4A6FFF", route: "/journeys/search?category=adventure" },
      { title: "Food", icon: "restaurant-outline", color: "#FF9F1C", route: "/hangouts/search?category=food" },
      { title: "Nightlife", icon: "beer-outline", color: "#7209B7", route: "/hangouts/search?category=nightlife" },
      { title: "Sports", icon: "football-outline", color: "#38B000", route: "/hangouts/search?category=sports" },
      { title: "Beach", icon: "sunny-outline", color: "#FFDE59", route: "/journeys/search?category=beach" },
      {
        title: "Cultural",
        icon: "color-palette-outline",
        color: "#9E0059",
        route: "/journeys/search?category=cultural",
      },
      { title: "Music", icon: "musical-notes-outline", color: "#118AB2", route: "/hangouts/search?category=music" },
    ];

    return (
      <View className="mb-6">
        <Text className="font-heading text-headline-500 mb-3">Categories</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View className="flex-row">
            {categories.map((item, index) => (
              <TouchableOpacity key={index} className="items-center mr-4 w-16" onPress={() => router.push(item.route)}>
                <View
                  className="w-12 h-12 rounded-full mb-1 items-center justify-center"
                  style={{ backgroundColor: `${item.color}15` }}
                >
                  <Ionicons name={item.icon} size={20} color={item.color} />
                </View>
                <Text className="text-xs font-body-medium text-center text-gray-700" numberOfLines={1}>
                  {item.title}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
      </View>
    );
  };

  const renderTrendingSection = () => {
    const tabs = [
      { id: "trending", title: "Trending" },
      { id: "thisWeek", title: "This Week" },
      { id: "new", title: "New" },
      { id: "recommended", title: "For You" },
    ];

    return (
      <View className="mb-5">
        <View className="flex-row items-center justify-between mb-3">
          <Text className="font-heading text-headline-500">Discover</Text>
          {/* <View className="flex-row">
            {tabs.map((tab) => (
              <TouchableOpacity
                key={tab.id}
                className={`px-3 py-1 rounded-full mr-2 ${activeTab === tab.id ? "bg-midnightBlue" : "bg-gray-100"}`}
                onPress={() => setActiveTab(tab.id)}
              >
                <Text className={`font-body-medium text-xs ${activeTab === tab.id ? "text-white" : "text-gray-600"}`}>
                  {tab.title}
                </Text>
              </TouchableOpacity>
            ))}
          </View> */}
        </View>
        {renderHorizontalCards()}
      </View>
    );
  };

  const renderHorizontalCards = () => {
    // Combine hangouts and journeys data
    const hangouts = hangoutsData?.pages[0]?.data || [];
    const journeys = [...(featuredJourneys.data || []), ...(upcomingJourneys.data || [])];

    // filter out duplicate journeys
    const uniqueJourneys = journeys.filter((item, index, self) => {
      return index === self.findIndex((t) => t._id === item._id);
    });

    // Transform data to common format
    const formattedHangouts: MixedContentItem[] = hangouts.map((item) => ({
      ...item,
      type: "hangout",
      images: item.images || [],
      featuredImage: item.featuredImage,
      price: item.price,
    }));

    const formattedJourneys: MixedContentItem[] = uniqueJourneys.map((item) => ({
      ...item,
      type: "journey",
      coverImage: item.coverImage,
    }));

    // Combine and limit content
    const mixedContent = [...formattedHangouts, ...formattedJourneys].slice(0, 6);

    if (isHangoutsLoading || featuredJourneys.isLoading) {
      return (
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {[1, 2, 3].map((_, index) => (
            <View key={index} className="h-[220px] w-[280px] rounded-xl bg-gray-100 animate-pulse mr-4" />
          ))}
        </ScrollView>
      );
    }

    if (mixedContent.length === 0) {
      return (
        <View className="bg-[#FFF8E1] rounded-xl p-6 items-center border-[1px] border-[#FFDE59]">
          <View className="bg-[#FFDE59] rounded-full p-3 mb-3">
            <Ionicons name="compass-outline" size={24} color="#222" />
          </View>
          <Text className="font-heading text-headline-400 text-center mb-1 text-gray-800">Nothing to discover yet</Text>
          <Text className="font-body text-xs text-gray-700 text-center">Check back later for new adventures!</Text>
        </View>
      );
    }

    return (
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {mixedContent.map((item, index) => (
          <TouchableOpacity
            key={index}
            className="mr-4"
            style={{ width: cardWidth }}
            onPress={() => router.push(item.type === "hangout" ? `/hangouts/${item._id}` : `/journeys/${item._id}`)}
          >
            <View className="rounded-xl overflow-hidden shadow-sm" style={{ height: 240 }}>
              <Image
                source={{ uri: item.type === "hangout" ? item.featuredImage || item.images?.[0] : item.coverImage }}
                style={{ width: cardWidth, height: 140 }}
                resizeMode="cover"
              />
              <View
                className="absolute top-3 right-3 px-2 py-1 rounded-full"
                style={{ backgroundColor: item.type === "hangout" ? "#D72638" : "#FFDE59" }}
              >
                <Text
                  className={`text-[10px] font-body-medium ${item.type === "hangout" ? "text-white" : "texttextColor"}`}
                >
                  {item.type === "hangout" ? "Hangout" : "Journey"}
                </Text>
              </View>
              <View className="p-3">
                <Text className="font-heading text-body-200 text-textColor" numberOfLines={1}>
                  {item.title}
                </Text>
                <View className="flex-row items-center justify-between mt-1">
                  <View className="flex-row items-center">
                    <Ionicons name="location-outline" size={12} color="#6B7280" />
                    <Text className="text-gray-500 text-xs ml-1" numberOfLines={1}>
                      {item.type === "hangout"
                        ? item.location?.address?.split(",")[0] || "Location unavailable"
                        : item.destinationCity || item.destination?.split(",")[0] || "Location unavailable"}
                    </Text>
                  </View>
                  {/* <View className="bg-gray-100 rounded-full px-2 py-0.5">
                    <Text className="text-gray-700 text-[10px]">
                      {item.type === "hangout" ? item.category || "Social" : item.difficultyLevel || "Easy"}
                    </Text>
                  </View> */}
                </View>
                <View className="flex-row items-center justify-between mt-2">
                  <View className="flex-row items-center">
                    <Ionicons name="calendar-outline" size={12} color={"#000"} />
                    <Text className={`text-xs ml-1 font-body-medium texttextColor`}>
                      {item.type === "hangout" && item.date
                        ? new Date(item.date).toLocaleDateString(undefined, { month: "short", day: "numeric" })
                        : item.type === "journey" && item.startDate
                        ? new Date(item.startDate).toLocaleDateString(undefined, { month: "short", day: "numeric" })
                        : "Soon"}
                    </Text>
                  </View>
                  <View className="flex-row items-center">
                    {item.type === "hangout" ? (
                      <Text className="text-xs font-body-medium text-textColor">
                        {item.participants?.length || 0} going
                      </Text>
                    ) : (
                      <Text className="text-xs ml-1 font-heading text-textColor">₹{item.basePrice || 0}</Text>
                    )}
                  </View>
                </View>
              </View>
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>
    );
  };

  const renderFeaturedContent = () => {
    // Combine hangouts and journeys data
    const hangouts = hangoutsData?.pages[0]?.data || [];
    const journeys = [...(featuredJourneys.data || []), ...(upcomingJourneys.data || [])];

    // filter out duplicate journeys
    const uniqueJourneys = journeys.filter((item, index, self) => {
      return index === self.findIndex((t) => t._id === item._id);
    });

    // Transform data to common format
    const formattedHangouts: MixedContentItem[] = hangouts.map((item) => ({
      ...item,
      type: "hangout",
      images: item.images || [],
      featuredImage: item.featuredImage,
      price: item.price,
    }));

    const formattedJourneys: MixedContentItem[] = uniqueJourneys.map((item) => ({
      ...item,
      type: "journey",
      coverImage: item.coverImage,
    }));

    // Combine and limit content - display different items than horizontal cards
    const mixedContent = [...formattedJourneys, ...formattedHangouts].slice(0, 3);

    if (isHangoutsLoading || featuredJourneys.isLoading) {
      return (
        <View className="space-y-3">
          {[1, 2].map((_, index) => (
            <View key={index} className="h-[100px] rounded-xl bg-gray-100 animate-pulse flex-row overflow-hidden">
              <View className="w-[100px] h-full bg-gray-200" />
              <View className="p-3 flex-1">
                <View className="h-4 bg-gray-200 rounded w-3/4 mb-2" />
                <View className="h-3 bg-gray-200 rounded w-1/2 mb-2" />
                <View className="h-3 bg-gray-200 rounded w-1/4" />
              </View>
            </View>
          ))}
        </View>
      );
    }

    if (mixedContent.length === 0) return null;

    return (
      <View className="mb-6">
        <Text className="font-heading text-headline-500 mb-3">Recommended For You</Text>
        <View className="space-y-3">
          {mixedContent.map((item, index) => (
            <TouchableOpacity
              key={index}
              className="flex-row h-[100px] rounded-xl overflow-hidden border border-slate-100"
              onPress={() => router.push(item.type === "hangout" ? `/hangouts/${item._id}` : `/journeys/${item._id}`)}
            >
              <Image
                source={{ uri: item.type === "hangout" ? item.featuredImage || item.images?.[0] : item.coverImage }}
                style={{ width: 100, height: 100 }}
                resizeMode="cover"
              />
              <View className="p-3 flex-1 justify-between">
                <View>
                  <Text className="font-heading text-[14px] text-textColor" numberOfLines={1}>
                    {item.title}
                  </Text>
                  <View className="flex-row items-center mt-1">
                    <Ionicons
                      name={item.type === "hangout" ? "people-outline" : "compass-outline"}
                      size={12}
                      color={item.type === "hangout" ? "#D72638" : "#444"}
                    />
                    <Text className="text-gray-500 text-xs ml-1" numberOfLines={1}>
                      {item.type === "hangout" ? item.category || "Social Hangout" : item.difficultyLevel || "Journey"}
                    </Text>
                  </View>
                </View>
                <View className="flex-row items-center justify-between">
                  <View className="flex-row items-center">
                    <Ionicons name="calendar-outline" size={12} color="#6B7280" />
                    <Text className="text-gray-500 text-xs ml-1">
                      {item.type === "hangout" && item.date
                        ? new Date(item.date).toLocaleDateString(undefined, { month: "short", day: "numeric" })
                        : item.type === "journey" && item.startDate
                        ? new Date(item.startDate).toLocaleDateString(undefined, { month: "short", day: "numeric" })
                        : "Soon"}
                    </Text>
                  </View>
                  <Text
                    className={`font-body-medium text-xs ${
                      item.type === "hangout" ? "text-[#D72638]" : "texttextColor"
                    }`}
                  >
                    {item.type === "hangout" ? (item.price ? `₹${item.price}` : "Free") : `₹${item.basePrice || 0}`}
                  </Text>
                </View>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  const renderInviteFriends = () => (
    <TouchableOpacity
      className="rounded-xl p-4 flex-row items-center justify-between mb-6"
      style={{
        backgroundColor: "#FFF8E1",
        borderWidth: 1,
        borderColor: "#FFDE59",
        borderLeftWidth: 4,
        borderLeftColor: "#FFDE59",
      }}
      onPress={() => router.push("/settings/invite")}
    >
      <View className="flex-1">
        <Text className="font-heading texttextColor text-[16px] mb-1">Better with friends</Text>
        <Text className="text-xs text-gray-700">Invite friends and earn rewards!</Text>
      </View>
      <View className="bg-[#FFDE59] p-2 rounded-full">
        <Ionicons name="share-social-outline" size={20} color="#222" />
      </View>
    </TouchableOpacity>
  );

  if (user?.isOrganizer) {
    return (
      <>
        <OrganizerHomeScreen />
      </>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: "white" }} edges={["top", "left", "right"]}>
      <StatusBar style="dark" />
      <ScrollView
        contentContainerStyle={{ padding: 16, paddingBottom: 32 }}
        showsHorizontalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} tintColor="#D72638" colors={["#D72638"]} />
        }
      >
        {renderTopSection()}
        {renderQuickAccess()}
        {renderCategories()}
        {renderTrendingSection()}
        {renderFeaturedContent()}
        {renderInviteFriends()}
        <NotificationPrompt />
      </ScrollView>

      {/* Auto Location Handler - shows modal for location permission */}
      <AutoLocationHandler
        onLocationSet={(success) => {
          if (success) {
            console.log("Location set successfully");
          } else {
            console.log("Location setting skipped or failed");
          }
        }}
      />
    </SafeAreaView>
  );
};

export default HomePage;
