import React, { useEffect, useRef, useState } from "react";
import { View, ScrollView, Animated, Text, RefreshControl, TouchableOpacity } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { StatusBar } from "expo-status-bar";
import { LinearGradient } from "expo-linear-gradient";
import SearchBar from "../../components/HomePage/SearchBar";
import { useIsFocused } from "@react-navigation/native";
import HomePageHeader from "../../components/HomePage/HomePageHeader";
import FeaturedJourneysList from "../../components/Journeys/FeaturedJourneysList";
import UpcomingJourneysList from "../../components/Journeys/UpcomingJourneysList";
import { useJourneys } from "../../hooks/useJourneys";
import MaterialCommunityIcons from "@expo/vector-icons/MaterialCommunityIcons";
import { router } from "expo-router";

const JourneysPage = () => {
  const [refreshing, setRefreshing] = useState(false);
  const { featured, upcoming } = useJourneys();
  const isFocused = useIsFocused();

  const fadeAnim = useRef(new Animated.Value(0)).current;
  const translateYAnim = useRef(new Animated.Value(-50)).current;

  useEffect(() => {
    if (isFocused) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(translateYAnim, {
          toValue: 0,
          duration: 500,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      fadeAnim.setValue(0);
      translateYAnim.setValue(-50);
    }
  }, [isFocused]);

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      if (featured.refetch && upcoming.refetch) {
        await Promise.all([featured.refetch(), upcoming.refetch()]);
      }
    } catch (error) {
      console.error("Error refreshing:", error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleLoadMore = async () => {
    if (upcoming.hasNextPage && !upcoming.isFetchingNextPage) {
      try {
        await upcoming.fetchNextPage();
      } catch (error) {
        console.error("Error loading more:", error);
      }
    }
  };

  const handleScroll = ({
    nativeEvent,
  }: {
    nativeEvent: {
      layoutMeasurement: { height: number };
      contentOffset: { y: number };
      contentSize: { height: number };
    };
  }) => {
    const { layoutMeasurement, contentOffset, contentSize } = nativeEvent;
    const paddingToBottom = 100;
    const isCloseToBottom = layoutMeasurement.height + contentOffset.y >= contentSize.height - paddingToBottom;

    if (isCloseToBottom && !upcoming.isFetchingNextPage) {
      handleLoadMore();
    }
  };

  if (featured.error || upcoming.error) {
    return (
      <View className="flex-1 items-center justify-center p-4">
        <Text className="text-headline-500 text-center text-red-600">
          Error loading journeys. Please try again later.
        </Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: "white" }}>
      {/* {isFocused && <StatusBar style="light" animated backgroundColor="transparent" translucent />} */}

      <View className="p-4 pb-0">
        <HomePageHeader />
        <View className="mt-4 flex-row items-center">
          <TouchableOpacity
            className="flex-1"
            onPress={() => {
              router.push("/journeys/search");
            }}
            activeOpacity={0.7}
          >
            <View pointerEvents="none">
              <SearchBar showTitle={false} border={true} placeholder="Search for your next adventure..." />
            </View>
          </TouchableOpacity>
          {/* <TouchableOpacity className="ml-3 border-[1px] border-slate-200 p-3 rounded-xl">
            <MaterialCommunityIcons name="filter" size={22} color="black" />
          </TouchableOpacity> */}
        </View>
      </View>

      {/* <Animated.View
        style={{
          opacity: fadeAnim,
          transform: [{ translateY: translateYAnim }],
        }}
      >
        <LinearGradient
          colors={["#0A0F1D", "#2B2B2B"]}
          className="h-[200px] rounded-b-2xl p-4 pt-16 flex-col justify-between"
        >
          <HomePageHeader darkBackground={true} />
          <SearchBar showTitle={false} />
        </LinearGradient>
      </Animated.View> */}

      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 20 }}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} tintColor="#0A0F1D" colors={["#0A0F1D"]} />
        }
        onScroll={handleScroll}
        scrollEventThrottle={200}
      >
        <View className="p-4">
          <FeaturedJourneysList journeys={featured.data} isLoading={featured.isLoading} />
          <UpcomingJourneysList journeys={upcoming.data} isLoading={upcoming.isLoading} />
          {upcoming.isFetchingNextPage && (
            <View className="items-center py-4">
              <Text className="text-gray-500">Loading more journeys...</Text>
            </View>
          )}
          {!upcoming.hasNextPage && upcoming.data.length > 0 && (
            <Text className="text-center text-gray-500 py-4">No more journeys to load</Text>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default JourneysPage;
