import { View, Text, TouchableOpacity, ScrollView, Image } from "react-native";
import React, { useState } from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { useAppSelector, useAppDispatch } from "../../reduxStore/hooks";
import { RootState } from "../../reduxStore/store";
import { useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import ProfileInfo from "../../components/Profile/ProfileInfo";
import StatsSection from "../../components/Profile/StatsSection";
import InterestsSection from "../../components/Profile/InterestsSection";
import AchievementsSection from "../../components/Profile/AchievementsSection";
import ActivityHistorySection from "../../components/Profile/ActivityHistorySection";
import BadgesSection from "../../components/Profile/BadgesSection";
import { getUserProfile } from "../../api/authService";

const ProfilePage = () => {
  const { user } = useAppSelector((state: RootState) => state.user);
  const router = useRouter();
  const dispatch = useAppDispatch();
  const [showBadgeTooltip, setShowBadgeTooltip] = useState(false);

  // Fetch user profile on component mount
  React.useEffect(() => {
    const fetchProfile = async () => {
      try {
        await getUserProfile(dispatch);
      } catch (error) {
        console.error("Error fetching profile:", error);
      }
    };

    fetchProfile();
  }, []);

  const handleEditProfile = () => {
    router.push("settings/profile-edit");
  };

  const handleSettingsPress = () => {
    router.push("/settings");
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <ScrollView className="flex-1">
        {/* Cover Photo */}
        <View className="h-40 relative">
          {!user?.profile?.coverPic ? (
            <View className="absolute top-0 left-0 right-0 bottom-0 bg-slate-100 items-center justify-center">
              <Ionicons name="image-outline" size={40} color="#9ca3af" />
            </View>
          ) : (
            <Image source={{ uri: user?.profile?.coverPic }} className="w-full h-full" />
          )}
          <View className="absolute top-4 right-4 flex-row">
            <TouchableOpacity onPress={handleEditProfile} className="bg-white/80 rounded-full p-2 mr-2">
              <Ionicons name="pencil" size={20} color="#333" />
            </TouchableOpacity>
            <TouchableOpacity onPress={handleSettingsPress} className="bg-white/80 rounded-full p-2">
              <Ionicons name="settings-outline" size={20} color="#333" />
            </TouchableOpacity>
          </View>
        </View>

        <View className="px-4 -mt-16">
          {/* Profile Info */}
          <ProfileInfo user={user} />

          {/* Stats Section - Always show with default values if not available */}
          <StatsSection
            stats={
              user?.profile?.stats || {
                totalHangoutsAttended: 0,
                totalJourneysAttended: 0,
                totalPoints: 0,
                level: 1,
                experience: 0,
                experienceToNextLevel: 100,
              }
            }
          />

          {/* Badges Section - Show placeholder if none */}
          <View className="mt-6 bg-white rounded-xl p-4 border border-slate-200">
            <View className="flex-row items-center mb-3">
              <Text className="text-lg font-bold mr-2">Badges</Text>
              <TouchableOpacity onPress={() => setShowBadgeTooltip(true)} accessibilityLabel="How to earn badges">
                <Ionicons name="information-circle-outline" size={18} color="#64748b" />
              </TouchableOpacity>
            </View>

            {user?.profile?.preferences?.showBadges !== false &&
              (user?.profile?.badges && user?.profile?.badges?.length > 0 ? (
                <BadgesSection badges={user?.profile?.badges} />
              ) : (
                <Text className="text-gray-500 italic">Earn badges by vibing, exploring, and hanging out.</Text>
              ))}

            {/* Tooltip Modal */}
            {showBadgeTooltip && (
              <View
                style={{
                  position: "absolute",
                  top: 40,
                  left: 20,
                  right: 20,
                  backgroundColor: "white",
                  borderRadius: 8,
                  padding: 16,
                  borderWidth: 1,
                  borderColor: "#e5e7eb",
                  shadowColor: "#000",
                  shadowOpacity: 0.1,
                  shadowRadius: 8,
                  elevation: 4,
                  zIndex: 10,
                }}
              >
                <Text className="text-base text-gray-700 mb-2 font-semibold">How to earn badges?</Text>
                <Text className="text-gray-600 mb-3">Earn badges by vibing, exploring, and hanging out.</Text>
                <TouchableOpacity onPress={() => setShowBadgeTooltip(false)} className="self-end mt-2">
                  <Text className="text-blue-500 font-medium">Got it</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>

          {/* Achievements Section - Show placeholder if none */}
          {user?.profile?.achievements && user?.profile?.achievements?.length > 0 ? (
            <AchievementsSection achievements={user?.profile?.achievements} />
          ) : (
            <View className="mt-6 bg-white rounded-xl p-4 shadow-sm border border-slate-200">
              <Text className="text-lg font-bold mb-3">Achievements</Text>
              <Text className="text-gray-500 italic">Every connection counts—start earning achievements now.</Text>
            </View>
          )}

          {/* Interests Section - Always show */}
          <InterestsSection
            hangoutInterests={user?.profile?.hangoutInterests || {}}
            journeyInterests={user?.profile?.journeyInterests || {}}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default ProfilePage;
