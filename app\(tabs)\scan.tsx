import React from "react";
import { View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Redirect } from "expo-router";
import QRScanner from "../../components/Organizer/QRScanner";
import { useAppSelector } from "../../reduxStore/hooks";
import { RootState } from "../../reduxStore/store";
import { USER_ROLES } from "../../constants/AuthConstants";
import PageHeader from "../../components/Common/PageHeader";

const ScanScreen = () => {
  const { user } = useAppSelector((state: RootState) => state.user);

  // Check if user is an organizer
  const isOrganizer =
    user?.isOrganizer ||
    user?.role?.includes(USER_ROLES.HANGOUT_HOST) ||
    user?.role?.includes(USER_ROLES.JOURNEY_CAPTAIN);

  // Redirect non-organizers
  if (!isOrganizer) {
    return <Redirect href="/(tabs)" />;
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: "white" }} edges={["top", "left", "right"]}>
      <View className="flex-1">
        <PageHeader title="Check-in Scanner" color="black" showIcon={false} />
        <QRScanner />
      </View>
    </SafeAreaView>
  );
};

export default ScanScreen;
