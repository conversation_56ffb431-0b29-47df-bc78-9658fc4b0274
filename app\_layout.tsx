import "react-native-get-random-values";
import React, { useEffect } from "react";
import { Stack } from "expo-router";
import { Provider } from "react-redux";
import { store } from "../reduxStore/store";
import { useFonts } from "expo-font";
import { FONTS } from "../lib/fonts";
import * as SplashScreen from "expo-splash-screen";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ToastProvider, useToast } from "../components/Common/ToastProvider";
import { setToastRef } from "../lib/utils/showToast";
import { useNotificationHandler } from "../hooks/useNotificationHandler";
import { AuthProvider } from "../contexts/AuthContext";
import AuthNavigationHandler from "../components/Navigation/AuthNavigationHandler";

// Keep the splash screen visible while we fetch resources
SplashScreen.preventAutoHideAsync();
const queryClient = new QueryClient();

// This component connects our global showToast function to the ToastProvider
const ToastConnector = () => {
  const { showToast } = useToast();

  useEffect(() => {
    setToastRef(showToast);
  }, [showToast]);

  return null;
};

const RootLayout = () => {
  const [fontsLoaded] = useFonts(FONTS);

  React.useEffect(() => {
    if (fontsLoaded) {
      // Hide splash screen once fonts are loaded
      SplashScreen.hideAsync();
    }
  }, [fontsLoaded]);

  if (!fontsLoaded) {
    return null;
  }

  // Component to handle notifications
  const NotificationHandler = () => {
    useNotificationHandler();
    return null;
  };

  return (
    <Provider store={store}>
      <QueryClientProvider client={queryClient}>
        <ToastProvider>
          <AuthProvider>
            <ToastConnector />
            <NotificationHandler />
            <AuthNavigationHandler />
            <Stack screenOptions={{ headerShown: false }}>
              <Stack.Screen name="index" options={{ headerShown: false }} />
              <Stack.Screen name="(auth)" options={{ headerShown: false }} />
              <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
              <Stack.Screen name="(reels)" options={{ headerShown: false }} />
              <Stack.Screen name="(notifications)" options={{ headerShown: false }} />
              <Stack.Screen name="hangouts" options={{ headerShown: false }} />
              <Stack.Screen name="journeys" options={{ headerShown: false }} />
              <Stack.Screen name="onboarding" options={{ headerShown: false }} />
              <Stack.Screen name="settings" options={{ headerShown: false }} />
              <Stack.Screen name="organizer" options={{ headerShown: false }} />
            </Stack>
          </AuthProvider>
        </ToastProvider>
      </QueryClientProvider>
    </Provider>
  );
};

export default RootLayout;
