import { View, StyleSheet } from "react-native";
import React, { useState, useEffect } from "react";
import { Stack, useLocalSearchParams } from "expo-router";
import { useIsFocused } from "@react-navigation/native";
import HangoutDetails from "../../components/Hangouts/HangoutDetails";
import { StatusBar } from "expo-status-bar";

const HangoutDetailsScreen = () => {
  const { id } = useLocalSearchParams();
  const isFocused = useIsFocused();
  const [statusBarStyle, setStatusBarStyle] = useState<"light" | "dark">("light");

  // Update status bar when screen comes into focus
  useEffect(() => {
    if (isFocused) {
      // Give time for animation to complete
      const timer = setTimeout(() => {
        setStatusBarStyle("light");
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [isFocused]);

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: false,
          presentation: "modal",
          animation: "slide_from_bottom",
          gestureEnabled: true,
          animationDuration: 250,
        }}
      />
      <View style={styles.container}>
        {isFocused && <StatusBar style={statusBarStyle} animated backgroundColor="transparent" translucent />}
        <HangoutDetails hangoutId={id as string} />
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000",
  },
});

export default HangoutDetailsScreen;
