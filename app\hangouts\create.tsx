// Main container for the hangout creation process
import React from "react";
import { View } from "react-native";
import CreateHangoutForm from "../../components/Hangouts/CreateHangout/CreateHangoutForm";
import { Stack } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import PageHeader from "../../components/Common/PageHeader";

const CreateHangoutScreen = () => {
  return (
    <>
      <Stack.Screen
        options={{
          headerShown: false,
          presentation: "card",
        }}
      />
      <SafeAreaView className="flex-1 bg-white">
        <PageHeader title="Create Hangout" color="black" showIcon={false} />
        <CreateHangoutForm />
      </SafeAreaView>
    </>
  );
};

export default CreateHangoutScreen;
