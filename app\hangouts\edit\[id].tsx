import React from "react";
import { View, ScrollView, Text } from "react-native";
import { Stack, useLocalSearchParams } from "expo-router";
import EditHangoutForm from "../../../components/Hangouts/EditHangout/EditHangoutForm";
import { SafeAreaView } from "react-native-safe-area-context";
import PageHeader from "../../../components/Common/PageHeader";

export default function EditHangoutScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();

  if (!id) {
    return (
      <View className="flex-1 justify-center items-center">
        <Text>Invalid hangout ID</Text>
      </View>
    );
  }

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: false,
          presentation: "card",
        }}
      />
      <SafeAreaView className="flex-1 bg-white">
        <PageHeader title="Edit Hangout" color="black" showIcon={false} />
        <EditHangoutForm hangoutId={id} />
      </SafeAreaView>
    </>
  );
}
