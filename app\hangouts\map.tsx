import React from "react";
import { View } from "react-native";
import HangoutMapSearch from "../../components/Hangouts/HangoutMapSearch";
import { Stack, useLocalSearchParams } from "expo-router";
import { HangoutFilters } from "../types/hangout";

export default function HangoutMapPage() {
  const params = useLocalSearchParams<{
    category?: string;
    subcategory?: string;
    filter?: string;
    search?: string;
  }>();

  // Convert URL params to filters
  const initialFilters: HangoutFilters = {
    filter: "popular", // Set default filter to popular
  };

  if (params.category) {
    initialFilters.category = params.category;
  }

  if (params.subcategory) {
    initialFilters.subcategory = params.subcategory;
  }

  if (params.filter) {
    initialFilters.filter = params.filter;
  }

  if (params.search) {
    initialFilters.search = params.search;
  }

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: false,
          presentation: "modal",
          animation: "slide_from_bottom",
        }}
      />
      <View className="flex-1">
        <HangoutMapSearch initialFilters={initialFilters} />
      </View>
    </>
  );
}
