import { View, ImageBackground } from "react-native";
import React, { useState, useEffect } from "react";
import OnBoardingStep from "../components/OnBoarding/OnBoardingStep";
import { ONBOARDING_STEPS_INFO } from "../constants/OnBoarding/OnBoardingConstants";
import { SafeAreaView } from "react-native-safe-area-context";
import PrimaryButton from "../components/Buttons/PrimaryButton";
import { useRouter } from "expo-router";
import { StatusBar } from "expo-status-bar";
import { useAuth, markOnboardingCompleted } from "../contexts/AuthContext";
import AppLoadingScreen from "../components/Common/AppLoadingScreen";

const App: React.FC = () => {
  const [step, setStep] = useState<number>(0);
  const router = useRouter();
  const { authState, isLoading } = useAuth();

  // Handle routing based on auth state
  useEffect(() => {
    if (!isLoading) {
      switch (authState) {
        case "authenticated":
          // User is fully authenticated and has completed interests
          router.replace("/(tabs)/home");
          break;
        case "interests-incomplete":
          // User is authenticated but needs to complete interests
          router.replace("/onboarding/hangout-interests");
          break;
        case "unauthenticated":
          // User needs to authenticate
          router.replace("/(auth)/google-signin");
          break;
        case "onboarding":
          // User needs to see onboarding (this component will handle it)
          break;
      }
    }
  }, [authState, isLoading, router]);

  // Show loading screen while auth state is being determined
  if (isLoading) {
    return <AppLoadingScreen />;
  }

  // If not in onboarding state, redirect (handled by useEffect above)
  if (authState !== "onboarding") {
    return <AppLoadingScreen />;
  }

  const stepHandler = async () => {
    if (step === 2) {
      try {
        await markOnboardingCompleted();
        console.log("onboarding completed");
        // Navigate to auth screen after onboarding completion
        router.replace("/(auth)/google-signin");
      } catch (error) {
        console.error("Error saving onboarding status:", error);
      }
    } else {
      setStep(step + 1);
    }
  };

  return (
    <View className="w-full h-full flex justify-center items-center">
      <StatusBar style="dark" />
      <ImageBackground source={ONBOARDING_STEPS_INFO[step]?.image} className="w-full h-full">
        <SafeAreaView className="w-full h-full flex justify-end">
          <View className="flex p-6 gap-y-14">
            <OnBoardingStep
              step={step}
              title={ONBOARDING_STEPS_INFO[step]?.title ?? ""}
              description={ONBOARDING_STEPS_INFO[step]?.description ?? ""}
            />
            <View>
              <PrimaryButton
                buttonText={step === 2 ? "Get Started" : "Next"}
                onPressHandler={stepHandler}
                borderRadius="xl"
              />
            </View>
          </View>
        </SafeAreaView>
      </ImageBackground>
    </View>
  );
};

export default App;
