import { View, ImageBackground } from "react-native";
import React, { useState, useEffect } from "react";
import OnBoardingStep from "../components/OnBoarding/OnBoardingStep";
import { ONBOARDING_STEPS_INFO } from "../constants/OnBoarding/OnBoardingConstants";
import { SafeAreaView } from "react-native-safe-area-context";
import PrimaryButton from "../components/Buttons/PrimaryButton";
import { useNavigation } from "expo-router";
import { StatusBar } from "expo-status-bar";
import { NavigationProp } from "../lib/types/commonTypes";
import AsyncStorage from "@react-native-async-storage/async-storage";

const ONBOARDING_COMPLETED_KEY = "onboarding_completed";

const App: React.FC = () => {
  const [step, setStep] = useState<number>(0);
  const [loading, setLoading] = useState(true);
  const navigation = useNavigation<NavigationProp>();

  useEffect(() => {
    checkOnboardingStatus();
  }, []);

  const checkOnboardingStatus = async () => {
    try {
      const onboardingCompleted = await AsyncStorage.getItem(ONBOARDING_COMPLETED_KEY);

      if (onboardingCompleted === "true") {
        // Reset navigation stack when redirecting to sign-in
        navigation.reset({
          index: 0,
          routes: [{ name: "(auth)", params: { screen: "google-signin" } }],
        });
      }
    } catch (error) {
      console.error("Error checking onboarding status:", error);
    } finally {
      setLoading(false);
    }
  };

  const stepHandler = async () => {
    if (step === 2) {
      try {
        await AsyncStorage.setItem(ONBOARDING_COMPLETED_KEY, "true");
        console.log("onboarding completed");
      } catch (error) {
        console.error("Error saving onboarding status:", error);
      }
      // Replace the current screen with google-signin instead of navigating to it
      // This removes the current screen from the navigation history
      navigation.reset({
        index: 0,
        routes: [{ name: "(auth)", params: { screen: "google-signin" } }],
      });
    } else {
      setStep(step + 1);
    }
  };

  if (loading) {
    return null; // Or a loading spinner
  }

  return (
    <View className="w-full h-full flex justify-center items-center">
      <StatusBar style="dark" />
      <ImageBackground source={ONBOARDING_STEPS_INFO[step]?.image} className="w-full h-full">
        <SafeAreaView className="w-full h-full flex justify-end">
          <View className="flex p-6 gap-y-14">
            <OnBoardingStep
              step={step}
              title={ONBOARDING_STEPS_INFO[step]?.title ?? ""}
              description={ONBOARDING_STEPS_INFO[step]?.description ?? ""}
            />
            <View>
              <PrimaryButton
                buttonText={step === 2 ? "Get Started" : "Next"}
                onPressHandler={stepHandler}
                borderRadius="xl"
              />
            </View>
          </View>
        </SafeAreaView>
      </ImageBackground>
    </View>
  );
};

export default App;
