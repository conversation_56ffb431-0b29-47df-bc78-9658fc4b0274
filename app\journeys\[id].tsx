import { View, StyleSheet } from "react-native";
import React from "react";
import { Stack, useLocalSearchParams } from "expo-router";
import { useIsFocused } from "@react-navigation/native";
import JourneyDetails from "../../components/Journeys/JourneyDetails";
import { StatusBar } from "expo-status-bar";

const JourneyDetailsScreen = () => {
  const { id } = useLocalSearchParams();
  const isFocused = useIsFocused();

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: false,
          presentation: "modal",
          animation: "slide_from_bottom",
        }}
      />
      <View style={styles.container}>
        {isFocused && <StatusBar style="light" animated backgroundColor="transparent" translucent />}
        <JourneyDetails journeyId={id as string} />
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
});

export default JourneyDetailsScreen;
