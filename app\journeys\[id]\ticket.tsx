import React, { useState, useEffect } from "react";
import { View, Text, ActivityIndicator } from "react-native";
import { Stack, useLocalSearchParams, router } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import JourneyTicket from "../../../components/Journeys/JourneyTicket";
import PageHeader from "../../../components/Common/PageHeader";
import { TouchableOpacity } from "react-native";
import { useJourneyTicket } from "../../../hooks/useJourneyTicket";

export default function JourneyTicketScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { data, isLoading, error } = useJourneyTicket(id);

  const handleClose = () => {
    router.back();
  };

  if (isLoading) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <Stack.Screen options={{ headerShown: false }} />
        <PageHeader title="Journey Ticket" color="black" />
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="large" color="#4A6FFF" />
          <Text className="mt-4 text-gray-600">Loading your ticket...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !data) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <Stack.Screen options={{ headerShown: false }} />
        <PageHeader title="Journey Ticket" color="black" />
        <View className="flex-1 justify-center items-center p-5">
          <Text className="text-xl font-heading text-red-500 mb-2">Ticket Not Found</Text>
          <Text className="text-center text-gray-600 mb-6">
            We couldn't find your ticket. Please try again or contact support.
          </Text>
          <TouchableOpacity className="bg-primary py-3 px-8 rounded-xl" onPress={() => router.back()}>
            <Text className="text-textColor font-heading">Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  if (data && !data.payment) {
    console.log("null payment");
  }

  return (
    <SafeAreaView className="flex-1 bg-white">
      <Stack.Screen options={{ headerShown: false }} />
      <PageHeader title="Journey Ticket" color="black" />
      {data && (
        <JourneyTicket
          journey={data.journey}
          participant={data.participant}
          ticketInfo={data.ticketInfo}
          payment={data.payment}
          qrCode={data.qrCode}
          onClose={handleClose}
          fullPage={true}
        />
      )}
    </SafeAreaView>
  );
}
