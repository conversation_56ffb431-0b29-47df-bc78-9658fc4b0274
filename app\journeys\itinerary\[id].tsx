import React from "react";
import { View, Text, StyleSheet } from "react-native";
import { Stack, useLocalSearchParams } from "expo-router";
import { useIsFocused } from "@react-navigation/native";
import { StatusBar } from "expo-status-bar";

const JourneyItineraryScreen = () => {
  const { id } = useLocalSearchParams();
  const isFocused = useIsFocused();

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: false,
          presentation: "modal",
          animation: "slide_from_bottom",
        }}
      />
      <View style={styles.container}>
        {isFocused && <StatusBar style="dark" />}
        <Text style={styles.text}>Journey Itinerary Details - Coming Soon</Text>
        <Text style={styles.subtext}>Journey ID: {id}</Text>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#f5f5f5",
  },
  text: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 10,
  },
  subtext: {
    fontSize: 14,
    color: "#666",
  },
});

export default JourneyItineraryScreen;
