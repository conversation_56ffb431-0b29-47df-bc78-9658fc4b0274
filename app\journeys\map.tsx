import React from "react";
import { View, Text } from "react-native";
import { Stack, useLocalSearchParams } from "expo-router";

// This will be replaced with actual journey map search component once built
interface JourneyFilters {
  destination?: string;
  travelMode?: string;
  filter?: string;
  search?: string;
}

export default function JourneyMapPage() {
  const params = useLocalSearchParams<{
    destination?: string;
    travelMode?: string;
    filter?: string;
    search?: string;
  }>();

  // Convert URL params to filters
  const initialFilters: JourneyFilters = {
    filter: "upcoming", // Set default filter to upcoming
  };

  if (params.destination) {
    initialFilters.destination = params.destination;
  }

  if (params.travelMode) {
    initialFilters.travelMode = params.travelMode;
  }

  if (params.filter) {
    initialFilters.filter = params.filter;
  }

  if (params.search) {
    initialFilters.search = params.search;
  }

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: false,
          presentation: "modal",
          animation: "slide_from_bottom",
        }}
      />
      <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
        <Text>Journey Map Search - Coming Soon</Text>
        {/* <JourneyMapSearch initialFilters={initialFilters} /> */}
      </View>
    </>
  );
}
