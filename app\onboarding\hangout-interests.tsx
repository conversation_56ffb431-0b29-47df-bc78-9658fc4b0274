import React, { useState } from "react";
import { View, Text, ScrollView, Image } from "react-native";
import { router, Stack } from "expo-router";
import PageHeader from "../../components/Common/PageHeader";
import InterestsSelector from "../../components/Profile/InterestsSelector";
import PrimaryButton from "../../components/Buttons/PrimaryButton";
import { HANGOUT_INTERESTS } from "../../constants/InterestsConstants";
import { updateUserProfile } from "../../api/userService";
import { useAppDispatch, useAppSelector } from "../../reduxStore/hooks";
import { RootState } from "../../reduxStore/store";
import { updateUser } from "../../reduxStore/userSlice";
import { showToast } from "../../lib/utils/showToast";
import { SafeAreaView } from "react-native-safe-area-context";
import { LinearGradient } from "expo-linear-gradient";
import { Ionicons } from "@expo/vector-icons";
import { StatusBar } from "expo-status-bar";

const HangoutInterestsScreen = () => {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state: RootState) => state.user);
  const [selectedInterests, setSelectedInterests] = useState<Record<string, string[]>>(
    user?.profile?.hangoutInterests || {}
  );
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInterestsChange = (interests: Record<string, string[]>) => {
    setSelectedInterests(interests);
  };

  const handleSubmit = async () => {
    if (Object.keys(selectedInterests).length === 0) {
      showToast("error", "Please select at least one interest");
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await updateUserProfile({
        profile: {
          hangoutInterests: selectedInterests,
        },
      });

      // Make sure we have a valid user object before dispatching
      if (user && response.data) {
        dispatch(
          updateUser({
            ...user,
            profile: {
              ...user.profile,
              hangoutInterests: selectedInterests,
            },
          })
        );
      }
      router.push("/onboarding/journey-interests");
    } catch (error) {
      console.error("Error updating hangout interests:", error);
      showToast("error", "Failed to update hangout interests");
    } finally {
      setIsSubmitting(false);
    }
  };

  const totalSelected = Object.values(selectedInterests).flat().length;

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />
      <StatusBar style="dark" />
      <SafeAreaView className="flex-1 bg-white">
        <LinearGradient
          colors={["rgba(255, 222, 89, 0.2)", "rgba(255, 255, 255, 0)"]}
          style={{ position: "absolute", height: 250, left: 0, right: 0, top: 0 }}
        />

        <PageHeader title="Your Hangout Interests" color="black" showIcon={false} />

        <View className="px-5 py-2 mb-2">
          <Text className="font-subheading text-body-300 text-black-100 mb-2">Tell us what you enjoy locally</Text>
          <Text className="font-body-light text-body-200 text-gray-700">
            Select your interests for local hangouts. This helps us recommend activities you'll love!
          </Text>
        </View>

        <View className="px-5 flex-row items-center mb-4">
          <View className="w-10 h-10 rounded-full bg-[#D72638] items-center justify-center mr-3">
            <Ionicons name="people-outline" size={20} color="white" />
          </View>
          <View className="flex-1">
            <View className="h-2 bg-gray-200 rounded-full overflow-hidden">
              <View className="h-full bg-[#D72638]" style={{ width: `${Math.min(100, totalSelected * 15)}%` }} />
            </View>
            <Text className="text-xs text-gray-500 mt-1">{totalSelected} interests selected</Text>
          </View>
        </View>

        <ScrollView className="flex-1 px-5">
          <InterestsSelector
            categories={HANGOUT_INTERESTS}
            selectedInterests={selectedInterests}
            onInterestsChange={handleInterestsChange}
          />
          <View className="h-20" />
        </ScrollView>

        <View className="px-5 py-4 border-t border-gray-100 bg-white">
          <PrimaryButton
            buttonText={isSubmitting ? "Saving..." : "Continue"}
            onPressHandler={handleSubmit}
            disabled={isSubmitting || Object.keys(selectedInterests).length === 0}
          />
          <Text className="text-center text-xs text-gray-500 mt-3">Step 1 of 2 - Hangout Interests</Text>
        </View>
      </SafeAreaView>
    </>
  );
};

export default HangoutInterestsScreen;
