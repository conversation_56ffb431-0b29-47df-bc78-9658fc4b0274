import React from "react";
import { View, ActivityIndicator, Text } from "react-native";
import { Stack, useLocalSearchParams } from "expo-router";
import { useHangoutDetails } from "../../../hooks/useHangoutDetails";
import OrganizerItemDetails from "../../../components/Organizer/OrganizerItemDetails";
import { SafeAreaView } from "react-native-safe-area-context";
import PageHeader from "../../../components/Common/PageHeader";

const OrganizerHangoutDetails = () => {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { data, isLoading, error } = useHangoutDetails(id as string);

  if (isLoading) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <PageHeader title="Hangout Details" />
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="large" color="#D72638" />
        </View>
      </SafeAreaView>
    );
  }

  if (!data || error) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <PageHeader title="Hangout Details" />
        <View className="flex-1 justify-center items-center">
          <Text className="text-textColor">Failed to load hangout details</Text>
        </View>
      </SafeAreaView>
    );
  }

  const { hangout, participants } = data;

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <SafeAreaView className="flex-1 bg-white">
        <PageHeader title="Hangout Details" color="black" />
        <OrganizerItemDetails
          id={hangout._id}
          type="hangout"
          title={hangout.title}
          image={hangout.featuredImage || hangout.images?.[0]}
          date={hangout.date}
          location={hangout.location?.address}
          participantsCount={participants?.length || 0}
          status={hangout.status || "Active"}
          viewsCount={hangout.viewsCount || 0}
        />
      </SafeAreaView>
    </>
  );
};

export default OrganizerHangoutDetails;
