import React, { useEffect, useState } from "react";
import { View, Text, FlatList, Image, TouchableOpacity, ActivityIndicator } from "react-native";
import { Stack, useLocalSearchParams } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import apiClient from "../../../../api/apiClient";
import PageHeader from "../../../../components/Common/PageHeader";
import { Ionicons } from "@expo/vector-icons";
import { statusbgColorMap, statusColorMap } from "../../../../constants/common";
import { toTitleCase } from "../../../../lib/utils/commonUtils";

interface Participant {
  _id: string;
  user: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    profilePic: string;
  };
  status: string;
  checkedIn: boolean;
  checkInTime?: string;
}

const HangoutParticipants = () => {
  const { id } = useLocalSearchParams<{ id: string }>();
  const [participants, setParticipants] = useState<Participant[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    const fetchParticipants = async () => {
      try {
        setLoading(true);
        const response = await apiClient.get(`/hangouts/${id}/participants`);
        setParticipants(response.data.data || []);
      } catch (err) {
        console.error("Error fetching participants:", err);
        setError("Failed to load participants");
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchParticipants();
    }
  }, [id]);

  const renderParticipant = ({ item }: { item: Participant }) => (
    <View className="bg-white rounded-xl mb-3 overflow-hidden shadow-sm">
      <View className="flex-row items-start p-4">
        <Image
          source={{ uri: item.user?.profilePic || "https://via.placeholder.com/50" }}
          className="w-16 h-full rounded-xl mr-4"
        />
        <View className="flex-1">
          <Text className="font-heading text-textColor text-base">
            {item.user?.firstName} {item.user?.lastName}
          </Text>
          <Text className="text-gray-500 text-xs mt-1">{item.user?.email}</Text>
          <Text className="text-gray-500 text-xs mt-1">{item.user?.phone}</Text>

          <View className="flex-row items-center mt-2">
            <View className={`h-2 w-2 rounded-full bg-${statusColorMap[item.status.toLowerCase()]} mr-1.5`} />
            <Text className="text-xs text-gray-600 mr-3">{toTitleCase(item.status)}</Text>

            {item.checkedIn ? (
              <View className="flex-row items-center">
                <Ionicons name="checkmark-circle" size={12} color="#22C55E" />
                <Text className="text-xs text-green-600 ml-1">Checked In</Text>
              </View>
            ) : (
              <View className="flex-row items-center">
                <Ionicons name="time-outline" size={12} color="#EAB308" />
                <Text className="text-xs text-yellow-600 ml-1">Not Checked In</Text>
              </View>
            )}
          </View>
        </View>

        {/* <TouchableOpacity className="bg-gray-100 p-2 rounded-full">
          <Ionicons name="ellipsis-vertical" size={16} color="#666" />
        </TouchableOpacity> */}
      </View>

      {item.checkInTime && (
        <View className="bg-green-50 px-4 py-2 border-t border-green-200">
          <Text className="text-xs text-gray-500">Checked in: {new Date(item.checkInTime).toLocaleString()}</Text>
        </View>
      )}
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <PageHeader title="Participants" />
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="large" color="#D72638" />
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <PageHeader title="Participants" />
        <View className="flex-1 justify-center items-center p-4">
          <Text className="text-textColor text-center">{error}</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <SafeAreaView className="flex-1 bg-gray-50">
        <PageHeader title="Participants" />

        <View className="p-4 bg-white mb-3 shadow-sm">
          <Text className="font-heading text-textColor">Total Participants: {participants.length}</Text>
          <View className="flex-row mt-3">
            <View className="flex-row items-center mr-6">
              <View className="w-3 h-3 rounded-full bg-green-500 mr-2" />
              <Text className="text-sm text-gray-600">
                Checked In: {participants.filter((p) => p.checkedIn).length}
              </Text>
            </View>
            <View className="flex-row items-center">
              <View className="w-3 h-3 rounded-full bg-yellow-500 mr-2" />
              <Text className="text-sm text-gray-600">
                Not Checked In: {participants.filter((p) => !p.checkedIn).length}
              </Text>
            </View>
          </View>
        </View>

        {participants.length === 0 ? (
          <View className="flex-1 justify-center items-center p-4">
            <Ionicons name="people-outline" size={48} color="#D72638" />
            <Text className="font-heading text-lg text-textColor mt-4">No participants yet</Text>
            <Text className="text-gray-500 text-center">Share your hangout to get people to join</Text>
          </View>
        ) : (
          <FlatList
            data={participants}
            renderItem={renderParticipant}
            keyExtractor={(item) => item._id}
            contentContainerStyle={{ padding: 16 }}
          />
        )}
      </SafeAreaView>
    </>
  );
};

export default HangoutParticipants;
