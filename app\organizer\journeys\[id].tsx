import React from "react";
import { View, Text, ActivityIndicator } from "react-native";
import { Stack, useLocalSearchParams } from "expo-router";
import { useJourneyDetails } from "../../../hooks/useJourneyDetails";
import OrganizerItemDetails from "../../../components/Organizer/OrganizerItemDetails";
import { SafeAreaView } from "react-native-safe-area-context";
import PageHeader from "../../../components/Common/PageHeader";

const OrganizerJourneyDetails = () => {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { data: journey, participantCount, isLoading, error } = useJourneyDetails(id as string);

  if (isLoading) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <PageHeader title="Journey Details" />
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="large" color="#4A6FFF" />
        </View>
      </SafeAreaView>
    );
  }

  if (!journey || error) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <PageHeader title="Journey Details" />
        <View className="flex-1 justify-center items-center">
          <Text className="text-textColor">Failed to load journey details</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <SafeAreaView className="flex-1 bg-white">
        <PageHeader title="Journey Details" color="black" />
        <OrganizerItemDetails
          id={journey._id}
          type="journey"
          title={journey.title}
          image={journey.coverImage}
          date={journey.startDate}
          location={journey.destinationCity || journey.destination?.split(",")[0]}
          participantsCount={participantCount || 0}
          status={journey.status || "Active"}
          viewsCount={journey.viewsCount || 0}
        />
      </SafeAreaView>
    </>
  );
};

export default OrganizerJourneyDetails;
