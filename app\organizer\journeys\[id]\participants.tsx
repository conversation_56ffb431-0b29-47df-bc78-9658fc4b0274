import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  FlatList,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  Modal,
  TextInput,
  Alert,
  Platform,
} from "react-native";
import { Stack, useLocalSearchParams } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import PageHeader from "../../../../components/Common/PageHeader";
import { Ionicons } from "@expo/vector-icons";
import { statusColorMap } from "../../../../constants/common";
import { toTitleCase } from "../../../../lib/utils/commonUtils";
import { journeysService } from "../../../../api/journeysService";
import TabButton from "../../../../components/Common/TabButton";
import { JOURNEY_PARTICIPANT_STATUS } from "../../../../constants/journeys";

interface Participant {
  _id: string;
  user: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    profilePic: string;
  };
  status: string;
  checkedIn: boolean;
  checkInTime?: string;
  pricingTier?: string;
  bookingPrice?: number;
  originalPrice?: number;
  discountApplied?: number;
  discountCode?: string;
  upiPaymentScreenshotUrl?: string;
  paymentRejectionReason?: string;
  bookingReference?: string;
}

const JourneyParticipants = () => {
  const { id } = useLocalSearchParams<{ id: string }>();
  const [participants, setParticipants] = useState<Participant[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [rejectModal, setRejectModal] = useState<{ visible: boolean; participantId?: string }>({ visible: false });
  const [rejectReason, setRejectReason] = useState("");
  const [screenshotModal, setScreenshotModal] = useState<{ visible: boolean; url?: string }>({ visible: false });
  const [activeTab, setActiveTab] = useState<"approvalPending" | "confirmed" | "rejected">("confirmed");
  const [checkInLoading, setCheckInLoading] = useState<string | null>(null);
  const [expandedPayment, setExpandedPayment] = useState<string | null>(null);

  const fetchParticipants = async () => {
    try {
      setLoading(true);
      const response = await journeysService.getJourneyParticipants(id!);
      setParticipants(response.data || []);
    } catch (err) {
      setError("Failed to load participants");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (id) {
      fetchParticipants();
    }
  }, [id]);

  const handleApprove = async (participantId: string) => {
    setActionLoading(participantId);
    try {
      await journeysService.approveJourneyParticipant(id!, participantId);
      await fetchParticipants();
    } catch (err) {
      Alert.alert("Error", "Failed to approve payment");
    } finally {
      setActionLoading(null);
    }
  };

  const handleReject = async () => {
    if (!rejectModal.participantId) return;
    setActionLoading(rejectModal.participantId);
    try {
      await journeysService.rejectJourneyParticipant(id!, rejectModal.participantId, rejectReason);
      setRejectModal({ visible: false });
      setRejectReason("");
      await fetchParticipants();
    } catch (err) {
      Alert.alert("Error", "Failed to reject payment");
    } finally {
      setActionLoading(null);
    }
  };

  const handleCheckIn = async (participantId: string) => {
    setCheckInLoading(participantId);
    try {
      await journeysService.checkInParticipant(participantId);
      await fetchParticipants();
    } catch (err) {
      Alert.alert("Error", "Failed to mark as checked in");
    } finally {
      setCheckInLoading(null);
    }
  };

  // Filtered participants for each tab
  const approvalPending = participants.filter((p) => p.status === JOURNEY_PARTICIPANT_STATUS.APPROVAL_PENDING);
  const confirmed = participants.filter((p) => p.status === JOURNEY_PARTICIPANT_STATUS.CONFIRMED);
  const rejected = participants.filter((p) => p.status === JOURNEY_PARTICIPANT_STATUS.REJECTED);

  const getTabData = () => {
    if (activeTab === JOURNEY_PARTICIPANT_STATUS.APPROVAL_PENDING) return approvalPending;
    if (activeTab === JOURNEY_PARTICIPANT_STATUS.CONFIRMED) return confirmed;
    return rejected;
  };

  // Confirmation wrappers (iOS compatible)
  const confirmApprove = (participantId: string) => {
    Alert.alert(
      "Approve Payment",
      "Are you sure you want to approve this payment?",
      [
        { text: "Cancel", style: "cancel" },
        { text: "Approve", style: "default", onPress: () => handleApprove(participantId) },
      ],
      { cancelable: true }
    );
  };
  const confirmReject = (participantId: string) => {
    setRejectModal({ visible: true, participantId });
  };
  const confirmCheckIn = (participantId: string) => {
    Alert.alert(
      "Mark as Checked In",
      "Are you sure you want to mark this participant as checked in?",
      [
        { text: "Cancel", style: "cancel" },
        { text: "Check In", style: "default", onPress: () => handleCheckIn(participantId) },
      ],
      { cancelable: true }
    );
  };

  const renderParticipant = ({ item }: { item: Participant }) => {
    const isApprovalPending = item.status === JOURNEY_PARTICIPANT_STATUS.APPROVAL_PENDING;
    const isConfirmed = item.status === JOURNEY_PARTICIPANT_STATUS.CONFIRMED;
    const isRejected = item.status === JOURNEY_PARTICIPANT_STATUS.REJECTED;
    const hasPaymentDetails = !!item.upiPaymentScreenshotUrl;
    const isExpanded = expandedPayment === item._id;
    return (
      <View className="bg-white rounded-xl mb-3 overflow-hidden shadow-sm border border-gray-100">
        <View className="flex-row items-center p-4">
          <Image
            source={{ uri: item.user?.profilePic || "https://via.placeholder.com/50" }}
            className="w-16 h-16 rounded-xl mr-4"
          />
          <View className="flex-1">
            <Text className="font-heading text-textColor text-base">
              {item.user?.firstName} {item.user?.lastName}
            </Text>
            <Text className="text-gray-500 text-xs mt-1">{item.user?.email}</Text>
            <Text className="text-gray-500 text-xs mt-1">{item.user?.phone}</Text>
            <Text className="text-gray-400 text-xs mt-1">Booking Ref: {item.bookingReference}</Text>
            <View className="flex-row items-center mt-2">
              <View
                className={`h-2 w-2 rounded-full bg-${
                  statusColorMap[item.status?.toLowerCase?.() || "approvalpending"]
                } mr-1.5`}
              />
              <Text className="text-xs text-gray-600 mr-3">{toTitleCase(item.status)}</Text>
              {isConfirmed &&
                (item.checkedIn ? (
                  <View className="flex-row items-center ml-2">
                    <Ionicons name="checkmark-circle" size={14} color="#22C55E" />
                    <Text className="text-xs text-green-600 ml-1">Checked In</Text>
                  </View>
                ) : (
                  <View className="flex-row items-center ml-2">
                    <Ionicons name="time-outline" size={14} color="#EAB308" />
                    <Text className="text-xs text-yellow-600 ml-1">Not Checked In</Text>
                  </View>
                ))}
              {isApprovalPending && item.checkedIn && (
                <View className="flex-row items-center ml-2">
                  <Ionicons name="checkmark-circle" size={14} color="#22C55E" />
                  <Text className="text-xs text-green-600 ml-1">Checked In</Text>
                </View>
              )}
            </View>
          </View>
        </View>

        {/* Payment & Booking Info */}
        <View className="px-4 pb-4">
          <View className="flex-row flex-wrap mb-2 items-center">
            {item.pricingTier && (
              <View className="bg-blue-50 rounded px-2 py-1 mr-2 mb-2">
                <Text className="text-xs text-blue-700">Tier: {item.pricingTier}</Text>
              </View>
            )}
            {item.bookingPrice !== undefined && (
              <View className="bg-green-50 rounded px-2 py-1 mr-2 mb-2">
                <Text className="text-xs text-green-700">Paid: ₹{item.bookingPrice}</Text>
              </View>
            )}
            {item.originalPrice !== undefined && item.originalPrice !== item.bookingPrice && (
              <View className="bg-gray-50 rounded px-2 py-1 mr-2 mb-2">
                <Text className="text-xs text-gray-700 line-through">Original: ₹{item.originalPrice}</Text>
              </View>
            )}
            {item.discountApplied !== undefined &&
              item.discountApplied > 0 &&
              item.originalPrice !== item.bookingPrice && (
                <View className="bg-yellow-50 rounded px-2 py-1 mr-2 mb-2">
                  <Text className="text-xs text-yellow-700">Discount: ₹{item.discountApplied}</Text>
                </View>
              )}
            {item.discountCode && (
              <View className="bg-purple-50 rounded px-2 py-1 mr-2 mb-2">
                <Text className="text-xs text-purple-700">Promo: {item.discountCode}</Text>
              </View>
            )}
          </View>
          {item.paymentRejectionReason && isRejected && (
            <Text className="text-xs text-red-500 mb-1">Rejection Reason: {item.paymentRejectionReason}</Text>
          )}

          {/* Payment Details Accordion Toggle */}
          {hasPaymentDetails && (
            <TouchableOpacity
              className="px-2 py-2 flex-row items-center"
              onPress={() => setExpandedPayment(isExpanded ? null : item._id)}
              accessibilityLabel={isExpanded ? "Hide payment details" : "Show payment details"}
            >
              <Ionicons name="card-outline" size={16} color="#4A6FFF" />
              <Text className="text-xs text-blue-600 ml-1">Payment Details</Text>
              <Ionicons
                name={isExpanded ? "chevron-up" : "chevron-down"}
                size={20}
                color="#4A6FFF"
                style={{ marginLeft: 2 }}
              />
            </TouchableOpacity>
          )}
          {/* Payment Details Dropdown */}
          {isExpanded && (
            <View className="px-4 pb-4">
              {item?.upiPaymentScreenshotUrl && (
                <TouchableOpacity
                  className="mb-2"
                  onPress={() => setScreenshotModal({ visible: true, url: item?.upiPaymentScreenshotUrl })}
                  accessibilityLabel="View payment screenshot"
                >
                  <Image
                    source={{ uri: item?.upiPaymentScreenshotUrl }}
                    className="w-full h-40 rounded-lg border border-blue-200"
                    resizeMode="contain"
                  />
                  <View className="absolute bottom-2 right-2 bg-white/80 rounded-full p-1">
                    <Ionicons name="expand" size={18} color="#4A6FFF" />
                  </View>
                  <Text className="text-xs text-center text-blue-700 mt-1">Tap to view full screenshot</Text>
                </TouchableOpacity>
              )}
            </View>
          )}
          {/* Approve/Reject Actions */}
          {isApprovalPending && (
            <View className="flex-row mt-2">
              <TouchableOpacity
                className="flex-1 bg-green-500 py-2 rounded-lg mr-2 items-center"
                disabled={actionLoading === item._id}
                onPress={() => confirmApprove(item._id)}
              >
                {actionLoading === item._id ? (
                  <ActivityIndicator color="#fff" />
                ) : (
                  <Text className="text-white font-semibold">Approve</Text>
                )}
              </TouchableOpacity>
              <TouchableOpacity
                className="flex-1 bg-red-500 py-2 rounded-lg ml-2 items-center"
                disabled={actionLoading === item._id}
                onPress={() => confirmReject(item._id)}
              >
                <Text className="text-white font-semibold">Reject</Text>
              </TouchableOpacity>
            </View>
          )}
          {/* Mark as Checked In (Confirmed tab only, not checked in) */}
          {isConfirmed && !item.checkedIn && (
            <TouchableOpacity
              className="mt-3 bg-primary-600 py-2 rounded-lg items-center"
              disabled={checkInLoading === item._id}
              onPress={() => confirmCheckIn(item._id)}
            >
              {checkInLoading === item._id ? (
                <ActivityIndicator color="#fff" />
              ) : (
                <Text className="text-white font-semibold">Mark as Checked In</Text>
              )}
            </TouchableOpacity>
          )}
        </View>
        {item.checkInTime && (
          <View className="bg-green-50 px-4 py-2 border-t border-green-100">
            <Text className="text-xs text-gray-500">Checked in: {new Date(item.checkInTime).toLocaleString()}</Text>
          </View>
        )}
      </View>
    );
  };

  // Info bar above tabs
  const infoBarText =
    activeTab === "approvalPending"
      ? "Approve or reject participant payments."
      : activeTab === "confirmed"
      ? "View and manage check-in status."
      : "See rejected participants and reasons.";

  if (loading) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <PageHeader title="Participants" />
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="large" color="#4A6FFF" />
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <PageHeader title="Participants" />
        <View className="flex-1 justify-center items-center p-4">
          <Text className="text-textColor text-center">{error}</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <SafeAreaView className="flex-1 bg-gray-50">
        <PageHeader title="Participants" color="textColor" />

        {/* Tab Bar */}
        <View className="flex-row bg-slate-100 mb-4 rounded-xl mx-4 mt-4 overflow-hidden justify-around">
          <TabButton
            title={`Confirmed (${confirmed.length})`}
            isActive={activeTab === "confirmed"}
            onPress={() => setActiveTab("confirmed")}
            smallScreen={true}
          />
          <TabButton
            title={`Pending (${approvalPending.length})`}
            isActive={activeTab === "approvalPending"}
            onPress={() => setActiveTab("approvalPending")}
            smallScreen={true}
          />
          <TabButton
            title={`Rejected (${rejected.length})`}
            isActive={activeTab === "rejected"}
            onPress={() => setActiveTab("rejected")}
            smallScreen={true}
          />
        </View>
        {/* Info Bar */}
        <View className="px-4 py-2 bg-slate-50 border-t border-slate-200">
          <Text className="text-xs text-gray-600 text-center">{infoBarText}</Text>
        </View>

        {/* Tab Content */}
        {getTabData().length === 0 ? (
          <View className="flex-1 justify-center items-center p-4">
            <Ionicons
              name={
                activeTab === "approvalPending"
                  ? "hourglass-outline"
                  : activeTab === "confirmed"
                  ? "checkmark-done-circle-outline"
                  : "close-circle-outline"
              }
              size={48}
              color={activeTab === "approvalPending" ? "#4A6FFF" : activeTab === "confirmed" ? "#22C55E" : "#EF4444"}
            />
            <Text className="font-heading text-lg text-textColor mt-4">
              {activeTab === "approvalPending"
                ? "No participants awaiting approval"
                : activeTab === "confirmed"
                ? "No confirmed participants yet"
                : "No rejected participants"}
            </Text>
          </View>
        ) : (
          <FlatList
            data={getTabData()}
            renderItem={renderParticipant}
            keyExtractor={(item) => item._id}
            contentContainerStyle={{ padding: 16 }}
          />
        )}
        {/* Reject Reason Modal */}
        <Modal
          visible={rejectModal.visible}
          transparent
          animationType="slide"
          onRequestClose={() => setRejectModal({ visible: false })}
        >
          <View className="flex-1 justify-center items-center bg-black/40 bg-opacity-40">
            <View className="bg-white rounded-lg p-6 w-11/12 max-w-md">
              <Text className="font-heading text-lg mb-2">Reject Payment</Text>
              <TextInput
                className="border border-gray-300 rounded px-3 py-2 mb-4"
                placeholder="Enter rejection reason"
                value={rejectReason}
                onChangeText={setRejectReason}
                multiline
                numberOfLines={3}
              />
              <View className="flex-row justify-end">
                <TouchableOpacity
                  className="bg-gray-100 mr-4 px-4 py-2 rounded-lg"
                  onPress={() => setRejectModal({ visible: false })}
                  disabled={actionLoading !== null}
                >
                  <Text className="text-gray-600">Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  className="bg-red-500 px-4 py-2 rounded-lg"
                  onPress={handleReject}
                  disabled={actionLoading !== null || !rejectReason.trim()}
                >
                  {actionLoading ? (
                    <ActivityIndicator color="#fff" />
                  ) : (
                    <Text className="text-white font-semibold">Reject</Text>
                  )}
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
        {/* Payment Screenshot Modal */}
        <Modal
          visible={screenshotModal.visible}
          transparent
          animationType="fade"
          onRequestClose={() => setScreenshotModal({ visible: false })}
        >
          <View className="flex-1 justify-center items-center bg-black/80 bg-opacity-80">
            <TouchableOpacity
              style={{ position: "absolute", top: 40, right: 20, zIndex: 10 }}
              onPress={() => setScreenshotModal({ visible: false })}
            >
              <Ionicons name="close-circle" size={36} color="#fff" />
            </TouchableOpacity>
            {screenshotModal.url && (
              <Image
                source={{ uri: screenshotModal.url }}
                style={{ width: "100%", height: "100%" }}
                resizeMode="contain"
              />
            )}
          </View>
        </Modal>
      </SafeAreaView>
    </>
  );
};

export default JourneyParticipants;
