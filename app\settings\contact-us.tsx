import React, { useState } from "react";
import { View, Text, ScrollView, TextInput, TouchableOpacity, Alert } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import PageHeader from "../../components/Common/PageHeader";
import { showToast } from "../../lib/utils/showToast";
import { Stack } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { supportService } from "../../api/supportService";

const ContactUsScreen = () => {
  const [subject, setSubject] = useState("");
  const [message, setMessage] = useState("");
  const [email, setEmail] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    if (!subject.trim()) {
      showToast("error", "Please enter a subject");
      return;
    }

    if (!message.trim() || message.length < 10) {
      showToast("error", "Please provide a detailed message (minimum 10 characters)");
      return;
    }

    if (!email.trim() || !/\S+@\S+\.\S+/.test(email)) {
      showToast("error", "Please enter a valid email address");
      return;
    }

    setIsSubmitting(true);

    try {
      // Add email to the message content
      const fullMessage = `Email: ${email}\n\n${message}`;

      await supportService.submitSupportRequest({
        subject: subject,
        message: fullMessage,
        category: "other",
        priority: "normal",
      });

      // Success
      showToast("success", "Your message has been sent");
      setSubject("");
      setMessage("");
      setEmail("");

      Alert.alert("Message Sent", "Thank you for contacting us. We'll get back to you as soon as possible.", [
        { text: "OK" },
      ]);
    } catch (error) {
      console.error("Error sending message:", error);
      showToast("error", "Failed to send message");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <SafeAreaView className="flex-1 bg-white">
        <PageHeader title="Contact Us" color="black" />
        <ScrollView className="flex-1 p-5">
          <View className="mb-6 flex-row items-center">
            <View className="w-12 h-12 bg-button/10 rounded-full items-center justify-center mr-4">
              <Ionicons name="mail-outline" size={24} color="#346aff" />
            </View>
            <View className="flex-1">
              <Text className="text-lg font-bold">Get in Touch</Text>
              <Text className="text-gray-600">We'd love to hear from you</Text>
            </View>
          </View>

          <View className="mb-6">
            <Text className="text-base font-bold mb-2">Subject</Text>
            <TextInput
              className="bg-gray-50 rounded-lg p-3 text-base"
              placeholder="What is this regarding?"
              value={subject}
              onChangeText={setSubject}
            />
          </View>

          <View className="mb-6">
            <Text className="text-base font-bold mb-2">Your Email</Text>
            <TextInput
              className="bg-gray-50 rounded-lg p-3 text-base"
              placeholder="Where can we reach you?"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>

          <View className="mb-6">
            <Text className="text-base font-bold mb-2">Message</Text>
            <TextInput
              className="bg-gray-50 rounded-lg p-3 text-base min-h-[120px]"
              placeholder="How can we help you?"
              multiline
              textAlignVertical="top"
              value={message}
              onChangeText={setMessage}
            />
            <Text className="text-xs text-gray-500 mt-1">{message.length}/500 characters (minimum 10)</Text>
          </View>

          <TouchableOpacity
            className={`rounded-lg py-3 ${isSubmitting ? "bg-primary-300" : "bg-primary-500"}`}
            onPress={handleSubmit}
            disabled={isSubmitting}
          >
            <Text className="text-textColor text-center text-base font-bold">
              {isSubmitting ? "Sending..." : "Send Message"}
            </Text>
          </TouchableOpacity>

          <View className="mt-8 bg-gray-50 p-4 rounded-lg">
            <Text className="text-base font-bold mb-2">Other Ways to Reach Us</Text>
            <View className="flex-row items-center mb-3">
              <Ionicons name="mail-outline" size={20} color="#4f46e5" style={{ marginRight: 8 }} />
              <Text className="text-gray-700"><EMAIL></Text>
            </View>
            {/* <View className="flex-row items-center">
              <Ionicons name="call-outline" size={20} color="#4f46e5" style={{ marginRight: 8 }} />
              <Text className="text-gray-700">+1 (555) 123-4567</Text>
            </View> */}
          </View>
        </ScrollView>
      </SafeAreaView>
    </>
  );
};

export default ContactUsScreen;
