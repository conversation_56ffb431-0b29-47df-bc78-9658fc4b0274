import React, { useState } from "react";
import { View, Text, ScrollView, TouchableOpacity, TextInput } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import PageHeader from "../../components/Common/PageHeader";
import { Stack } from "expo-router";

const HelpCenterScreen = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [expandedFaq, setExpandedFaq] = useState<number | null>(null);

  const faqs = [
    {
      question: "How do I create a hangout?",
      answer:
        "To create a hangout, go to the Hangouts tab and tap on the '+' button. Follow the step-by-step guide to set up your hangout details, location, activities, and more.",
    },
    {
      question: "How do I join a journey?",
      answer:
        "Browse available journeys in the Journeys tab. When you find one you like, tap on it to view details and then tap the 'Join Journey' button to request to join.",
    },
    {
      question: "How do I update my profile?",
      answer:
        "Go to the Profile tab and tap the edit icon in the top right corner of your profile. From there, you can update your personal information, interests, and preferences.",
    },
    {
      question: "What's the difference between hangouts and journeys?",
      answer:
        "Hangouts are typically shorter activities lasting a few hours, while journeys are longer experiences that can span multiple days and include various activities and destinations.",
    },
    {
      question: "How do I become a host?",
      answer:
        "To become a host, go to your profile settings and select 'Become a Host'. You'll need to complete a verification process and provide additional information about yourself.",
    },
  ];

  const toggleFaq = (index: number) => {
    if (expandedFaq === index) {
      setExpandedFaq(null);
    } else {
      setExpandedFaq(index);
    }
  };

  const filteredFaqs = searchQuery
    ? faqs.filter(
        (faq) =>
          faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
          faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : faqs;

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />

      <SafeAreaView className="flex-1 bg-white">
        <PageHeader title="Help Center" color="black" />
        <ScrollView className="flex-1 p-5">
          <View className="mb-6">
            <Text className="text-2xl font-bold mb-4">How can we help you?</Text>
            <View className="flex-row items-center bg-gray-100 rounded-lg px-3 py-2">
              <Ionicons name="search" size={20} color="#666" />
              <TextInput
                className="flex-1 ml-2 text-base"
                placeholder="Search for help"
                value={searchQuery}
                onChangeText={setSearchQuery}
              />
              {searchQuery ? (
                <TouchableOpacity onPress={() => setSearchQuery("")}>
                  <Ionicons name="close-circle" size={20} color="#666" />
                </TouchableOpacity>
              ) : null}
            </View>
          </View>

          <View className="mb-6">
            <Text className="text-xl font-bold mb-4">Frequently Asked Questions</Text>
            {filteredFaqs.length > 0 ? (
              filteredFaqs.map((faq, index) => (
                <TouchableOpacity
                  key={index}
                  className={`mb-4 bg-gray-50 rounded-lg p-4 ${
                    expandedFaq === index ? "border-l-4 border-secondary" : ""
                  }`}
                  onPress={() => toggleFaq(index)}
                >
                  <View className="flex-row justify-between items-center">
                    <Text className="text-base font-bold flex-1">{faq.question}</Text>
                    <Ionicons name={expandedFaq === index ? "chevron-up" : "chevron-down"} size={20} color="#666" />
                  </View>
                  {expandedFaq === index && <Text className="text-base mt-2 text-gray-700">{faq.answer}</Text>}
                </TouchableOpacity>
              ))
            ) : (
              <Text className="text-base text-gray-500">No results found for "{searchQuery}"</Text>
            )}
          </View>

          <View className="mb-10">
            <Text className="text-xl font-bold mb-4">Still Need Help?</Text>
            <TouchableOpacity className="bg-gray-50 rounded-lg p-4 mb-3 flex-row items-center">
              <Ionicons name="mail-outline" size={24} color="#333" className="mr-3" />
              <View className="ml-3">
                <Text className="text-base font-bold">Email Support</Text>
                <Text className="text-sm text-gray-600">Get help via email</Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity className="bg-gray-50 rounded-lg p-4 mb-3 flex-row items-center">
              <Ionicons name="chatbubble-ellipses-outline" size={24} color="#333" className="mr-3" />
              <View className="ml-3">
                <Text className="text-base font-bold">Live Chat</Text>
                <Text className="text-sm text-gray-600">Chat with our support team</Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity className="bg-gray-50 rounded-lg p-4 flex-row items-center">
              <Ionicons name="document-text-outline" size={24} color="#333" className="mr-3" />
              <View className="ml-3">
                <Text className="text-base font-bold">Knowledge Base</Text>
                <Text className="text-sm text-gray-600">Browse our help articles</Text>
              </View>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </SafeAreaView>
    </>
  );
};

export default HelpCenterScreen;
