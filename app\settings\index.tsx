import { <PERSON>, Text, TouchableO<PERSON>city, <PERSON><PERSON>View, Switch, Alert } from "react-native";
import React, { useState } from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack, useRouter } from "expo-router";
import { <PERSON><PERSON><PERSON>, <PERSON>ather } from "@expo/vector-icons";
import PageHeader from "../../components/Common/PageHeader";
import { useAppDispatch, useAppSelector } from "../../reduxStore/hooks";
import { RootState } from "../../reduxStore/store";
import { updateUserProfile } from "../../api/userService";
import { updateUser } from "../../reduxStore/userSlice";
import { showToast } from "../../lib/utils/showToast";
import { auth } from "../../config/firebase";
import Animated, { FadeIn } from "react-native-reanimated";
import apiClient from "../../api/apiClient"; // Make sure this path is correct

// Define interfaces for the different types of settings items
interface BaseSettingItem {
  title: string;
  icon: string;
  type: string;
  info?: string;
}

interface LinkSettingItem extends BaseSettingItem {
  type: "link";
  action: () => void;
}

interface ToggleSettingItem extends BaseSettingItem {
  type: "toggle";
  value: boolean;
  action: () => Promise<void>;
}

interface InfoSettingItem extends BaseSettingItem {
  type: "info";
}

type SettingItem = LinkSettingItem | ToggleSettingItem | InfoSettingItem;

interface SettingSection {
  title: string;
  items: SettingItem[];
}

const SettingsPage = () => {
  const { user } = useAppSelector((state: RootState) => state.user);
  const dispatch = useAppDispatch();
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  // Default preferences if not set
  const preferences = user?.profile?.preferences || {
    showStats: true,
    showBadges: true,
    notifications: { general: true }, // Adjusted to match Record<string, boolean>
    emailUpdates: true,
    locationServices: true,
    activityStatus: true,
    profileVisibility: true,
  };

  const [settings, setSettings] = useState(preferences);

  const handleToggle = async (setting: string) => {
    const newSettings = {
      ...settings,
      [setting]: !settings[setting as keyof typeof settings],
    };

    setSettings(newSettings);

    try {
      setLoading(true);
      await updateUserProfile({
        profile: {
          preferences: newSettings,
        },
      });

      if (user) {
        dispatch(
          updateUser({
            ...user,
            profile: {
              ...user.profile,
              preferences: newSettings,
            },
          })
        );
      }

      showToast("success", "Settings updated successfully");
    } catch (error) {
      console.error("Error updating settings:", error);
      showToast("error", "Failed to update settings");
      // Revert the toggle if update fails
      setSettings(settings);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      await auth.signOut();
      router.replace("/(auth)/google-signin");
    } catch (error) {
      console.error("Error signing out:", error);
      showToast("error", "Failed to sign out");
    }
  };

  // Add this function
  const handleDeleteAccount = async () => {
    Alert.alert("Delete Account", "Are you sure you want to delete your account? This action cannot be undone.", [
      { text: "Cancel", style: "cancel" },
      {
        text: "Delete",
        style: "destructive",
        onPress: async () => {
          try {
            setLoading(true);
            await apiClient.delete("/users/me");
            await auth.signOut();
            showToast("success", "Your account has been deleted.");
            router.replace("/(auth)/google-signin");
          } catch (error) {
            console.error("Error deleting account:", error);
            showToast("error", "Failed to delete account");
          } finally {
            setLoading(false);
          }
        },
      },
    ]);
  };

  // Settings sections data
  const settingsSections: SettingSection[] = [
    {
      title: "Account",
      items: [
        {
          title: "Edit Profile",
          icon: "person-outline",
          action: () => router.push("/settings/profile-edit"),
          type: "link",
        },
        // {
        //   title: "Notifications",
        //   icon: "notifications-outline",
        //   action: () => router.push("/settings/notifications"),
        //   type: "link",
        // },
      ],
    },
    {
      title: "Privacy & Security",
      items: [
        // {
        //   title: "Location Services",
        //   icon: "location-outline",
        //   value: settings.locationServices,
        //   action: () => handleToggle("locationServices"),
        //   type: "toggle",
        // },
        // {
        //   title: "Activity Status",
        //   icon: "eye-outline",
        //   value: settings.activityStatus,
        //   action: () => handleToggle("activityStatus"),
        //   type: "toggle",
        // },
        // {
        //   title: "Profile Visibility",
        //   icon: "people-outline",
        //   value: settings.profileVisibility,
        //   action: () => handleToggle("profileVisibility"),
        //   type: "toggle",
        // },
        {
          title: "Privacy Policy",
          icon: "shield-checkmark-outline",
          action: () => router.push("/settings/privacy-policy"),
          type: "link",
        },
        {
          title: "Terms of Service",
          icon: "document-text-outline",
          action: () => router.push("/settings/terms-of-service"),
          type: "link",
        },
      ],
    },
    // {
    //   title: "Appearance",
    //   items: [
    //     {
    //       title: "Show Stats on Profile",
    //       icon: "stats-chart-outline",
    //       value: settings.showStats,
    //       action: () => handleToggle("showStats"),
    //       type: "toggle",
    //     },
    //     {
    //       title: "Show Badges on Profile",
    //       icon: "ribbon-outline",
    //       value: settings.showBadges,
    //       action: () => handleToggle("showBadges"),
    //       type: "toggle",
    //     },
    //     {
    //       title: "Theme",
    //       icon: "color-palette-outline",
    //       info: "Light",
    //       action: () => router.push("/settings/theme"),
    //       type: "link",
    //     },
    //     {
    //       title: "Language",
    //       icon: "language-outline",
    //       info: "English",
    //       action: () => router.push("/settings/language"),
    //       type: "link",
    //     },
    //   ],
    // },
    {
      title: "Support",
      items: [
        // {
        //   title: "Help Center",
        //   icon: "help-circle-outline",
        //   action: () => router.push("/settings/help-center"),
        //   type: "link",
        // },
        {
          title: "Report a Problem",
          icon: "warning-outline",
          action: () => router.push("/settings/report-problem"),
          type: "link",
        },
        {
          title: "Contact Us",
          icon: "mail-outline",
          action: () => router.push("/settings/contact-us"),
          type: "link",
        },
      ],
    },
    {
      title: "About",
      items: [
        {
          title: "App Version",
          icon: "information-circle-outline",
          info: "1.0.1",
          type: "info",
        },
        // {
        //   title: "Licenses",
        //   icon: "document-outline",
        //   action: () => router.push("/settings/licenses"),
        //   type: "link",
        // },
      ],
    },
  ];

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />

      <SafeAreaView className="flex-1 bg-background">
        <PageHeader title="Settings" color="black" showIcon={true} />
        <ScrollView className="flex-1 pt-2">
          {settingsSections.map((section, sectionIndex) => (
            <Animated.View
              key={sectionIndex}
              className="mb-5"
              entering={FadeIn.delay(sectionIndex * 100).duration(400)}
            >
              <Text className="text-base font-heading text-headline-400 px-5 mb-2">{section.title}</Text>
              <View className="bg-white rounded-xl mx-4 border border-slate-200 overflow-hidden">
                {section.items.map((item, itemIndex) => (
                  <View key={itemIndex}>
                    {item.type === "link" && (
                      <TouchableOpacity
                        className="flex-row items-center py-4 px-4 border-b border-slate-100"
                        onPress={item.action}
                        style={itemIndex === section.items.length - 1 ? { borderBottomWidth: 0 } : {}}
                      >
                        <View
                          className="w-10 h-10 rounded-full items-center justify-center mr-3"
                          style={{ backgroundColor: "rgba(79, 70, 229, 0.1)" }}
                        >
                          <Ionicons name={item.icon as any} size={20} color="#4f46e5" />
                        </View>
                        <View className="flex-1">
                          <Text className="text-base font-body text-body-400">{item.title}</Text>
                          {item.info && <Text className="text-xs text-gray-500 mt-1">{item.info}</Text>}
                        </View>
                        <Feather name="chevron-right" size={20} color="#999" />
                      </TouchableOpacity>
                    )}
                    {item.type === "toggle" && (
                      <View
                        className="flex-row items-center py-4 px-4 border-b border-slate-100"
                        style={itemIndex === section.items.length - 1 ? { borderBottomWidth: 0 } : {}}
                      >
                        <View
                          className="w-10 h-10 rounded-full items-center justify-center mr-3"
                          style={{ backgroundColor: "rgba(79, 70, 229, 0.1)" }}
                        >
                          <Ionicons name={item.icon as any} size={20} color="#4f46e5" />
                        </View>
                        <Text className="flex-1 text-base font-body text-body-400">{item.title}</Text>
                        <Switch
                          value={item.value}
                          onValueChange={item.action}
                          disabled={loading}
                          trackColor={{ false: "#d1d5db", true: "#4f46e5" }}
                          thumbColor={item.value ? "#ffffff" : "#f4f3f4"}
                          ios_backgroundColor="#d1d5db"
                        />
                      </View>
                    )}
                    {item.type === "info" && (
                      <View
                        className="flex-row items-center py-4 px-4 border-b border-slate-100"
                        style={itemIndex === section.items.length - 1 ? { borderBottomWidth: 0 } : {}}
                      >
                        <View
                          className="w-10 h-10 rounded-full items-center justify-center mr-3"
                          style={{ backgroundColor: "rgba(79, 70, 229, 0.1)" }}
                        >
                          <Ionicons name={item.icon as any} size={20} color="#4f46e5" />
                        </View>
                        <Text className="flex-1 text-base font-body text-body-400">{item.title}</Text>
                        <Text className="text-sm text-gray-500">{item.info}</Text>
                      </View>
                    )}
                  </View>
                ))}
              </View>
            </Animated.View>
          ))}

          {/* Logout button - styled to match other components */}
          <TouchableOpacity
            className="mx-4 mb-3 bg-white rounded-xl border border-slate-200 overflow-hidden"
            onPress={handleLogout}
          >
            <View className="flex-row items-center py-4 px-4">
              <View
                className="w-10 h-10 rounded-full items-center justify-center mr-3"
                style={{ backgroundColor: "rgba(225, 29, 72, 0.1)" }}
              >
                <Ionicons name="log-out-outline" size={20} color="#e11d48" />
              </View>
              <Text className="flex-1 text-base font-body-medium text-red-600">Logout</Text>
            </View>
          </TouchableOpacity>

          {/* Delete Account button */}
          <TouchableOpacity
            className="mx-4 mb-10 bg-white rounded-xl border border-slate-200 overflow-hidden"
            onPress={handleDeleteAccount}
            disabled={loading}
          >
            <View className="flex-row items-center py-4 px-4">
              <View
                className="w-10 h-10 rounded-full items-center justify-center mr-3"
                style={{ backgroundColor: "rgba(239, 68, 68, 0.1)" }}
              >
                <Ionicons name="trash-outline" size={20} color="#ef4444" />
              </View>
              <Text className="flex-1 text-base font-body-medium text-red-600">Delete Account</Text>
            </View>
          </TouchableOpacity>
        </ScrollView>
      </SafeAreaView>
    </>
  );
};

export default SettingsPage;
