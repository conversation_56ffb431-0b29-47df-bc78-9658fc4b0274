import React from "react";
import { View, Text, TouchableOpacity, Share, ScrollView, Image } from "react-native";
import { Stack, router } from "expo-router";
import { StatusBar } from "expo-status-bar";
import { Ionicons } from "@expo/vector-icons";
import { SafeAreaView, useSafeAreaInsets } from "react-native-safe-area-context";
import PageHeader from "../../components/Common/PageHeader";
import { getInviteLink } from "../../constants/common";

const InviteFriendsScreen = () => {
  const insets = useSafeAreaInsets();

  const handleShare = async () => {
    try {
      const result = await Share.share({
        message: `I found this awesome app called Logoutloud—it's all about making real offline connections and breaking free from endless scrolling. Join me and let's make life social again! Download here: ${getInviteLink()}`,
      });
    } catch (error) {
      console.error("Error sharing:", error);
    }
  };

  return (
    <>
      <Stack.Screen
        options={{
          title: "Invite Friends",
          headerShown: false,
        }}
      />

      <SafeAreaView className="flex-1 bg-white">
        <PageHeader title="Invite Friends" color="black" />
        <ScrollView
          style={{ flex: 1, backgroundColor: "white" }}
          contentContainerStyle={{ padding: 16, paddingBottom: insets.bottom + 16 }}
        >
          {/* Header Section */}
          <View className="items-center mb-8 mt-4">
            <View className="bg-yellow-100 p-4 rounded-full mb-4">
              <Ionicons name="people" size={40} color="#FFDE59" />
            </View>
            <Text className="font-heading text-2xl text-textColor text-center">Share Logoutloud With Friends</Text>
            <Text className="font-body text-gray-600 text-center mt-2">
              Help your friends discover amazing offline experiences and make meaningful connections!
            </Text>
          </View>

          {/* Why Share Section */}
          <View className="bg-white rounded-xl p-5 mb-6 shadow-sm border border-gray-100">
            <Text className="font-heading text-lg text-textColor mb-3">Why Share?</Text>

            <View className="flex-row items-center mb-4">
              <View className="bg-blue-100 p-2 rounded-full mr-3">
                <Ionicons name="people-outline" size={20} color="#4A6FFF" />
              </View>
              <View className="flex-1">
                <Text className="font-body-medium text-textColor">Better with friends</Text>
                <Text className="font-body text-xs text-gray-500">
                  More friends means more opportunities to connect offline
                </Text>
              </View>
            </View>

            <View className="flex-row items-center mb-4">
              <View className="bg-green-100 p-2 rounded-full mr-3">
                <Ionicons name="compass-outline" size={20} color="#38B000" />
              </View>
              <View className="flex-1">
                <Text className="font-body-medium text-textColor">Discover together</Text>
                <Text className="font-body text-xs text-gray-500">
                  Find and join activities with people you already know
                </Text>
              </View>
            </View>

            <View className="flex-row items-center">
              <View className="bg-purple-100 p-2 rounded-full mr-3">
                <Ionicons name="heart-outline" size={20} color="#8B5CF6" />
              </View>
              <View className="flex-1">
                <Text className="font-body-medium text-textColor">Meaningful connections</Text>
                <Text className="font-body text-xs text-gray-500">
                  Help friends move from online to real-world experiences
                </Text>
              </View>
            </View>
          </View>

          {/* Share Button Section */}
          <View className="bg-white rounded-xl p-5 mb-6 shadow-sm border border-gray-100">
            <Text className="font-heading text-lg text-textColor mb-3">Share Logoutloud</Text>
            <Text className="font-body text-gray-600 mb-4">
              Invite your friends to join Logoutloud and start exploring together!
            </Text>

            <TouchableOpacity
              className="bg-primary-500 py-3 rounded-xl items-center flex-row justify-center"
              onPress={handleShare}
            >
              <Ionicons name="share-social-outline" size={20} color="#2e2e2e" style={{ marginRight: 8 }} />
              <Text className="font-body-medium text-textColor">Share With Friends</Text>
            </TouchableOpacity>
          </View>

          {/* Impact Section */}
          <View className="mb-6">
            <Text className="font-heading text-lg text-textColor mb-3">Make an Impact</Text>

            <View className="bg-gray-50 rounded-xl p-4 mb-4">
              <Text className="font-body text-center text-gray-700 italic">
                {`"Behind every share is a story \n waiting to be rewritten offline."`}
              </Text>
            </View>

            <Text className="font-body text-gray-600">
              By sharing Logoutloud, you're helping create a community focused on real-life connections and experiences.
              Together, we can shift from endless scrolling to meaningful adventures.
            </Text>
          </View>
        </ScrollView>
      </SafeAreaView>
    </>
  );
};

export default InviteFriendsScreen;
