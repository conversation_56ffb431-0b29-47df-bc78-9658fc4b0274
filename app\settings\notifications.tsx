import React, { useState } from "react";
import { View, Text, Switch, ScrollView } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import PageHeader from "../../components/Common/PageHeader";
import { useAppDispatch, useAppSelector } from "../../reduxStore/hooks";
import { RootState } from "../../reduxStore/store";
import { updateUserProfile } from "../../api/userService";
import { updateUser } from "../../reduxStore/userSlice";
import { showToast } from "../../lib/utils/showToast";
import Animated, { FadeIn } from "react-native-reanimated";

const NotificationsScreen = () => {
  const { user } = useAppSelector((state: RootState) => state.user);
  const dispatch = useAppDispatch();
  const [loading, setLoading] = useState(false);

  // Default notification preferences
  const defaultNotifications = {
    enabled: true,
    emailUpdates: true,
    hangoutInvites: true,
    journeyUpdates: true,
    messages: true,
    friendRequests: true,
    appUpdates: true,
  };

  // Get current notifications or use defaults
  const notificationPrefs = {
    ...defaultNotifications,
    ...(user?.profile?.preferences?.notifications || {}),
  };

  const [settings, setSettings] = useState(notificationPrefs);

  const handleToggle = async (setting: string) => {
    const newSettings = {
      ...settings,
      [setting]: !settings[setting as keyof typeof settings],
    };

    setSettings(newSettings);

    try {
      setLoading(true);
      await updateUserProfile({
        profile: {
          preferences: {
            ...(user?.profile?.preferences || {}),
            notifications: newSettings,
          },
        },
      });

      if (user) {
        dispatch(
          updateUser({
            ...user,
            profile: {
              ...user.profile,
              preferences: {
                ...(user?.profile?.preferences || {}),
                notifications: newSettings,
              },
            },
          })
        );
      }

      showToast("success", "Notification settings updated");
    } catch (error) {
      console.error("Error updating notification settings:", error);
      showToast("error", "Failed to update settings");
      setSettings(settings);
    } finally {
      setLoading(false);
    }
  };

  const notificationItems = [
    {
      id: "enabled",
      title: "All Notifications",
      description: "Master toggle for all notifications",
      icon: "notifications-outline",
    },
    {
      id: "emailUpdates",
      title: "Email Updates",
      description: "Receive updates via email",
      icon: "mail-outline",
    },
    {
      id: "hangoutInvites",
      title: "Hangout Invites",
      description: "Get notified about new hangout invites",
      icon: "people-outline",
    },
    {
      id: "journeyUpdates",
      title: "Journey Updates",
      description: "Updates about journeys you're part of",
      icon: "map-outline",
    },
    {
      id: "messages",
      title: "Messages",
      description: "Notifications for new messages",
      icon: "chatbubble-outline",
    },
    {
      id: "friendRequests",
      title: "Friend Requests",
      description: "Get notified about new friend requests",
      icon: "person-add-outline",
    },
    {
      id: "appUpdates",
      title: "App Updates",
      description: "Notifications about new features and updates",
      icon: "information-circle-outline",
    },
  ];

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />

      <SafeAreaView className="flex-1 bg-background">
        <PageHeader title="Notifications" color="black" showIcon={true} />

        <ScrollView className="flex-1 pt-4">
          <Animated.View entering={FadeIn.duration(400)} className="px-4">
            <Text className="text-base font-body text-body-200 text-gray-700 mb-6">
              Manage how and when you receive notifications from the app.
            </Text>

            <View className="bg-white rounded-xl border border-slate-200 overflow-hidden mb-8">
              {notificationItems.map((item, index) => (
                <View
                  key={item.id}
                  className={`py-4 px-4 ${index < notificationItems.length - 1 ? "border-b border-slate-100" : ""}`}
                >
                  <View className="flex-row items-center">
                    <View
                      className="w-10 h-10 rounded-full items-center justify-center mr-3"
                      style={{ backgroundColor: "rgba(79, 70, 229, 0.1)" }}
                    >
                      <Ionicons name={item.icon as any} size={20} color="#4f46e5" />
                    </View>

                    <View className="flex-1">
                      <Text className="text-base font-body text-body-400">{item.title}</Text>
                      <Text className="text-xs text-gray-500 mt-1">{item.description}</Text>
                    </View>

                    <Switch
                      value={settings[item.id as keyof typeof settings] as boolean}
                      onValueChange={() => handleToggle(item.id)}
                      disabled={loading || (item.id !== "enabled" && !settings.enabled)}
                      trackColor={{ false: "#d1d5db", true: "#4f46e5" }}
                      thumbColor={settings[item.id as keyof typeof settings] ? "#ffffff" : "#f4f3f4"}
                      ios_backgroundColor="#d1d5db"
                    />
                  </View>
                </View>
              ))}
            </View>
          </Animated.View>
        </ScrollView>
      </SafeAreaView>
    </>
  );
};

export default NotificationsScreen;
