import { View, ScrollView, Text, TouchableOpacity, ActivityIndicator } from "react-native";
import React, { useState } from "react";
import { SafeAreaView, useSafeAreaInsets } from "react-native-safe-area-context";
import { Stack, useNavigation } from "expo-router";
import ProfilePicture from "../../components/Profile/ProfilePicture";
import { Ionicons } from "@expo/vector-icons";
import DateTimePicker from "@react-native-community/datetimepicker";
import * as ImagePicker from "expo-image-picker";
import { useAppDispatch, useAppSelector } from "../../reduxStore/hooks";
import { RootState } from "../../reduxStore/store";
import { showToast } from "../../lib/utils/showToast";
import { updateUserProfile } from "../../api/userService";
import { updateUser } from "../../reduxStore/userSlice";
import { format } from "date-fns";
import FormField from "../../components/Common/FormField";
import PrimaryButton from "../../components/Buttons/PrimaryButton";
import CoverPhotoEditor from "../../components/Profile/CoverPhotoEditor";
import Animated, { FadeIn } from "react-native-reanimated";
import { uploadFileToFirebase } from "../../services/firebaseStorageService";
import { LANGUAGE_OPTIONS } from "../../constants/ProfileConstants";
import { validateUserProfile } from "../../lib/validation/userValidation";

const ProfileEditPage = () => {
  const { user } = useAppSelector((state: RootState) => state.user);
  const dispatch = useAppDispatch();
  const [loading, setLoading] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();
  const [validationErrors, setValidationErrors] = useState<Record<string, string> | null>(null);

  const [formData, setFormData] = useState({
    firstName: user?.firstName || "",
    lastName: user?.lastName || "",
    phone: user?.phone || "",
    profilePic: user?.profilePic || "",
    profile: {
      languages: user?.profile?.languages || [],
      coverPic: user?.profile?.coverPic || "",
      bio: user?.profile?.bio || "",
      dob: user?.profile?.dob || new Date().toISOString(),
      gender: user?.profile?.gender || "male",
      preferences: {
        ...(user?.profile?.preferences || {}),
        showBadges: user?.profile?.preferences?.showBadges !== false,
      },
      instagramProfile: user?.profile?.instagramProfile || "",
    },
  });

  const handleInputChange = (field: string, value: string | boolean) => {
    if (field.includes(".")) {
      const parts = field.split(".");
      if (parts.length === 2) {
        const [parent, child] = parts;
        setFormData((prev) => ({
          ...prev,
          [parent]: {
            ...(typeof prev[parent as keyof typeof prev] === "object"
              ? (prev[parent as keyof typeof prev] as object)
              : {}),
            [child]: value,
          },
        }));
      } else if (parts.length === 3) {
        const [parent, child, grandchild] = parts;
        setFormData((prev) => {
          const parentObj = prev[parent as keyof typeof prev];
          return {
            ...prev,
            [parent]: {
              ...(typeof parentObj === "object" ? (parentObj as object) : {}),
              [child]: {
                ...(typeof parentObj === "object" && parentObj !== null ? (parentObj as any)[child] || {} : {}),
                [grandchild]: value,
              },
            },
          };
        });
      }
    } else {
      setFormData((prev) => ({
        ...prev,
        [field]: value,
      }));
    }
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(false);
    if (selectedDate) {
      handleInputChange("profile.dob", selectedDate.toISOString());
    }
  };

  const handleProfilePicChange = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      console.log(status, "status of media library permissions");

      if (status !== "granted") {
        showToast("error", "Permission to access media library is required!");
        return;
      }
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        // Show loading indicator
        setLoading(true);

        // Upload to Firebase Storage
        const downloadUrl = await uploadFileToFirebase(result.assets[0].uri, "profile-pictures");

        if (downloadUrl) {
          handleInputChange("profilePic", downloadUrl);
          showToast("success", "Profile picture uploaded successfully");
        } else {
          showToast("error", "Failed to upload profile picture");
        }
      }
    } catch (error) {
      console.error("Error picking/uploading image:", error);
      showToast("error", "Failed to upload image");
    } finally {
      setLoading(false);
    }
  };

  const handleCoverPhotoChange = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [3, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        // Show loading indicator
        setLoading(true);

        // Upload to Firebase Storage
        const downloadUrl = await uploadFileToFirebase(result.assets[0].uri, "cover-photos");

        if (downloadUrl) {
          handleInputChange("profile.coverPic", downloadUrl);
          showToast("success", "Cover photo uploaded successfully");
        } else {
          showToast("error", "Failed to upload cover photo");
        }
      }
    } catch (error) {
      console.error("Error picking/uploading image:", error);
      showToast("error", "Failed to upload image");
    } finally {
      setLoading(false);
    }
  };

  const handleLanguageToggle = (language: string) => {
    const currentLanguages = [...(formData?.profile?.languages || [])];

    if (currentLanguages.includes(language)) {
      // Remove language if already selected
      const updatedLanguages = currentLanguages.filter((lang) => lang !== language);
      setFormData((prev) => ({
        ...prev,
        profile: {
          ...prev.profile,
          languages: updatedLanguages,
        },
      }));
    } else {
      // Add language if not selected
      const updatedLanguages = [...currentLanguages, language];
      setFormData((prev) => ({
        ...prev,
        profile: {
          ...prev.profile,
          languages: updatedLanguages,
        },
      }));
    }
  };

  const handleSubmit = async () => {
    try {
      // Validate form data before submitting
      const validation = validateUserProfile(formData);

      if (!validation.success) {
        setValidationErrors(validation.errors);
        // Scroll to the first error
        if (validation.errors) {
          const firstErrorKey = Object.keys(validation.errors)[0];
          showToast("error", validation.errors[firstErrorKey]);
        }
        return;
      }

      // Clear any previous validation errors
      setValidationErrors(null);

      setLoading(true);
      const updatedData = await updateUserProfile(formData);

      if (updatedData && user) {
        dispatch(
          updateUser({
            ...user,
            firstName: formData.firstName,
            lastName: formData.lastName,
            phone: formData.phone,
            profilePic: formData.profilePic,
            profile: {
              ...user.profile,
              languages: formData?.profile.languages,
              coverPic: formData.profile.coverPic,
              bio: formData.profile.bio,
              dob: formData.profile.dob,
              gender: formData.profile.gender,
              preferences: {
                ...(user.profile?.preferences || {}),
                showBadges: formData.profile.preferences.showBadges,
              },
              instagramProfile: formData.profile.instagramProfile,
            },
          })
        );
      }

      showToast("success", "Profile updated successfully!");

      // Check if we need to return to a hangout
      const navigationState = navigation.getState();
      const previousScreen = navigationState?.routes[navigationState?.routes.length - 2]?.name;
      if (previousScreen && previousScreen.includes("hangout-details")) {
        navigation.goBack();
      }
    } catch (error: any) {
      console.error("Error updating profile:", error.response?.data || error.message);

      // Handle backend validation errors
      if (error.response?.data?.details) {
        const backendErrors = error.response.data.details;
        setValidationErrors(backendErrors);

        // Show the first error as a toast
        const firstErrorKey = Object.keys(backendErrors)[0];
        showToast("error", backendErrors[firstErrorKey]);
      } else {
        showToast("error", "Failed to update profile");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Stack.Screen options={{ headerShown: false, presentation: "card" }} />
      <SafeAreaView className="flex-1 bg-white">
        <ScrollView className="flex-1">
          {/* Cover Photo */}
          <View className="h-40 relative">
            <TouchableOpacity
              className="p-2 bg-black/20 rounded-full items-center justify-center absolute top-4 left-4 z-50"
              onPress={() => navigation.goBack()}
            >
              <Ionicons name="chevron-back" size={20} color="white" />
            </TouchableOpacity>
            <CoverPhotoEditor imageUrl={formData.profile.coverPic} onPress={handleCoverPhotoChange} />
          </View>

          <Animated.View entering={FadeIn.duration(400)} className="px-5 -mt-16">
            {/* Profile Picture */}
            <ProfilePicture imageUrl={formData.profilePic} onPress={handleProfilePicChange} />

            <View className="gap-y-4">
              {/* Basic Info Section */}
              <View className="bg-white rounded-xl border border-gray-100 p-4 shadow-sm">
                <Text className="text-lg font-semibold mb-4">Basic Information</Text>

                <FormField
                  label="First Name"
                  field="firstName"
                  value={formData.firstName}
                  onChangeText={(text) => handleInputChange("firstName", text)}
                  autoCapitalize="words"
                  error={validationErrors?.firstName}
                />

                <FormField
                  label="Last Name"
                  field="lastName"
                  value={formData.lastName}
                  onChangeText={(text) => handleInputChange("lastName", text)}
                  autoCapitalize="words"
                  containerStyle={{ marginTop: 12 }}
                  error={validationErrors?.lastName}
                />

                <FormField
                  label="Phone Number"
                  field="phone"
                  value={formData.phone}
                  onChangeText={(text) => handleInputChange("phone", text)}
                  keyboardType="phone-pad"
                  containerStyle={{ marginTop: 12 }}
                  error={validationErrors?.phone}
                />
                <FormField
                  label="Instagram Profile Link"
                  field="profile.instagramProfile"
                  value={formData.profile.instagramProfile}
                  onChangeText={(text) => handleInputChange("profile.instagramProfile", text)}
                  containerStyle={{ marginTop: 12 }}
                  error={validationErrors?.["profile.instagramProfile"]}
                  placeholder="https://www.instagram.com/yourusername/"
                />
              </View>

              {/* Profile Details Section */}
              <View className="bg-white rounded-xl border border-gray-100 p-4 shadow-sm">
                <Text className="text-lg font-semibold mb-4">Profile Details</Text>

                {/* Date of Birth */}
                <View className="mb-4">
                  <Text className="text-sm font-medium text-gray-700 mb-1">Date of Birth</Text>
                  <TouchableOpacity
                    className="border border-gray-300 rounded-lg p-3 flex-row justify-between items-center"
                    onPress={() => setShowDatePicker(true)}
                  >
                    <Text>
                      {formData.profile.dob ? format(new Date(formData.profile.dob), "MMMM d, yyyy") : "Select date"}
                    </Text>
                    <Ionicons name="calendar-outline" size={20} color="#6b7280" />
                  </TouchableOpacity>
                  {showDatePicker && (
                    <DateTimePicker
                      value={formData.profile.dob ? new Date(formData.profile.dob) : new Date()}
                      mode="date"
                      display="default"
                      onChange={handleDateChange}
                      maximumDate={new Date()}
                    />
                  )}
                </View>

                {/* Gender */}
                <View className="mb-4">
                  <Text className="text-sm font-medium text-gray-700 mb-1">Gender</Text>
                  <View className="flex-row flex-wrap">
                    {["male", "female", "other", "prefer not to say"].map((gender) => (
                      <TouchableOpacity
                        key={gender}
                        className={`mr-2 mb-2 px-4 py-2 rounded-full border ${
                          formData.profile.gender === gender ? "bg-button/10 border-button" : "border-gray-300"
                        }`}
                        onPress={() => handleInputChange("profile.gender", gender)}
                      >
                        <Text className={formData.profile.gender === gender ? "text-button" : "text-gray-700"}>
                          {gender.charAt(0).toUpperCase() + gender.slice(1).replace("-", " ")}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>

                {/* Bio */}
                <View>
                  <FormField
                    field="profile.bio"
                    label="Bio"
                    value={formData.profile.bio}
                    onChangeText={(text) => handleInputChange("profile.bio", text)}
                    multiline
                    numberOfLines={4}
                    textAlignVertical="top"
                    placeholder="Tell us about yourself..."
                    error={validationErrors?.["profile.bio"]}
                  />
                </View>
              </View>

              {/* Languages Section */}
              <View className="bg-white rounded-xl border border-gray-100 p-4 shadow-sm">
                <Text className="text-lg font-semibold mb-4">Languages</Text>

                <View className="flex-row flex-wrap">
                  {LANGUAGE_OPTIONS.map((language) => (
                    <TouchableOpacity
                      key={language}
                      className={`mr-2 mb-2 px-4 py-2 rounded-full border ${
                        formData?.profile.languages?.includes(language)
                          ? "bg-button/10 border-button"
                          : "border-gray-300"
                      }`}
                      onPress={() => handleLanguageToggle(language)}
                    >
                      <Text
                        className={formData?.profile.languages?.includes(language) ? "text-button" : "text-gray-700"}
                      >
                        {language}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              {/* Add padding at the bottom to ensure content isn't hidden behind the floating button */}
              <View className="h-20" />
            </View>
          </Animated.View>
        </ScrollView>

        {/* Floating Save Button */}
        <View
          style={{
            position: "absolute",
            bottom: 0,
            left: 0,
            right: 0,
            backgroundColor: "white",
            paddingHorizontal: 20,
            paddingTop: 15,
            paddingBottom: insets.bottom + 10,
            borderTopWidth: 1,
            borderTopColor: "#f1f5f9",
            shadowColor: "#000",
            shadowOffset: { width: 0, height: -3 },
            shadowOpacity: 0.1,
            shadowRadius: 3,
            elevation: 5,
            zIndex: 100,
          }}
        >
          <PrimaryButton
            buttonText={loading ? "Updating..." : "Save Changes"}
            onPressHandler={handleSubmit}
            disabled={loading}
          />
          {loading && <ActivityIndicator size="small" color="#4f46e5" style={{ marginTop: 10 }} />}
        </View>
      </SafeAreaView>
    </>
  );
};

export default ProfileEditPage;
