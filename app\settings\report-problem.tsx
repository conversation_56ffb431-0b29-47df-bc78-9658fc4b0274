import React, { useState } from "react";
import { View, Text, ScrollView, TextInput, TouchableOpacity, Alert } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import PageHeader from "../../components/Common/PageHeader";
import { showToast } from "../../lib/utils/showToast";
import { Stack } from "expo-router";
import { supportService } from "../../api/supportService";

const ReportProblemScreen = () => {
  const [problemType, setProblemType] = useState("");
  const [description, setDescription] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const problemTypes = [
    "App Crash",
    "Login Issues",
    "Payment Problems",
    "Booking Issues",
    "Account Problems",
    "Feature Request",
    "Other",
  ];

  // Map UI problem types to API categories
  const mapProblemTypeToCategory = (type: string): string => {
    const mapping: Record<string, string> = {
      "App Crash": "app_crash",
      "Login Issues": "login_issues",
      "Payment Problems": "payment",
      "Booking Issues": "booking_issues",
      "Account Problems": "account",
      "Feature Request": "feature_request",
      Other: "other",
    };
    return mapping[type] || "bug";
  };

  const handleSubmit = async () => {
    if (!problemType) {
      showToast("error", "Please select a problem type");
      return;
    }

    if (!description || description.length < 10) {
      showToast("error", "Please provide a detailed description");
      return;
    }

    setIsSubmitting(true);

    try {
      await supportService.submitSupportRequest({
        subject: `Problem Report: ${problemType}`,
        message: description,
        category: mapProblemTypeToCategory(problemType) as any,
        priority: "normal",
      });

      // Success
      showToast("success", "Your report has been submitted");
      setProblemType("");
      setDescription("");

      Alert.alert(
        "Report Submitted",
        "Thank you for your feedback. Our team will review your report and get back to you if needed.",
        [{ text: "OK" }]
      );
    } catch (error) {
      console.error("Error submitting report:", error);
      showToast("error", "Failed to submit report");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <SafeAreaView className="flex-1 bg-white">
        <PageHeader title="Report a Problem" color="black" />
        <ScrollView className="flex-1 p-5">
          <Text className="text-base mb-6">
            We're sorry you're experiencing an issue. Please provide details below so we can help resolve it.
          </Text>

          <View className="mb-6">
            <Text className="text-base font-bold mb-2">What type of problem are you experiencing?</Text>
            <View className="flex-row flex-wrap">
              {problemTypes.map((type, index) => (
                <TouchableOpacity
                  key={index}
                  className={`mr-2 mb-2 px-3 py-2 rounded-full ${problemType === type ? "bg-secondary" : "bg-gray-50"}`}
                  onPress={() => setProblemType(type)}
                >
                  <Text className={`text-sm ${problemType === type ? "text-white" : "text-gray-700"}`}>{type}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View className="mb-6">
            <Text className="text-base font-bold mb-2">Describe the problem</Text>
            <TextInput
              className="bg-gray-50 rounded-lg p-3 text-base min-h-[120px]"
              placeholder="Please provide as much detail as possible..."
              multiline
              textAlignVertical="top"
              value={description}
              onChangeText={setDescription}
            />
            <Text className="text-xs text-gray-500 mt-1">{description.length}/500 characters (minimum 10)</Text>
          </View>

          <TouchableOpacity
            className={`rounded-lg py-3 ${isSubmitting ? "bg-primary-300" : "bg-primary"}`}
            onPress={handleSubmit}
            disabled={isSubmitting}
          >
            <Text className="text-textColor text-center text-base font-bold">
              {isSubmitting ? "Submitting..." : "Submit Report"}
            </Text>
          </TouchableOpacity>
        </ScrollView>
      </SafeAreaView>
    </>
  );
};

export default ReportProblemScreen;
