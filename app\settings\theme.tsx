import React, { useState } from "react";
import { View, Text, TouchableOpacity, ScrollView } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import PageHeader from "../../components/Common/PageHeader";
import { useAppDispatch, useAppSelector } from "../../reduxStore/hooks";
import { RootState } from "../../reduxStore/store";
import { updateUserProfile } from "../../api/userService";
import { updateUser } from "../../reduxStore/userSlice";
import { showToast } from "../../lib/utils/showToast";
import Animated, { FadeIn } from "react-native-reanimated";

const ThemeScreen = () => {
  const { user } = useAppSelector((state: RootState) => state.user);
  const dispatch = useAppDispatch();
  const [loading, setLoading] = useState(false);

  // Default to light if not set
  const currentTheme = user?.profile?.preferences?.theme || "light";
  const [selectedTheme, setSelectedTheme] = useState(currentTheme);

  const themes = [
    { id: "light", name: "Light", icon: "sunny-outline", color: "#f3f4f6" },
    { id: "dark", name: "Dark", icon: "moon-outline", color: "#1f2937" },
    { id: "system", name: "System Default", icon: "phone-portrait-outline", color: "#9ca3af" },
  ];

  const handleThemeChange = async (themeId: string) => {
    if (themeId === selectedTheme) return;

    setSelectedTheme(themeId);
    setLoading(true);

    try {
      const preferences = {
        ...(user?.profile?.preferences || {}),
        theme: themeId,
      };

      await updateUserProfile({
        profile: {
          preferences,
        },
      });

      if (user) {
        dispatch(
          updateUser({
            ...user,
            profile: {
              ...user.profile,
              preferences,
            },
          })
        );
      }

      showToast("success", "Theme updated successfully");
    } catch (error) {
      console.error("Error updating theme:", error);
      showToast("error", "Failed to update theme");
      setSelectedTheme(currentTheme);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />

      <SafeAreaView className="flex-1 bg-background">
        <PageHeader title="Theme" color="black" showIcon={true} />

        <ScrollView className="flex-1 pt-4">
          <Animated.View entering={FadeIn.duration(400)} className="px-4">
            <Text className="text-base font-body text-body-200 text-gray-700 mb-6">
              Choose your preferred app theme. Changes will apply immediately.
            </Text>

            <View className="bg-white rounded-xl border border-slate-200 overflow-hidden">
              {themes.map((theme, index) => (
                <TouchableOpacity
                  key={theme.id}
                  className={`flex-row items-center py-4 px-4 ${
                    index < themes.length - 1 ? "border-b border-slate-100" : ""
                  }`}
                  onPress={() => handleThemeChange(theme.id)}
                  disabled={loading}
                >
                  <View
                    className="w-10 h-10 rounded-full items-center justify-center mr-3"
                    style={{ backgroundColor: `${theme.color}20` }}
                  >
                    <Ionicons name={theme.icon as any} size={20} color={theme.color} />
                  </View>

                  <Text className="flex-1 text-base font-body text-body-400">{theme.name}</Text>

                  {selectedTheme === theme.id && <Ionicons name="checkmark-circle" size={24} color="#4f46e5" />}
                </TouchableOpacity>
              ))}
            </View>
          </Animated.View>
        </ScrollView>
      </SafeAreaView>
    </>
  );
};

export default ThemeScreen;
