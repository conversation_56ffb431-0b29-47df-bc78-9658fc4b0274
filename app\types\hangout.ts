import { Activity } from "../../components/Hangouts/HangoutActivityItem";

export interface Hangout {
  _id: string;
  title: string;
  description: string;
  category: string;
  subcategory: string;
  date: string;
  location: {
    type: string;
    address: string;
    coordinates: number[];
    placeName?: string;
    placeId?: string;
  };
  price: number;
  status: string;
  organizer: {
    _id: string;
    firstName: string;
    lastName: string;
    profilePic?: string;
  };
  organizerInstagramProfile?: string;
  participants: string[];
  maxParticipants: number;
  isPaid: boolean;
  images: string[];
  videos: string[];
  tags: string[];
  duration: number;
  ratings: Array<{
    rating: number;
    user: {
      _id: string;
      firstName: string;
      lastName: string;
    };
  }>;
  socialLinks: string[];
  createdAt: string;
  updatedAt: string;
  featuredImage?: string;
  activities?: Activity[];
  cancellationPolicy?: string;
  viewsCount?: number;
}

export interface HangoutParticipant {
  _id: string;
  user: {
    _id: string;
    profilePic: string;
  };
  status: string;
}

export interface userBookingStatusResponse {
  isBooked: boolean;
  status: string;
}

export interface HangoutDetailsResponse {
  hangout: Hangout;
  participants: HangoutParticipant[];
  userBookingStatus: userBookingStatusResponse;
}

export interface HangoutFilters {
  page?: number;
  limit?: number;
  filter?: string;
  search?: string;
  category?: string;
  subcategory?: string;
  startDate?: string;
  endDate?: string;
  location?: string;
  maxDistance?: number;
  minPrice?: number;
  maxPrice?: number;
  status?: string;
  organizerId?: string;
  isPaid?: boolean;
  minRating?: number;
  maxParticipants?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

export interface HangoutResponse {
  success: boolean;
  data: Hangout[];
  pagination: {
    total: number;
    page: number;
    pages: number;
  };
}

export interface JoinHangoutResponse {
  message: string;
  hangout: Hangout;
  participant: {
    _id: string;
    hangout: string;
    user: string;
    status: string;
    joinedAt: string;
  };
  qrCode: string;
}
