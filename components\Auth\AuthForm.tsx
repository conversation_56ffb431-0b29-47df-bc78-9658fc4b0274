import { View, Text, Keyboard, TouchableWithoutFeedback, StyleSheet } from "react-native";
import React, { useState } from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import PrimaryButton from "../Buttons/PrimaryButton";
import { <PERSON> } from "expo-router";
import HorizontalLine from "../Common/HorizontalLine";
import { AuthFormProps } from "../../lib/types/OnBoarding/onBoardingTypes";
import { signInWithEmail, signUpWithEmail } from "../../services/auth/AuthService";
import { useAppDispatch } from "../../reduxStore/hooks";
import TextFieldInput from "./TextFieldInput";
import PasswordInput from "./PasswordInput";
import PhoneInput from "./PhoneInput";
import { DEFAULT_COUNTRY_CODE } from "../../constants/AuthConstants";
import { LinearGradient } from "expo-linear-gradient";
import { Ionicons } from "@expo/vector-icons";
import useGoogleAuth from "../../hooks/useGoogleAuth";

const AuthForm: React.FC<AuthFormProps> = ({ buttonText, linkText, linkHref, isSignUp = false }) => {
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    confirmPassword: "",
    countryCode: DEFAULT_COUNTRY_CODE,
    phoneNumber: "",
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [generalError, setGeneralError] = useState("");
  const [loading, setLoading] = useState(false);
  const { loading: authLoading } = useGoogleAuth();
  const dispatch = useAppDispatch();

  const handleInputChange = (name: string, value: string) => {
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
    // Clear error for this field when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePassword = (password: string): boolean => {
    return password.length >= 6;
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Email validation
    if (!formData.email) {
      newErrors.email = "Email is required";
    } else if (!validateEmail(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = "Password is required";
    } else if (!validatePassword(formData.password)) {
      newErrors.password = "Password must be at least 6 characters";
    }

    // Sign up specific validations
    if (isSignUp) {
      if (!formData.firstName) {
        newErrors.firstName = "First name is required";
      }

      if (!formData.lastName) {
        newErrors.lastName = "Last name is required";
      }

      if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = "Passwords do not match";
      }

      // Phone validation
      if (!formData.phoneNumber) {
        newErrors.phoneNumber = "Phone number is required";
      } else if (!/^\d{10}$/.test(formData.phoneNumber)) {
        newErrors.phoneNumber = "Please enter a valid 10-digit phone number";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleAuth = async () => {
    Keyboard.dismiss();
    setGeneralError("");

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      if (isSignUp) {
        await signUpWithEmail(
          formData.firstName,
          formData.lastName,
          formData.email,
          formData.password,
          `${formData.countryCode}${formData.phoneNumber}`,
          dispatch
        );
      } else {
        await signInWithEmail(formData.email, formData.password);
      }
    } catch (err: any) {
      // Handle specific Firebase auth errors
      const errorCode = err.code;
      let errorMessage = "An unexpected error occurred. Please try again.";

      if (
        errorCode === "auth/user-not-found" ||
        errorCode === "auth/wrong-password" ||
        errorCode === "auth/invalid-credential"
      ) {
        errorMessage = "Invalid email or password";
      } else if (errorCode === "auth/email-already-in-use") {
        errorMessage = "Email is already in use";
      } else if (errorCode === "auth/weak-password") {
        errorMessage = "Password is too weak";
      } else if (errorCode === "auth/invalid-email") {
        errorMessage = "Invalid email address";
      } else if (errorCode === "auth/network-request-failed") {
        errorMessage = "Network error. Please check your connection";
      } else if (errorCode === "auth/too-many-requests") {
        errorMessage = "Too many failed login attempts. Please try again later";
      } else if (errorCode === "auth/user-disabled") {
        errorMessage = "This account has been disabled";
      } else if (err.response?.data?.details?.code === "auth/phone-number-already-exists") {
        errorMessage = "This phone number is already registered. Please use a different number.";
      } else if (err.message) {
        errorMessage = err.message;
      }

      console.log("Auth error:", errorCode, err.message);
      setGeneralError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <LinearGradient colors={["#f5f5f5", "#ffffff"]} style={styles.container}>
        <SafeAreaView className="items-center w-full h-full p-6">
          {/* Creative Logo Design */}
          <View className="mb-8 mt-6 items-center">
            <View className="items-center">
              <View className="flex-row items-center">
                <Text className="text-4xl font-heading-bold text-textColor">Logout</Text>
                <Text className="text-4xl font-heading-bold text-primary">loud</Text>
              </View>
              <View className="bg-primary h-1 w-32 rounded-full mt-1" />
            </View>
          </View>

          {/* Card Container for Form */}
          <View className="w-full bg-white rounded-3xl p-6 shadow-md border border-gray-100">
            {/* Form Title */}
            <View className="mb-6 border-l-4 border-primary pl-3">
              <Text className="text-2xl font-heading text-textColor">
                {isSignUp ? "Create Account" : "Welcome Back"}
              </Text>
              <Text className="text-gray-400 font-body-light text-sm">
                {isSignUp ? "Join our community" : "Sign in to continue"}
              </Text>
            </View>

            {/* Form Fields */}
            <View className="w-full">
              {isSignUp && (
                <>
                  <View className="flex-row justify-between w-full gap-x-2">
                    <View className="flex-1">
                      <TextFieldInput
                        value={formData.firstName}
                        onChangeText={(text) => handleInputChange("firstName", text)}
                        placeholder="First Name"
                        error={errors.firstName}
                      />
                    </View>
                    <View className="flex-1">
                      <TextFieldInput
                        value={formData.lastName}
                        onChangeText={(text) => handleInputChange("lastName", text)}
                        placeholder="Last Name"
                        error={errors.lastName}
                      />
                    </View>
                  </View>

                  <View className="mb-4">
                    <PhoneInput
                      phoneNumber={formData.phoneNumber}
                      countryCode={formData.countryCode}
                      onPhoneChange={(text) => handleInputChange("phoneNumber", text)}
                      onCountryCodeChange={(code) => handleInputChange("countryCode", code)}
                      error={errors.phoneNumber}
                    />
                  </View>
                </>
              )}

              <View className="mb-4">
                <TextFieldInput
                  value={formData.email}
                  onChangeText={(text) => handleInputChange("email", text)}
                  placeholder="Enter your email"
                  keyboardType="email-address"
                  autoCapitalize="none"
                  error={errors.email}
                  className="mb-0"
                />
              </View>

              <View className="mb-2">
                <PasswordInput
                  value={formData.password}
                  onChangeText={(text) => handleInputChange("password", text)}
                  placeholder="Password"
                  error={errors.password}
                />
              </View>

              {isSignUp && (
                <View className="mt-4 mb-2">
                  <PasswordInput
                    value={formData.confirmPassword}
                    onChangeText={(text) => handleInputChange("confirmPassword", text)}
                    placeholder="Confirm Password"
                    error={errors.confirmPassword}
                  />
                </View>
              )}
            </View>

            {generalError ? (
              <View className="bg-red-50 p-3 rounded-xl mt-4 flex-row items-center">
                <Ionicons name="alert-circle" size={18} color="#ef4444" />
                <Text className="text-red-500 ml-2 flex-1">{generalError}</Text>
              </View>
            ) : null}

            {/* Submit Button */}
            <View className="mt-6">
              <PrimaryButton
                onPressHandler={handleAuth}
                buttonText={loading || authLoading ? "Loading..." : buttonText}
                borderRadius={"xl"}
                disabled={loading || authLoading}
                height={50}
              />
            </View>
          </View>

          {/* Footer */}
          <View className="w-full mt-6">
            <HorizontalLine text={"or"} textColor={"text-gray-400"} linebg={"bg-gray-300"} />
            <Link href={linkHref} className="text-center text-sm mt-4 text-gray-400 font-subheading">
              {linkText}
            </Link>
          </View>
        </SafeAreaView>
      </LinearGradient>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: "100%",
    height: "100%",
  },
});

export default AuthForm;
