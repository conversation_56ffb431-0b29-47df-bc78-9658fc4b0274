import React, { useState, useEffect } from "react";
import { View, Text, Alert, TouchableOpacity } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { LinearGradient } from "expo-linear-gradient";
import { Ionicons } from "@expo/vector-icons";
import { sendEmailVerification, reload, signOut } from "firebase/auth";
import { auth } from "../../config/firebase";
import PrimaryButton from "../Buttons/PrimaryButton";
import { useAuth } from "../../contexts/AuthContext";
import { useRouter } from "expo-router";
import { StatusBar } from "expo-status-bar";
import PageHeader from "../Common/PageHeader";

const EmailVerificationScreen: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [resendLoading, setResendLoading] = useState(false);
  const [lastSentTime, setLastSentTime] = useState<number | null>(null);
  const [countdown, setCountdown] = useState(0);
  const { firebaseUser, refreshAuthState } = useAuth();
  const router = useRouter();

  // Countdown timer for resend button
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (countdown > 0) {
      interval = setInterval(() => {
        setCountdown((prev) => prev - 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [countdown]);

  const handleResendVerification = async () => {
    if (!firebaseUser) return;

    try {
      setResendLoading(true);
      await sendEmailVerification(firebaseUser);
      setLastSentTime(Date.now());
      setCountdown(60); // 60 second cooldown
      Alert.alert("Email Sent", "Verification email has been sent to your email address.", [{ text: "OK" }]);
    } catch (error: any) {
      console.error("Error sending verification email:", error);
      Alert.alert("Error", "Failed to send verification email. Please try again.", [{ text: "OK" }]);
    } finally {
      setResendLoading(false);
    }
  };

  const handleCheckVerification = async () => {
    if (!firebaseUser) return;

    try {
      setLoading(true);
      // Reload user to get latest emailVerified status
      await reload(firebaseUser);

      if (firebaseUser.emailVerified) {
        // Email is now verified, refresh auth state to trigger navigation
        console.log("Email verified successfully");
        await refreshAuthState();
      } else {
        Alert.alert(
          "Email Not Verified",
          "Please check your email and click the verification link before continuing.",
          [{ text: "OK" }]
        );
      }
    } catch (error: any) {
      console.error("Error checking verification:", error);
      Alert.alert("Error", "Failed to check verification status. Please try again.", [{ text: "OK" }]);
    } finally {
      setLoading(false);
    }
  };

  const handleChangeEmail = async () => {
    Alert.alert(
      "Change Email Address",
      "Are you sure you want to go back and sign up with a different email? You'll need to create a new account.",
      [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Yes, Change Email",
          style: "destructive",
          onPress: async () => {
            try {
              // Sign out the current user to allow them to sign up with a different email
              await signOut(auth);
              console.log("User signed out, redirecting to signup");
              // Navigate back to signup screen
              router.replace("/(auth)/sign-up");
            } catch (error) {
              console.error("Error signing out:", error);
              Alert.alert("Error", "Failed to sign out. Please try again.", [{ text: "OK" }]);
            }
          },
        },
      ]
    );
  };

  const canResend = countdown === 0;

  return (
    <LinearGradient colors={["#f5f5f5", "#ffffff"]} className="flex-1">
      <SafeAreaView className="flex-1 px-6 justify-center">
        <StatusBar style="dark" />
        {/* Icon */}
        <View className="items-center mb-8">
          <View className="w-24 h-24 bg-primary/10 rounded-full items-center justify-center mb-4">
            <Ionicons name="mail-outline" size={48} color="#D72638" />
          </View>

          {/* Title */}
          <Text className="text-3xl font-heading-bold text-textColor text-center mb-2">Verify Your Email</Text>

          {/* Subtitle */}
          <Text className="text-gray-500 text-center text-base leading-6">
            We've sent a verification link to{"\n"}
            <Text className="font-semibold text-textColor">{firebaseUser?.email}</Text>
          </Text>
        </View>

        {/* Instructions */}
        <View className="bg-blue-50 p-4 rounded-xl mb-6">
          <Text className="text-blue-800 text-sm leading-5">
            📧 Check your email inbox (and spam folder) for a verification link.{"\n"}
            🔗 Click the link to verify your email address.{"\n"}✅ Return here and tap "I've Verified My Email" to
            continue.
          </Text>
        </View>

        {/* Action Buttons */}
        <View className="space-y-4">
          {/* Check Verification Button */}
          <View>
            <PrimaryButton
              buttonText={loading ? "Checking..." : "I've Verified My Email"}
              onPressHandler={handleCheckVerification}
              disabled={loading || resendLoading}
              borderRadius="xl"
              height={50}
            />
          </View>

          {/* Resend Button */}
          <View>
            <PrimaryButton
              buttonText={
                resendLoading ? "Sending..." : canResend ? "Resend Verification Email" : `Resend in ${countdown}s`
              }
              onPressHandler={handleResendVerification}
              disabled={!canResend || resendLoading || loading}
              bgColor="bg-gray-100"
              borderRadius="xl"
              height={50}
            />
          </View>
        </View>

        {/* Change Email Button */}
        <View className="mt-6">
          <TouchableOpacity
            onPress={handleChangeEmail}
            disabled={loading || resendLoading}
            className={`bg-red-50 border border-red-200 py-3 rounded-xl items-center h-[50px] justify-center ${
              loading || resendLoading ? "opacity-50" : ""
            }`}
          >
            <Text className="text-red-600 font-medium">Wrong Email? Sign Up Again</Text>
          </TouchableOpacity>
        </View>

        {/* Help Text */}
        <View className="mt-6">
          <Text className="text-gray-400 text-center text-sm">
            Having trouble? Check your spam folder or contact support.
          </Text>
        </View>
      </SafeAreaView>
    </LinearGradient>
  );
};

export default EmailVerificationScreen;
