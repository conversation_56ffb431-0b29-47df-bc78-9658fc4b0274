import { Text, TouchableOpacity, Image } from "react-native";
import React from "react";
import { PrimaryButtonProps } from "../../lib/types/commonTypes";

const PrimaryButton: React.FC<PrimaryButtonProps> = ({
  buttonText,
  onPressHandler,
  disabled = false,
  bgColor,
  icon,
  borderRadius,
  height = 45,
  width,
}) => {
  return (
    <TouchableOpacity
      onPress={onPressHandler}
      className={`${bgColor || "bg-primary"} py-3 rounded-${
        borderRadius || "xl"
      } items-center flex-row justify-center gap-x-2 h-[${height}px] ${width && `w-[${width}px]`}`}
      disabled={disabled}
    >
      {icon && <Image source={icon} resizeMode="contain" className="w-[20px] h-[20px]" />}
      <Text className="text-center font-subheading">{buttonText}</Text>
    </TouchableOpacity>
  );
};

export default PrimaryButton;
