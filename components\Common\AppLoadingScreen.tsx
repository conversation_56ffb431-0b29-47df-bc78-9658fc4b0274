import React from "react";
import { View, Text } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { StatusBar } from "expo-status-bar";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  withSequence,
  interpolateColor,
} from "react-native-reanimated";

const AppLoadingScreen: React.FC = () => {
  const pulse = useSharedValue(1);
  const flicker = useSharedValue(0);

  React.useEffect(() => {
    // Infinite pulse animation (scaling the whole logo)
    pulse.value = withRepeat(
      withSequence(withTiming(1.1, { duration: 1000 }), withTiming(1, { duration: 1000 })),
      -1,
      true
    );

    // Flicker value toggles between 0 and 1
    flicker.value = withRepeat(withSequence(withTiming(1, { duration: 500 }), withTiming(0, { duration: 500 })), -1);
  }, []);

  // Entire logo pulse style
  const logoStyle = useAnimatedStyle(() => ({
    transform: [{ scale: pulse.value }],
  }));

  return (
    <View className="flex-1 bg-black justify-center items-center">
      <StatusBar style="light" />
      <SafeAreaView className="w-full h-full flex justify-center items-center">
        <Animated.View style={logoStyle} className="flex-row items-center">
          <Text className="text-white text-3xl font-heading">Logout</Text>
          <Animated.Text className="text-3xl font-heading text-primary">loud</Animated.Text>
        </Animated.View>
      </SafeAreaView>
    </View>
  );
};

export default AppLoadingScreen;
