import { Text } from "react-native";
import React from "react";
import { toTitleCase } from "../../lib/utils/commonUtils";
type ChipProps = {
  value: string;
  size?: "small" | "medium" | "large";
  color?: string;
  textColor?: string;
};

const Chip: React.FC<ChipProps> = ({ value, size = "small", color = "bg-secondary", textColor = "text-white" }) => {
  return (
    <Text
      className={`${color} ${textColor} text-[${
        size === "small" ? "8px" : size === "medium" ? "10px" : "12px"
      }] font-bold px-[6px] py-[1px] rounded-lg self-start`}
    >
      {toTitleCase(value.toLowerCase())}
    </Text>
  );
};

export default Chip;
