import React from "react";
import { View, Text, Image, TouchableOpacity, ImageBackground, Dimensions } from "react-native";
import Ionicons from "react-native-vector-icons/Ionicons";
import { LinearGradient } from "expo-linear-gradient";
import ParticipantAvatars from "./ParticipantAvatars";
import { formatAddress } from "../../lib/utils/commonUtils";

export type ContentType = "hangout" | "travel" | "room";

export interface Participant {
  id: number | string;
  uri: string;
}

export interface ContentCardProps {
  item: any;
  featured?: boolean;
  contentType?: ContentType;
  onPress?: () => void;
  participantsCount?: number;
}

const { width } = Dimensions.get("window");
const cardWidth = width * 0.85; // Exactly matching journey cards

const ContentCard = ({
  item,
  featured = false,
  contentType = "hangout",
  onPress,
  participantsCount,
}: ContentCardProps) => {
  // Get content-specific labels and icons
  const getContentDetails = () => {
    switch (contentType) {
      case "travel":
        return {
          dateLabel: "Departure",
          categoryIcon: "airplane-outline",
          categoryLabel: item.destination || "Travel",
          locationIcon: "navigate-outline",
          participantsLabel: "Travelers",
        };
      case "room":
        return {
          dateLabel: "Created",
          categoryIcon: "people-outline",
          categoryLabel: item.roomType || "Chat Room",
          locationIcon: "chatbubble-outline",
          participantsLabel: "Members",
        };
      case "hangout":
      default:
        return {
          dateLabel: "Event",
          categoryIcon: "pricetag-outline",
          categoryLabel: item.category || "Event",
          locationIcon: "location-outline",
          participantsLabel: "Joined",
        };
    }
  };

  const contentDetails = getContentDetails();
  const itemDate = new Date(item.date);
  const imageUrl = item.featuredImage || "https://picsum.photos/800/600";
  const price = item.price || 0;

  if (featured) {
    // Featured card (large with overlay text, matching featured journeys exactly)
    return (
      <TouchableOpacity style={{ width: cardWidth }} className="mr-4" onPress={onPress}>
        <View className="rounded-2xl overflow-hidden shadow-md" style={{ height: 250 }}>
          <Image
            source={typeof imageUrl === "string" ? { uri: imageUrl } : imageUrl}
            style={{ width: cardWidth, height: 250 }}
            resizeMode="cover"
          />

          {/* Date badge */}
          <View className="absolute top-3 right-3 bg-primary-200 rounded-lg p-2 items-center">
            <Text className="font-bold text-textColor">{itemDate.getDate()}</Text>
            <Text className="text-textColor text-xs">{itemDate.toLocaleString("default", { month: "short" })}</Text>
          </View>

          {/* Price badge */}
          <View className="absolute top-3 left-3 bg-black/50 px-3 py-1 rounded-full">
            <Text className="text-white font-body-medium">₹{price.toLocaleString()}</Text>
          </View>

          {/* Bottom info panel */}
          <LinearGradient colors={["transparent", "rgba(0,0,0,0.8)"]} className="absolute bottom-0 left-0 right-0 p-4">
            <Text className="font-heading text-headline-500 text-white mb-2" numberOfLines={1}>
              {item.title}
            </Text>

            <View className="flex-row justify-between">
              <View className="flex-row items-center">
                <Ionicons name={contentDetails.locationIcon} size={14} color="#FFFFFF" />
                <Text className="font-body text-body-200 text-gray-100 ml-1" numberOfLines={1}>
                  {item.location.placeName
                    ? item.location.placeName
                    : item.location?.address
                    ? formatAddress(item.location.address)
                    : item.location}
                </Text>
              </View>
            </View>

            <View className="flex-row items-center mt-1 justify-between">
              <View className="flex-row items-center">
                <Ionicons name="time-outline" size={12} color="white" />
                <Text className="text-white text-xs ml-1 font-body">
                  {itemDate.toLocaleString("default", { hour: "2-digit", minute: "2-digit" })}
                </Text>
              </View>

              {item.participants.length > 0 && (
                <View className="flex-row items-center">
                  <ParticipantAvatars avatars={item.participants} imageSize={18} leftGap={12} />
                  <Text className="text-white text-xs ml-1">{participantsCount || item.participants.length}+</Text>
                </View>
              )}
            </View>
          </LinearGradient>
        </View>
      </TouchableOpacity>
    );
  }

  // Regular card (horizontal layout matching upcoming journeys exactly)
  return (
    <TouchableOpacity
      className="flex-row p-3 mb-3 rounded-2xl overflow-hidden border-[1px] border-slate-200 active:bg-gray-50 relative"
      onPress={onPress}
    >
      {/* Bookmark icon */}
      {/* <TouchableOpacity className="absolute top-2 right-2 z-10">
        <Ionicons name="bookmark-outline" size={20} color="#6B7280" />
      </TouchableOpacity> */}

      <View className="rounded-xl w-[90px] h-[115px] relative">
        <Image
          source={typeof imageUrl === "string" ? { uri: imageUrl } : imageUrl}
          className="w-full h-full rounded-xl"
          resizeMode="cover"
        />
        <LinearGradient
          colors={["transparent", "rgba(0,0,0,0.5)"]}
          className="absolute bottom-0 left-0 right-0 p-2 rounded-xl"
        />
        <View className="absolute bottom-2 left-2 bg-black/60 rounded-lg px-2 py-1">
          <Text className="text-white font-body text-[10px]">
            {itemDate.getHours().toString().padStart(2, "0")}:{itemDate.getMinutes().toString().padStart(2, "0")}
          </Text>
        </View>
      </View>

      <View className="ml-3 flex-1 justify-between">
        <View>
          <Text className="font-heading text-textColor w-[80%]" numberOfLines={1}>
            {item.title}
          </Text>

          <View className="flex-row items-center mt-1">
            <Ionicons name={contentDetails.locationIcon} size={12} color="#D72638" />
            <Text className="text-gray-600 text-xs ml-1 font-body" numberOfLines={1}>
              {item.location.placeName
                ? item.location.placeName
                : item.location?.address
                ? formatAddress(item.location.address)
                : item.location}
            </Text>
          </View>

          <View className="flex-row items-center mt-2">
            <Ionicons name="calendar-outline" size={12} color="#6B7280" />
            <Text className="text-gray-500 font-body text-xs ml-1">
              {itemDate.toLocaleDateString("en-US", { month: "short", day: "numeric" })}
            </Text>
          </View>
        </View>

        <View className="flex-row justify-between items-center mt-3">
          {item.participants.length > 0 ? (
            <View className="flex-row items-center">
              <ParticipantAvatars avatars={item.participants} imageSize={16} leftGap={10} />
              <Text className="text-xs text-gray-500 ml-1">{participantsCount || item.participants.length}+</Text>
            </View>
          ) : (
            <View className="flex-row items-center">
              <Ionicons name={contentDetails.categoryIcon} size={12} color="#6B7280" />
              <Text className="text-xs text-gray-500 ml-1">{contentDetails.categoryLabel.toLowerCase()}</Text>
            </View>
          )}

          <Text className="text-primary-600 font-heading text-headline-300">₹{price.toLocaleString()}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default ContentCard;
