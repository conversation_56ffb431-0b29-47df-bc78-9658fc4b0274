import React, { useState } from "react";
import { View, Text, TouchableOpacity, ScrollView } from "react-native";
import { ExpandableTextProps } from "../../lib/types/commonTypes";

const ExpandableText: React.FC<ExpandableTextProps> = ({ content, maxLines = 3, textSize = 14 }) => {
  const [expanded, setExpanded] = useState<boolean>(false);

  return (
    <View>
      <ScrollView nestedScrollEnabled showsVerticalScrollIndicator={false}>
        <Text className={`text-[${textSize}px] text-white`} numberOfLines={expanded ? undefined : maxLines}>
          {content}
        </Text>
        <TouchableOpacity onPress={() => setExpanded(!expanded)}>
          <Text className="text-[#007BFF] font-bold">{expanded ? "See Less" : "See More"}</Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
};

export default ExpandableText;
