import React from "react";
import { TouchableOpacity, Text } from "react-native";

interface FilterButtonProps {
  title: string;
  isActive: boolean;
  onPress: () => void;
}

const FilterButton: React.FC<FilterButtonProps> = ({ title, isActive, onPress }) => {
  return (
    <TouchableOpacity
      className={`px-3 py-1.5 rounded-2xl mr-2 mb-2 ${isActive ? "bg-button" : "bg-background border border-gray-200"}`}
      onPress={onPress}
    >
      <Text className={`text-xs font-body-medium ${isActive ? "text-white" : "text-textColor/60"}`}>{title}</Text>
    </TouchableOpacity>
  );
};

export default FilterButton;
