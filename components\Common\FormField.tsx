import React from "react";
import { View, Text, TextInput, TextInputProps } from "react-native";

interface FormFieldProps extends TextInputProps {
  label: string;
  field: string;
  value: string;
  error?: string;
  containerStyle?: any;
}

const FormField: React.FC<FormFieldProps> = ({ label, field, value, error, containerStyle, ...props }) => {
  return (
    <View style={containerStyle}>
      <Text className="text-sm font-medium text-gray-700 mb-1">{label}</Text>
      <TextInput
        className={`border ${error ? "border-red-500" : "border-gray-300"} rounded-lg p-3 text-base`}
        value={value}
        {...props}
      />
      {error && <Text className="text-red-500 text-xs mt-1">{error}</Text>}
    </View>
  );
};

export default FormField;
