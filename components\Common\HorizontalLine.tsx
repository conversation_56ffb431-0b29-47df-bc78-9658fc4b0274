import { View, Text } from "react-native";
import React from "react";
import { HorizontalLineProps } from "../../lib/types/commonTypes";

const HorizontalLine: React.FC<HorizontalLineProps> = ({ text, textColor, linebg }) => {
  return (
    <View className={`flex-row items-center ${text && "gap-2"}`}>
      <View className={`flex-1 h-[1px] ${linebg || "bg-stone-500"}`} />
      {text && (
        <View>
          <Text className={`${textColor || "text-white"} text-center`}>{text}</Text>
        </View>
      )}
      <View className={`flex-1 h-[1px] ${linebg || "bg-stone-500"}`} />
    </View>
  );
};

export default HorizontalLine;
