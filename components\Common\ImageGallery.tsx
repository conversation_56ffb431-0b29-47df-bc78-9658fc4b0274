import React, { useState, useRef } from "react";
import { View, Image, TouchableOpacity, Modal, Dimensions, Animated } from "react-native";
import { Ionicons } from "@expo/vector-icons";

interface ImageGalleryProps {
  images: string[];
  dynamicSize?: boolean;
  onImagePress?: (index: number) => void;
}

const ImageGallery: React.FC<ImageGalleryProps> = ({ images, dynamicSize = false, onImagePress }) => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.3)).current;
  const windowWidth = Dimensions.get("window").width;
  const windowHeight = Dimensions.get("window").height;

  const handleImagePress = (index: number) => {
    setSelectedImage(images[index]);
    setSelectedImageIndex(index);

    // Reset animations
    scaleAnim.setValue(0.3);
    fadeAnim.setValue(0);

    // Start animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();

    onImagePress?.(index);
  };

  const handleClose = () => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 0.3,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setSelectedImage(null);
      setSelectedImageIndex(null);
    });
  };

  return (
    <>
      <View className="flex-row flex-wrap -mx-1">
        {images.map((image, index) => {
          // Determine image size based on position if dynamicSize is true
          const isLarge = dynamicSize && index % 5 === 0;
          const isMedium = dynamicSize && index % 5 === 1;
          const width = dynamicSize ? (isLarge ? "w-full" : isMedium ? "w-2/3" : "w-1/3") : "w-1/2";
          const height = dynamicSize ? (isLarge ? "h-64" : isMedium ? "h-48" : "h-32") : "aspect-square";

          return (
            <TouchableOpacity key={index} className={`${width} p-1`} onPress={() => handleImagePress(index)}>
              <View className={`${height} rounded-lg overflow-hidden`}>
                <Image source={{ uri: image }} className="w-full h-full" resizeMode="cover" />
              </View>
            </TouchableOpacity>
          );
        })}
      </View>

      <Modal visible={!!selectedImage} transparent={true} animationType="none" onRequestClose={handleClose}>
        <Animated.View className="flex-1 bg-black/90 justify-center items-center" style={{ opacity: fadeAnim }}>
          {selectedImage && (
            <>
              <TouchableOpacity
                className="absolute top-12 right-4 z-10 w-10 h-10 bg-white/20 rounded-full items-center justify-center"
                onPress={handleClose}
              >
                <Ionicons name="close" size={24} color="white" />
              </TouchableOpacity>
              <Animated.Image
                source={{ uri: selectedImage }}
                style={{
                  width: windowWidth,
                  height: windowHeight * 0.8,
                  transform: [{ scale: scaleAnim }],
                }}
                resizeMode="contain"
                className="rounded-lg"
              />
            </>
          )}
        </Animated.View>
      </Modal>
    </>
  );
};

export default ImageGallery;
