import React, { useEffect, useState, useRef } from "react";
import { View, Text, TouchableOpacity, ActivityIndicator } from "react-native";
import { useLocation } from "../../hooks/useLocation";
import MapView, { Marker, PROVIDER_DEFAULT } from "react-native-maps";
import { useAppSelector, useAppDispatch } from "../../reduxStore/hooks";
import { RootState } from "../../reduxStore/store";
import * as Location from "expo-location";
import { updateUser } from "../../reduxStore/userSlice";

interface LocationPermissionProps {
  onLocationUpdated?: (success: boolean) => void;
  autoRequest?: boolean;
  buttonText?: string;
  showAddress?: boolean;
}

export const LocationPermission: React.FC<LocationPermissionProps> = ({
  onLocationUpdated,
  autoRequest = false,
  buttonText = "Share My Location",
  showAddress = false,
}) => {
  const { location, isLoading, error, requestPermission, getLocation, updateLocation } = useLocation();
  const [permissionRequested, setPermissionRequested] = useState(false);
  const [permissionStatus, setPermissionStatus] = useState<string | null>(null);
  const mapRef = useRef<MapView>(null);
  const user = useAppSelector((state: RootState) => state.user.user);
  const dispatch = useAppDispatch();

  // Check if user already has location data
  const hasExistingLocation = !!(user?.location?.coordinates && user.location.coordinates.length > 0);

  // Check if location permission is already granted
  useEffect(() => {
    const checkPermission = async () => {
      try {
        const { status } = await Location.getForegroundPermissionsAsync();
        setPermissionStatus(status);

        // If permission is already granted and we don't have a location yet, get it
        if (status === "granted" && !user?.location?.address) {
          await getLocation();
        }
      } catch (error) {
        console.error("Error checking location permission:", error);
      }
    };

    checkPermission();
  }, []);

  useEffect(() => {
    if (autoRequest && !permissionRequested && permissionStatus !== "granted") {
      handleRequestPermission();
      setPermissionRequested(true);
    }
  }, [autoRequest, permissionRequested, permissionStatus]);

  const handleRequestPermission = async () => {
    const granted = await requestPermission();
    if (granted) {
      setPermissionStatus("granted");
      await getLocation();
    }
  };

  const handleUpdateLocation = async () => {
    // If we don't have location yet but permission is granted, get it first
    if (!location && permissionStatus === "granted") {
      await getLocation();
      return; // Wait for location to be fetched before updating
    }

    const success = await updateLocation();

    // Update Redux store with new location data
    if (success && location && user) {
      const updatedUser = {
        ...user,
        location: {
          coordinates: [location.coordinates.longitude, location.coordinates.latitude],
          address: location.address,
          city: location.city,
          country: location.country,
          state: location.state,
          zipCode: location.zipCode,
          countryCode: location.countryCode,
        },
      };
      dispatch(updateUser(updatedUser));
    }

    if (onLocationUpdated) {
      onLocationUpdated(success);
    }
  };

  // Determine what to display in the map
  const displayLocation =
    location ||
    (hasExistingLocation
      ? {
          coordinates: {
            latitude: user?.location?.coordinates[1] || 0,
            longitude: user?.location?.coordinates[0] || 0,
          },
          address: user?.location?.address || "Your location",
          city: user?.location?.city || "Unknown city",
          state: user?.location?.state || "Unknown state",
          country: user?.location?.country || "Unknown country",
          zipCode: user?.location?.zipCode || "Unknown zip code",
          countryCode: user?.location?.countryCode || "Unknown country code",
        }
      : null);

  // Determine button text based on permission status and location
  const getButtonText = () => {
    if (isLoading) return "Loading...";

    if (permissionStatus !== "granted") {
      return "Allow Location Access";
    }

    if (hasExistingLocation) {
      return "Update Location";
    }

    return "Set Location";
  };

  return (
    <View className="p-4">
      {error && <Text className="text-red-500 mb-3">{error}</Text>}

      {displayLocation && showAddress && (
        <View className="mb-4">
          <View className="p-3 bg-gray-100 rounded-lg mb-3">
            <Text className="text-base mb-1">
              {displayLocation.city}, {displayLocation.state}, {displayLocation.country}
            </Text>
          </View>

          <View className="h-48 rounded-lg overflow-hidden mb-3">
            <MapView
              ref={mapRef}
              className="w-full h-full"
              provider={PROVIDER_DEFAULT}
              initialRegion={{
                latitude: displayLocation.coordinates.latitude,
                longitude: displayLocation.coordinates.longitude,
                latitudeDelta: 0.005,
                longitudeDelta: 0.005,
              }}
              showsUserLocation
              showsMyLocationButton
              showsCompass
              showsScale
            >
              <Marker
                coordinate={{
                  latitude: displayLocation.coordinates.latitude,
                  longitude: displayLocation.coordinates.longitude,
                }}
                title={hasExistingLocation && !location ? "Saved Location" : "Current Location"}
                description={displayLocation.address}
              />
            </MapView>
          </View>
        </View>
      )}

      <TouchableOpacity
        className={`p-3.5 rounded-lg items-center ${isLoading ? "bg-blue-400" : "bg-blue-500"}`}
        onPress={permissionStatus === "granted" ? handleUpdateLocation : handleRequestPermission}
        disabled={isLoading}
      >
        {isLoading ? (
          <ActivityIndicator color="#fff" />
        ) : (
          <Text className="text-white text-base font-semibold">{getButtonText()}</Text>
        )}
      </TouchableOpacity>

      {permissionStatus === "denied" && (
        <View className="mt-3 p-3 bg-yellow-50 rounded-lg">
          <Text className="text-yellow-700 text-sm text-center">
            Location access is denied. Please enable location permissions in your device settings.
          </Text>
        </View>
      )}
    </View>
  );
};
