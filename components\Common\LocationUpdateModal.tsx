import React, { useState, useEffect, useRef } from "react";
import { View, Text, TouchableOpacity, Modal, Animated, Dimensions, StyleSheet } from "react-native";
import { LocationPermission } from "./LocationPermission";
import Ionicons from "react-native-vector-icons/Ionicons";
import { useAppSelector } from "../../reduxStore/hooks";
import { RootState } from "../../reduxStore/store";

interface LocationUpdateModalProps {
  visible: boolean;
  onClose: () => void;
  onLocationUpdated?: (success: boolean) => void;
}

export const LocationUpdateModal: React.FC<LocationUpdateModalProps> = ({ visible, onClose, onLocationUpdated }) => {
  const [updateSuccess, setUpdateSuccess] = useState(false);
  const user = useAppSelector((state: RootState) => state.user.user);
  const [modalVisible, setModalVisible] = useState(visible);
  const slideAnimation = useRef(new Animated.Value(0)).current;
  const fadeAnimation = useRef(new Animated.Value(0)).current;
  const { height } = Dimensions.get("window");

  // Check if user already has location data
  const hasExistingLocation = !!(user?.location?.coordinates && user.location.coordinates.length > 0);

  // Update modalVisible when visible prop changes
  useEffect(() => {
    setModalVisible(visible);
  }, [visible]);

  // Handle animations when visibility changes
  useEffect(() => {
    if (visible) {
      // Make sure modal is visible before starting animations
      setModalVisible(true);

      // Reset animations to initial values if needed
      slideAnimation.setValue(0);
      fadeAnimation.setValue(0);

      // Start animations
      Animated.parallel([
        Animated.timing(slideAnimation, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnimation, {
          toValue: 0.5,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // Only animate out if modal is currently visible
      if (modalVisible) {
        Animated.parallel([
          Animated.timing(slideAnimation, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(fadeAnimation, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true,
          }),
        ]).start(() => {
          setModalVisible(false);
        });
      }
    }
  }, [visible, modalVisible]);

  const handleLocationUpdated = (success: boolean) => {
    setUpdateSuccess(success);
    if (success) {
      // Auto-close the modal after successful update with a small delay
      setTimeout(() => {
        onClose();
        setUpdateSuccess(false);
        if (onLocationUpdated) {
          onLocationUpdated(success);
        }
      }, 2000);
    } else if (onLocationUpdated) {
      onLocationUpdated(success);
    }
  };

  const handleClose = () => {
    // Just call onClose directly - the parent component will set visible to false
    // which will trigger our animation effect
    onClose();
  };

  const translateY = slideAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [height, 0],
  });

  return (
    <Modal
      animationType="none"
      transparent={true}
      visible={modalVisible}
      onRequestClose={handleClose}
      statusBarTranslucent={true}
    >
      <Animated.View style={[StyleSheet.absoluteFill, { backgroundColor: "black", opacity: fadeAnimation }]}>
        <TouchableOpacity style={StyleSheet.absoluteFill} activeOpacity={1} onPress={handleClose} />
      </Animated.View>

      <Animated.View style={[styles.modalContainer, { transform: [{ translateY }] }]}>
        <View style={styles.contentContainer}>
          <View style={styles.handle} />

          <View style={styles.content}>
            <View style={styles.header}>
              <Text style={styles.title}>{hasExistingLocation ? "Update Your Location" : "Set Your Location"}</Text>
              {!updateSuccess && (
                <TouchableOpacity onPress={handleClose}>
                  <Ionicons name="close" size={24} color="#000" />
                </TouchableOpacity>
              )}
            </View>

            {updateSuccess && (
              <View style={styles.successMessage}>
                <Text style={styles.successText}>Your location has been updated successfully!</Text>
              </View>
            )}

            <LocationPermission
              onLocationUpdated={handleLocationUpdated}
              showAddress={true}
              buttonText={hasExistingLocation ? "Update My Location" : "Set My Location"}
              autoRequest={false} // Never auto-request, just show the current location if permission is granted
            />
          </View>
        </View>
      </Animated.View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: "flex-end",
  },
  contentContainer: {
    backgroundColor: "white",
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
  },
  handle: {
    width: 48,
    height: 4,
    backgroundColor: "#e0e0e0",
    borderRadius: 2,
    alignSelf: "center",
    marginTop: 12,
    marginBottom: 12,
  },
  content: {
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 24,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: "600",
  },
  successMessage: {
    backgroundColor: "#e6f7e6",
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  successText: {
    color: "#2ecc71",
    fontSize: 14,
  },
});
