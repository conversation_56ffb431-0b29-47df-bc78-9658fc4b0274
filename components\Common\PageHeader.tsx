import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import React from "react";
import Ionicons from "@expo/vector-icons/Ionicons";
import { useNavigation } from "expo-router";
import { PageHeaderProps } from "../../lib/types/commonTypes";

const PageHeader: React.FC<PageHeaderProps> = ({ title, color = "white", showIcon = true }) => {
  const navigation = useNavigation();

  return (
    <View className="flex-row items-center p-4 bg-white/20">
      <TouchableOpacity
        className="p-2 bg-black/30 rounded-full items-center justify-center"
        onPress={() => navigation.goBack()}
      >
        <Ionicons name="chevron-back" size={20} color="white" />
      </TouchableOpacity>

      {title && (
        <View className="flex-1 items-center">
          <Text className={`text-${color} font-heading text-headline-600`}>{title}</Text>
        </View>
      )}

      {/* Empty view for balanced layout */}
      <View style={{ width: 40 }} />
    </View>
  );
};

export default PageHeader;
