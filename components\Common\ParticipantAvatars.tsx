import { Image, Text, View } from "react-native";
import React from "react";
import { Avatar } from "../../lib/types/commonTypes";

interface ParticipantAvatarsProps {
  avatars: Avatar[];
  imageSize: number;
  leftGap: number;
}

const ParticipantAvatars = ({ avatars, imageSize, leftGap }: ParticipantAvatarsProps) => {
  return (
    <View className="relative z-50" style={{ width: (avatars.length - 1) * 15 + imageSize, height: imageSize }}>
      {avatars.map((avatar, index) => (
        <Image
          key={index}
          source={{ uri: avatar.user?.profilePic || avatar.uri }}
          className={`absolute rounded-full`}
          style={{
            width: imageSize,
            height: imageSize,
            left: index * leftGap,
            zIndex: index - avatars.length,
            borderWidth: 1,
            borderColor: "white",
          }}
          resizeMode="cover"
        />
      ))}
    </View>
  );
};

export default ParticipantAvatars;
