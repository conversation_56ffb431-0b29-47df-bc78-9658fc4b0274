import React from "react";
import { Text, View } from "react-native";
import { AntDesign } from "@expo/vector-icons";
import { RatingsProps } from "../../lib/types/commonTypes";

const Ratings: React.FC<RatingsProps> = ({ rating, size = 8, ratingText, ratingTextColor = "text-gray-500" }) => {
  const stars = [];
  for (let i = 0; i < rating; i++) {
    stars.push(<AntDesign key={i} name="star" size={size} color="#FCD240" />);
  }

  return (
    <View className="flex-row gap-x-1 items-center">
      {stars}
      {ratingText && <Text className={`text-[${size}px] ${ratingTextColor}`}>{ratingText}</Text>}
    </View>
  );
};

export default Ratings;
