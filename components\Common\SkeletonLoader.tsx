import React, { useEffect } from "react";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
  withSequence,
} from "react-native-reanimated";

interface SkeletonProps {
  width: number | string;
  height: number | string;
  borderRadius?: number;
}

const SkeletonLoader = ({ width, height, borderRadius = 8 }: SkeletonProps) => {
  const opacity = useSharedValue(0.3);

  useEffect(() => {
    opacity.value = withRepeat(
      withSequence(withTiming(0.7, { duration: 750 }), withTiming(0.3, { duration: 750 })),
      -1,
      true
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
  }));

  return (
    <Animated.View
      style={[
        {
          width: width as number,
          height: height as number,
          borderRadius,
          backgroundColor: "#E0E0E0",
        },
        animatedStyle,
      ]}
    />
  );
};

export default SkeletonLoader;
