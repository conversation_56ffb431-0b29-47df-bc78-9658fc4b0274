import { Text, TouchableOpacity, View } from "react-native";
import React from "react";

const TabButton = ({
  title,
  isActive,
  onPress,
  smallScreen = false,
}: {
  title: string;
  isActive: boolean;
  onPress: () => void;
  smallScreen?: boolean;
}) => (
  <TouchableOpacity
    onPress={onPress}
    className={`py-2 px-1 items-center ${isActive ? "border-textColor" : ""}`}
    style={{ flexShrink: 1 }}
  >
    <View
      className={`rounded-full ${isActive ? "bg-white" : ""}`}
      style={{
        paddingHorizontal: 12,
        paddingVertical: 8,
        maxWidth: "100%",
      }}
    >
      <Text
        className={`font-heading text-xs text-center ${smallScreen ? "text-[12px]" : "text-body-200"} ${
          isActive ? "text-black" : "text-textColor/50"
        }`}
        numberOfLines={1}
        adjustsFontSizeToFit
        minimumFontScale={0.8}
      >
        {title}
      </Text>
    </View>
  </TouchableOpacity>
);

export default TabButton;
