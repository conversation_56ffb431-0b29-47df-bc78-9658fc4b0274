import React, { useEffect, useRef } from "react";
import { View, Text, Animated, StyleSheet, TouchableOpacity, Platform } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useSafeAreaInsets } from "react-native-safe-area-context";

export type ToastType = "success" | "error" | "info" | "warning";

interface ToastProps {
  visible: boolean;
  message: string;
  type: ToastType;
  duration?: number;
  onDismiss: () => void;
}

const getToastConfig = (type: ToastType) => {
  switch (type) {
    case "success":
      return { icon: "checkmark-circle", color: "#10b981", bgColor: "#d1fae5" };
    case "error":
      return { icon: "close-circle", color: "#ef4444", bgColor: "#fee2e2" };
    case "warning":
      return { icon: "warning", color: "#f59e0b", bgColor: "#fef3c7" };
    case "info":
    default:
      return { icon: "information-circle", color: "#3b82f6", bgColor: "#dbeafe" };
  }
};

const Toast: React.FC<ToastProps> = ({ visible, message, type, duration = 3000, onDismiss }) => {
  const insets = useSafeAreaInsets();
  const opacity = useRef(new Animated.Value(0)).current;
  const translateY = useRef(new Animated.Value(-20)).current;

  const { icon, color, bgColor } = getToastConfig(type);

  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.timing(opacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(translateY, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();

      const timer = setTimeout(() => {
        hideToast();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [visible]);

  const hideToast = () => {
    Animated.parallel([
      Animated.timing(opacity, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(translateY, {
        toValue: -20,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      onDismiss();
    });
  };

  if (!visible) return null;

  return (
    <Animated.View
      style={[
        styles.container,
        {
          opacity,
          transform: [{ translateY }],
          top: insets.top + 10,
          backgroundColor: bgColor,
          borderColor: color,
        },
      ]}
    >
      <View style={styles.content}>
        <Ionicons name={icon as any} size={24} color={color} />
        <Text style={[styles.message, { color: "#1f2937" }]}>{message}</Text>
      </View>
      <TouchableOpacity onPress={hideToast} style={styles.closeButton}>
        <Ionicons name="close" size={18} color="#6b7280" />
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: "absolute",
    left: 16,
    right: 16,
    zIndex: 9999,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderLeftWidth: 4,
    ...Platform.select({
      ios: {
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  content: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
  },
  message: {
    marginLeft: 12,
    fontSize: 14,
    fontFamily: "Roboto-Medium",
    flexShrink: 1,
  },
  closeButton: {
    padding: 4,
  },
});

export default Toast;
