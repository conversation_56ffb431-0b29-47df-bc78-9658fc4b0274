import React, { createContext, useContext, useState } from 'react';
import Toast, { ToastType } from './Toast';

interface ToastContextType {
  showToast: (type: ToastType, message: string, duration?: number) => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

export const ToastProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [visible, setVisible] = useState(false);
  const [message, setMessage] = useState('');
  const [type, setType] = useState<ToastType>('info');
  const [duration, setDuration] = useState(3000);

  const showToast = (toastType: ToastType, toastMessage: string, toastDuration = 3000) => {
    // If a toast is already visible, hide it first
    if (visible) {
      setVisible(false);
      setTimeout(() => {
        setType(toastType);
        setMessage(toastMessage);
        setDuration(toastDuration);
        setVisible(true);
      }, 300);
    } else {
      setType(toastType);
      setMessage(toastMessage);
      setDuration(toastDuration);
      setVisible(true);
    }
  };

  return (
    <ToastContext.Provider value={{ showToast }}>
      {children}
      <Toast
        visible={visible}
        message={message}
        type={type}
        duration={duration}
        onDismiss={() => setVisible(false)}
      />
    </ToastContext.Provider>
  );
};