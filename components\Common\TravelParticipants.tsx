import { Text, View } from "react-native";
import React from "react";
import ParticipantAvatars from "./ParticipantAvatars";
import { Avatar } from "../../lib/types/commonTypes";

interface TravelParticipantsProps {
  totalSpots?: number; // Total number of spots
  spotsLeft?: number; // Number of spots left
  spotsTitle?: string; // Title for the spots
  spotsTitleSize?: number; // Size of the title text (optional)
  avatars: Avatar[]; // Array of avatar objects
  imageSize?: number; // Size of the avatar images (optional)
  textColor?: string; // Color of the text (optional)
  leftGap?: number; // Gap between avatars (optional)
}

const TravelParticipants: React.FC<TravelParticipantsProps> = ({
  totalSpots,
  spotsLeft,
  spotsTitle,
  spotsTitleSize = 10,
  avatars,
  imageSize = 14,
  textColor = "text-textColor",
  leftGap = 8,
}) => {
  return (
    <View className="relative flex-row items-center space-x-1  overflow-hidden">
      {spotsTitle && (
        <Text className={`font-bold ${textColor}`} style={{ fontSize: spotsTitleSize }}>
          {totalSpots && totalSpots - (spotsLeft || 0)} &#x2022; {spotsTitle}
        </Text>
      )}
      <ParticipantAvatars avatars={avatars} imageSize={imageSize} leftGap={leftGap} />
    </View>
  );
};

export default TravelParticipants;
