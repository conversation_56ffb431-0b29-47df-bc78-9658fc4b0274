// Multi-step form controller
import React, { useState } from "react";
import { View, Text, Alert } from "react-native";
import { router } from "expo-router";
import BasicInfoStep from "./steps/BasicInfoStep";
import LocationStep from "./steps/LocationStep";
import ActivitiesStep from "./steps/ActivitiesStep";
import DetailsStep from "./steps/DetailsStep";
import MediaStep from "./steps/MediaStep";
import ReviewStep from "./steps/ReviewStep";
import FormProgress from "./FormProgress";
import { hangoutsService } from "../../../api/hangoutsService";

interface Activity {
  name: string;
  description: string;
  duration: number;
  includedInPrice: boolean;
  additionalCost?: number;
}

interface Location {
  type: string;
  coordinates: [number, number];
  address: string;
  placeId: string;
  placeName: string;
}

interface FormData {
  title: string;
  description: string;
  location: Location;
  date: Date;
  category: string;
  subcategory: string;
  activities: Activity[];
  maxParticipants: number;
  price: number;
  isPaid: boolean;
  images: string[] | { uri: string }[];
  videos: string[] | { uri: string }[];
  tags: string[];
  duration: number;
  cancellationPolicy: string;
  socialLinks?: Array<{ platform: string; url: string }>;
}

const CreateHangoutForm = () => {
  const [currentStep, setCurrentStep] = useState<number>(0);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [formData, setFormData] = useState<FormData>({
    title: "",
    description: "",
    location: {
      type: "Point",
      coordinates: [0, 0],
      address: "",
      placeId: "",
      placeName: "",
    },
    date: new Date(),
    category: "ENTERTAINMENT", // Use uppercase format to match enum
    subcategory: "",
    activities: [], // Will be properly formatted in handleSubmit
    maxParticipants: 10,
    price: 0,
    isPaid: false,
    images: [],
    videos: [],
    tags: [],
    duration: 60,
    cancellationPolicy: "",
    socialLinks: [],
  });

  // Helper function to prepare data for API submission
  const prepareDataForSubmission = (data: FormData) => {
    // Process images to ensure they're in the correct format
    const processedImages = data.images
      .map((img) => {
        if (typeof img === "string") return img;
        if (img.uri) return img.uri;
        return "";
      })
      .filter(Boolean);

    // Process videos to ensure they're in the correct format
    const processedVideos = data.videos
      .map((vid) => {
        if (typeof vid === "string") return vid;
        if (vid.uri) return vid.uri;
        return "";
      })
      .filter(Boolean);

    // Ensure activities are in the correct format
    const processedActivities = data.activities.map((activity) => {
      if (typeof activity === "string") {
        return {
          name: activity,
          description: "No description provided",
          duration: 30,
          includedInPrice: true,
        };
      }
      return activity;
    });

    return {
      ...data,
      date: data.date.toISOString(),
      category: data.category,
      images: processedImages,
      videos: processedVideos,
      activities: processedActivities,
      // Ensure other fields match the expected schema
      maxParticipants: Number(data.maxParticipants),
      price: Number(data.price),
      duration: Number(data.duration),
    };
  };

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);

      // Format data for API using the helper function
      const hangoutData = prepareDataForSubmission(formData);

      // Call the API
      const response = await hangoutsService.createHangout(hangoutData);

      // Show success message
      Alert.alert("Success", "Your hangout has been created successfully!", [
        {
          text: "OK",
          onPress: () => router.replace(`/hangouts/${response._id}`),
        },
      ]);
    } catch (error: any) {
      // Handle API errors
      let errorMessage = "Something went wrong. Please try again.";

      if (error.response?.data?.error) {
        // Handle Zod validation errors which come as an array
        if (Array.isArray(error.response.data.error)) {
          const validationErrors = error.response.data.error;
          // Format validation errors into readable message
          const errorMessages = validationErrors.map((err: any) => {
            const path = err.path ? err.path.join(".") : "";
            return `${path ? path + ": " : ""}${err.message}`;
          });

          errorMessage = errorMessages.join("\n\n");
        } else if (typeof error.response.data.error === "string") {
          errorMessage = error.response.data.error;
        }
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      // Show error as alert with properly formatted message
      Alert.alert("Error", "Failed to create hangout:\n\n" + errorMessage, [{ text: "OK" }]);

      console.error("Error creating hangout:", error.response?.data || errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const steps: { title: string; component: React.ComponentType<any> }[] = [
    { title: "Basic Info", component: BasicInfoStep },
    { title: "Location", component: LocationStep },
    { title: "Activities", component: ActivitiesStep },
    { title: "Details", component: DetailsStep },
    { title: "Media", component: MediaStep },
    {
      title: "Review",
      component: (props: any) => <ReviewStep {...props} onSubmit={handleSubmit} isSubmitting={isSubmitting} />,
    },
  ];

  const updateFormData = (data: Partial<FormData>) => {
    setFormData((prev) => ({ ...prev, ...data }));
  };

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const CurrentStepComponent = steps[currentStep].component;

  return (
    <View className="flex-1 p-4">
      <FormProgress steps={steps} currentStep={currentStep} />
      <CurrentStepComponent
        formData={formData}
        updateFormData={updateFormData}
        nextStep={nextStep}
        prevStep={prevStep}
        isLastStep={currentStep === steps.length - 1}
      />
    </View>
  );
};

export default CreateHangoutForm;
