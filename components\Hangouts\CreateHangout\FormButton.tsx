// Reusable button component for form navigation
import React from "react";
import { TouchableOpacity, Text, ActivityIndicator, View } from "react-native";

interface FormButtonProps {
  title: string;
  onPress: () => void;
  secondary?: boolean;
  disabled?: boolean;
  icon?: React.ReactNode | (() => React.ReactNode);
}

const FormButton: React.FC<FormButtonProps> = ({ title, onPress, secondary = false, disabled = false, icon }) => {
  const baseStyle = "py-3 px-6 rounded-lg";
  const primaryStyle = "bg-primary";
  const secondaryStyle = "bg-slate-200";
  const disabledStyle = "opacity-50";

  const textBaseStyle = "text-center font-medium";
  const primaryTextStyle = "text-black";
  const secondaryTextStyle = "text-gray-700";

  return (
    <TouchableOpacity
      onPress={onPress}
      disabled={disabled}
      className={`
        ${baseStyle} 
        ${secondary ? secondaryStyle : primaryStyle}
        ${disabled ? disabledStyle : ""}
      `}
    >
      <View className="flex-row items-center justify-center">
        {icon && <View className="mr-2">{typeof icon === "function" ? icon() : icon}</View>}
        <Text
          className={`
            ${textBaseStyle} 
            ${secondary ? secondaryTextStyle : primaryTextStyle}
          `}
        >
          {title}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

export default FormButton;
