// Progress indicator for multi-step form
import React from "react";
import { View, Text } from "react-native";

interface Step {
  title: string;
}

interface FormProgressProps {
  steps: Step[];
  currentStep: number;
}

const FormProgress: React.FC<FormProgressProps> = ({ steps, currentStep }) => {
  return (
    <View className="mb-6">
      <View className="flex-row justify-between mb-2">
        {steps.map((step, index) => (
          <View
            key={index}
            className={`h-2 flex-1 mx-1 rounded-full ${index <= currentStep ? "bg-secondary" : "bg-slate-200"}`}
          />
        ))}
      </View>
      <Text className="font-subheading text-headline-500 font-bold mt-4">{steps[currentStep].title}</Text>
    </View>
  );
};

export default FormProgress;
