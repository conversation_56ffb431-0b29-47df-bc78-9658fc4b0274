import React from "react";
import { View, Text } from "react-native";
import { Picker } from "@react-native-picker/picker";

interface Category {
  id: string;
  name: string;
}

interface Subcategory {
  id: string;
  name: string;
}

interface CategoryPickerProps {
  categories: Category[];
  subcategories: Subcategory[];
  selectedCategory: string;
  selectedSubcategory: string;
  onCategoryChange: (value: string) => void;
  onSubcategoryChange: (value: string) => void;
  errors?: {
    category?: string;
    subcategory?: string;
  };
}

const CategoryPicker: React.FC<CategoryPickerProps> = ({
  categories = [],
  subcategories = [],
  selectedCategory,
  selectedSubcategory,
  onCategoryChange,
  onSubcategoryChange,
  errors,
}) => {
  return (
    <View>
      <View className="mb-4">
        <Text className="font-body-medium mb-2">Category</Text>
        <View className="border border-gray-300 rounded-xl overflow-hidden">
          <Picker selectedValue={selectedCategory} onValueChange={onCategoryChange}>
            <Picker.Item label="Select a category" value="" />
            {categories?.map((category) => (
              <Picker.Item key={category.id} label={category.name} value={category.id.toUpperCase()} />
            ))}
          </Picker>
        </View>
        {errors?.category && <Text className="text-red-500 mt-1">{errors.category}</Text>}
      </View>

      {selectedCategory && (
        <View className="mb-4">
          <Text className="font-body-medium mb-2">Subcategory</Text>
          <View className="border border-gray-300 rounded-xl overflow-hidden">
            <Picker selectedValue={selectedSubcategory} onValueChange={onSubcategoryChange}>
              <Picker.Item label="Select a subcategory" value="" />
              {subcategories.map((subcategory) => (
                <Picker.Item key={subcategory.id} label={subcategory.name} value={subcategory.id} />
              ))}
            </Picker>
          </View>
          {errors?.subcategory && <Text className="text-red-500 mt-1">{errors.subcategory}</Text>}
        </View>
      )}
    </View>
  );
};

export default CategoryPicker;
