import React, { useState } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  Switch,
} from "react-native";
import FormButton from "../FormButton";
import { MaterialIcons, Ionicons } from "@expo/vector-icons";

interface Activity {
  name: string;
  description: string;
  duration: number;
  includedInPrice: boolean;
  additionalCost: number;
  _id?: { $oid: string };
}

interface FormData {
  activities: Activity[];
}

interface ActivitiesStepProps {
  formData: FormData;
  updateFormData: (data: Partial<FormData>) => void;
  nextStep: () => void;
  prevStep: () => void;
}

const ActivitiesStep: React.FC<ActivitiesStepProps> = ({ formData, updateFormData, nextStep, prevStep }) => {
  const [errors, setErrors] = useState<{ activities?: string }>({});
  const [showAddForm, setShowAddForm] = useState(false);
  const [newActivity, setNewActivity] = useState<Activity>({
    name: "",
    description: "",
    duration: 0,
    includedInPrice: true,
    additionalCost: 0,
  });

  const addActivity = () => {
    if (newActivity.name.trim() && newActivity.description.trim()) {
      const updatedActivities = [...formData.activities, { ...newActivity }];
      updateFormData({ activities: updatedActivities });
      setNewActivity({
        name: "",
        description: "",
        duration: 0,
        includedInPrice: true,
        additionalCost: 0,
      });
    }
  };

  const removeActivity = (index: number) => {
    const updatedActivities = [...formData.activities];
    updatedActivities.splice(index, 1);
    updateFormData({ activities: updatedActivities });
  };

  const editActivity = (index: number, field: keyof Activity, value: string | number | boolean) => {
    const updatedActivities = [...formData.activities];
    updatedActivities[index] = { ...updatedActivities[index], [field]: value };
    updateFormData({ activities: updatedActivities });
  };

  const validate = () => {
    const newErrors: { activities?: string } = {};
    if (formData.activities.length === 0) {
      newErrors.activities = "At least one activity is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validate()) {
      nextStep();
    }
  };

  return (
    <KeyboardAvoidingView behavior={Platform.OS === "ios" ? "padding" : "height"} style={{ flex: 1 }}>
      <View className="flex-1">
        <Text className="text-2xl font-bold mb-4">What activities will you offer?</Text>

        {!showAddForm ? (
          <TouchableOpacity
            className="flex-row items-center bg-primary-50 rounded-lg p-4 mb-4 bg-slate-100"
            onPress={() => setShowAddForm(true)}
          >
            <Ionicons name="add-circle" size={24} color="black" />
            <Text className="ml-2 font-medium text-textColor">Add New Activity</Text>
          </TouchableOpacity>
        ) : (
          <View className="bg-white rounded-lg p-4 mb-4 shadow-sm border border-gray-100">
            <View className="flex-row justify-between items-center mb-3">
              <Text className="font-bold text-lg">New Activity</Text>
              <TouchableOpacity onPress={() => setShowAddForm(false)}>
                <Ionicons name="close-circle" size={24} color="#6B7280" />
              </TouchableOpacity>
            </View>

            <TextInput
              className="border border-gray-300 rounded-lg p-3 mb-2"
              value={newActivity.name}
              onChangeText={(text) => setNewActivity({ ...newActivity, name: text })}
              placeholder="Activity Name"
            />
            <TextInput
              className="border border-gray-300 rounded-lg p-3 mb-2"
              value={newActivity.description}
              onChangeText={(text) => setNewActivity({ ...newActivity, description: text })}
              placeholder="Activity Description"
              multiline
              numberOfLines={3}
            />

            <View className="flex-row mb-2">
              <View className="flex-1 mr-2">
                <Text className="text-sm text-gray-600 mb-1">Duration (min)</Text>
                <TextInput
                  className="border border-gray-300 rounded-lg p-3"
                  value={newActivity.duration.toString()}
                  onChangeText={(text) => setNewActivity({ ...newActivity, duration: parseInt(text) || 0 })}
                  keyboardType="numeric"
                />
              </View>
              <View className="flex-1">
                <Text className="text-sm text-gray-600 mb-1">Additional Cost (₹)</Text>
                <TextInput
                  className={`border border-gray-300 rounded-lg p-3 ${
                    newActivity.includedInPrice ? "bg-slate-200" : ""
                  }`}
                  value={newActivity.additionalCost?.toString()}
                  onChangeText={(text) => setNewActivity({ ...newActivity, additionalCost: parseFloat(text) || 0 })}
                  keyboardType="numeric"
                />
              </View>
            </View>

            <View className="flex-row items-center justify-between mb-4">
              <Text className="font-medium">Included in price?</Text>
              <Switch
                value={newActivity.includedInPrice}
                onValueChange={(value) => setNewActivity({ ...newActivity, includedInPrice: value })}
              />
            </View>

            <TouchableOpacity
              className="bg-primary rounded-lg justify-center px-4 py-3"
              onPress={() => {
                addActivity();
                setShowAddForm(false);
              }}
            >
              <Text className="text-textColor text-center font-medium">Add Activity</Text>
            </TouchableOpacity>
          </View>
        )}

        {errors.activities && <Text className="text-red-500 mb-2">{errors.activities}</Text>}

        <View className="mb-4">
          <Text className="font-medium mb-2">Your activities ({formData.activities.length}):</Text>
          {formData.activities.length === 0 ? (
            <View className="items-center justify-center py-8 bg-gray-50 rounded-lg">
              <Ionicons name="extension-puzzle-outline" size={48} color="#9CA3AF" />
              <Text className="text-gray-500 italic mt-2">No activities added yet</Text>
            </View>
          ) : (
            <FlatList
              data={formData.activities}
              keyExtractor={(item, index) => item._id?.$oid || index?.toString()}
              renderItem={({ item, index }) => (
                <View className="bg-white rounded-lg p-4 mb-3 shadow-sm border border-gray-100">
                  <View className="flex-row justify-between items-center mb-2">
                    <View className="w-8 h-8 bg-primary/50 rounded-full items-center justify-center mr-2">
                      <Ionicons name="extension-puzzle-outline" size={16} color="black" />
                    </View>
                    <TextInput
                      className="font-bold text-base flex-1"
                      value={item.name}
                      onChangeText={(text) => editActivity(index, "name", text)}
                      placeholder="Activity Name"
                    />
                    <TouchableOpacity onPress={() => removeActivity(index)}>
                      <MaterialIcons name="delete" size={22} color="#EF4444" />
                    </TouchableOpacity>
                  </View>

                  <TextInput
                    className="border border-gray-200 rounded-lg p-2 mb-2 bg-gray-50"
                    value={item.description}
                    onChangeText={(text) => editActivity(index, "description", text)}
                    placeholder="Activity Description"
                    multiline
                    numberOfLines={2}
                  />

                  <View className="flex-row">
                    <View className="flex-1 mr-2">
                      <Text className="text-xs text-gray-500 mb-1">Duration</Text>
                      <TextInput
                        className="border border-gray-200 rounded-lg p-2 bg-gray-50"
                        value={item.duration?.toString()}
                        onChangeText={(text) => editActivity(index, "duration", parseInt(text) || 0)}
                        placeholder="Minutes"
                        keyboardType="numeric"
                      />
                    </View>
                    <View className="flex-1 mr-2">
                      <Text className="text-xs text-gray-500 mb-1">Cost</Text>
                      <TextInput
                        className={`border border-gray-200 rounded-lg p-2  ${
                          item.includedInPrice ? "bg-slate-200" : "bg-gray-50"
                        }`}
                        value={item.additionalCost?.toString()}
                        onChangeText={(text) => editActivity(index, "additionalCost", parseFloat(text) || 0)}
                        placeholder="₹"
                        keyboardType="numeric"
                        editable={!item.includedInPrice}
                      />
                    </View>
                    <View className="flex-1 items-start">
                      <Text className="text-xs text-gray-500">Included</Text>
                      <Switch
                        value={item.includedInPrice}
                        onValueChange={(value) => editActivity(index, "includedInPrice", value)}
                      />
                    </View>
                  </View>
                </View>
              )}
            />
          )}
        </View>

        <View className="flex-row justify-between mt-6">
          <FormButton title="Back" onPress={prevStep} secondary />
          <FormButton title="Next" onPress={handleNext} />
        </View>
      </View>
    </KeyboardAvoidingView>
  );
};

export default ActivitiesStep;
