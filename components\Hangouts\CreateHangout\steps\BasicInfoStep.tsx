// Basic info form step (title, description, category, subcategory)
import React, { useState, useEffect } from "react";
import { View, Text, TextInput, ScrollView } from "react-native";
import FormButton from "../FormButton";
import CategoryPicker from "../inputs/CategoryPicker";
import { hangoutsService } from "../../../../api/hangoutsService";

interface FormData {
  title: string;
  description: string;
  category: string;
  subcategory: string;
}

interface Category {
  id: string;
  name: string;
  subcategories: Subcategory[];
}

interface Subcategory {
  id: string;
  name: string;
}

interface BasicInfoStepProps {
  formData: FormData;
  updateFormData: (data: Partial<FormData>) => void;
  nextStep: () => void;
}

const BasicInfoStep: React.FC<BasicInfoStepProps> = ({ formData, updateFormData, nextStep }) => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [subcategories, setSubcategories] = useState<Subcategory[]>([]);
  const [errors, setErrors] = useState<{
    title?: string;
    description?: string;
    category?: string;
    subcategory?: string;
  }>({});
  const [loading, setLoading] = useState<boolean>(true); // Add loading state

  useEffect(() => {
    const getCategories = async () => {
      try {
        const data = await hangoutsService.fetchCategories();
        setCategories(data || []);
      } catch (error) {
        console.error("Error fetching categories:", error);
        setCategories([]); // Fallback to an empty array in case of an error
      } finally {
        setLoading(false); // Set loading to false after API call
      }
    };

    getCategories();
  }, []);

  useEffect(() => {
    if (formData.category && categories.length > 0) {
      const selectedCategory = categories.find((cat) => cat.id === formData.category.toLowerCase());
      setSubcategories(selectedCategory?.subcategories || []);
    }
  }, [formData.category, categories]);

  const validate = () => {
    const newErrors: {
      title?: string;
      description?: string;
      category?: string;
      subcategory?: string;
    } = {};
    if (!formData.title) newErrors.title = "Title is required";

    if (!formData.description) {
      newErrors.description = "Description is required";
    } else if (formData.description.length < 10) {
      newErrors.description = "Description must be at least 10 characters long";
    }

    if (!formData.category) newErrors.category = "Category is required";
    if (!formData.subcategory) newErrors.subcategory = "Subcategory is required";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validate()) {
      nextStep();
    }
  };

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center">
        <Text className="text-lg text-gray-500">Loading categories...</Text>
      </View>
    );
  }

  return (
    <ScrollView showsVerticalScrollIndicator={false} className="flex-1">
      <View className="mb-4">
        <Text className="font-body-medium mb-2">Title</Text>
        <TextInput
          className="border border-gray-300 rounded-xl p-3 font-body"
          placeholder="Enter hangout title"
          value={formData.title}
          onChangeText={(text) => updateFormData({ title: text })}
        />
        {errors.title && <Text className="text-red-500 mt-1">{errors.title}</Text>}
      </View>

      <View className="mb-4">
        <Text className="font-body-medium mb-2">Description</Text>
        <TextInput
          className="border border-gray-300 rounded-xl p-3 font-body h-32"
          placeholder="Describe your hangout"
          multiline
          textAlignVertical="top"
          value={formData.description}
          onChangeText={(text) => updateFormData({ description: text })}
        />
        {errors.description && <Text className="text-red-500 mt-1">{errors.description}</Text>}
      </View>

      <CategoryPicker
        categories={categories}
        subcategories={subcategories}
        selectedCategory={formData.category}
        selectedSubcategory={formData.subcategory}
        onCategoryChange={(category: string) => updateFormData({ category, subcategory: "" })}
        onSubcategoryChange={(subcategory: string) => updateFormData({ subcategory })}
        errors={errors}
      />

      <View className="mt-6">
        <FormButton title="Next" onPress={handleNext} />
      </View>
    </ScrollView>
  );
};

export default BasicInfoStep;
