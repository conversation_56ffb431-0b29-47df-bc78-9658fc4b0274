import React, { useState } from "react";
import { View, Text, TextInput, ScrollView, Switch, TouchableOpacity } from "react-native";
import FormButton from "../FormButton";
import DateTimePicker from "@react-native-community/datetimepicker";
import { MaterialIcons } from "@expo/vector-icons";

interface FormData {
  date: Date;
  maxParticipants: number;
  isPaid: boolean;
  price: number;
  duration: number;
  cancellationPolicy: string;
  tags: string[];
}

interface DetailsStepProps {
  formData: FormData;
  updateFormData: (data: Partial<FormData>) => void;
  nextStep: () => void;
  prevStep: () => void;
}

const DetailsStep: React.FC<DetailsStepProps> = ({ formData, updateFormData, nextStep, prevStep }) => {
  const [errors, setErrors] = useState<{
    date?: string;
    maxParticipants?: string;
    price?: string;
    duration?: string;
  }>({});
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [tagInput, setTagInput] = useState("");

  const handleDateChange = (event: any, selectedDate: Date | undefined) => {
    if (event.type === "set" && selectedDate) {
      updateFormData({ date: new Date(selectedDate) });
      setShowDatePicker(false);
      setShowTimePicker(true); // Show time picker after date is selected
    } else {
      setShowDatePicker(false);
    }
  };

  const handleTimeChange = (event: any, selectedTime: Date | undefined) => {
    if (event.type === "set" && selectedTime) {
      const updatedDate = new Date(formData.date);
      updatedDate.setHours(selectedTime.getHours());
      updatedDate.setMinutes(selectedTime.getMinutes());
      updateFormData({ date: updatedDate });
    }
    setShowTimePicker(false);
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString("en-US", {
      weekday: "short",
      month: "short",
      day: "numeric",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const validate = () => {
    const newErrors: {
      date?: string;
      maxParticipants?: string;
      price?: string;
      duration?: string;
    } = {};
    if (!formData.date) newErrors.date = "Date is required";
    if (formData.maxParticipants <= 0) newErrors.maxParticipants = "Must allow at least 1 participant";
    if (formData.isPaid && formData.price <= 0) newErrors.price = "Price must be greater than 0";
    if (formData.duration <= 0) newErrors.duration = "Duration must be greater than 0";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validate()) {
      nextStep();
    }
  };

  const handleAddTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      updateFormData({ tags: [...formData.tags, tagInput.trim()] });
      setTagInput("");
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    updateFormData({
      tags: formData.tags.filter((tag) => tag !== tagToRemove),
    });
  };

  return (
    <ScrollView className="flex-1">
      <Text className="text-2xl font-bold mb-4">Event Details</Text>

      <View className="mb-4">
        <Text className="font-medium mb-1">Date and Time</Text>
        <TouchableOpacity
          className="border border-gray-300 rounded-lg p-3 flex-row justify-between items-center"
          onPress={() => setShowDatePicker(true)}
        >
          <Text>{formData.date ? formatDate(formData.date) : "Select a date and time"}</Text>
          <MaterialIcons name="calendar-today" size={24} color="gray" />
        </TouchableOpacity>
        {showDatePicker && (
          <DateTimePicker
            value={formData.date || new Date()} // Ensure a valid date is passed
            mode="date"
            display="default"
            onChange={handleDateChange}
            minimumDate={new Date()} // Prevent selecting past dates
          />
        )}
        {showTimePicker && (
          <DateTimePicker
            value={formData.date || new Date()} // Ensure a valid date is passed
            mode="time"
            display="default"
            onChange={handleTimeChange}
          />
        )}
        {errors.date && <Text className="text-red-500">{errors.date}</Text>}
      </View>

      <View className="mb-4">
        <Text className="font-medium mb-1">Duration (minutes)</Text>
        <TextInput
          className="border border-gray-300 rounded-lg p-3"
          value={formData.duration.toString()}
          onChangeText={(text) => updateFormData({ duration: parseInt(text) || 0 })}
          keyboardType="numeric"
          placeholder="60"
        />
        {errors.duration && <Text className="text-red-500">{errors.duration}</Text>}
      </View>

      <View className="mb-4">
        <Text className="font-medium mb-1">Maximum Participants</Text>
        <TextInput
          className="border border-gray-300 rounded-lg p-3"
          value={formData.maxParticipants.toString()}
          onChangeText={(text) => updateFormData({ maxParticipants: parseInt(text) || 0 })}
          keyboardType="numeric"
          placeholder="10"
        />
        {errors.maxParticipants && <Text className="text-red-500">{errors.maxParticipants}</Text>}
      </View>

      <View className="mb-4">
        <View className="flex-row justify-between items-center">
          <Text className="font-medium">Is this a paid event?</Text>
          <Switch value={formData.isPaid} onValueChange={(value) => updateFormData({ isPaid: value })} />
        </View>
      </View>

      {formData.isPaid && (
        <View className="mb-4">
          <Text className="font-medium mb-1">Price (₹)</Text>
          <TextInput
            className="border border-gray-300 rounded-lg p-3"
            value={formData.price.toString()}
            onChangeText={(text) => updateFormData({ price: parseFloat(text) || 0 })}
            keyboardType="numeric"
            placeholder="0.00"
          />
          {errors.price && <Text className="text-red-500">{errors.price}</Text>}
        </View>
      )}

      <View className="mb-4">
        <Text className="font-medium mb-1">Cancellation Policy</Text>
        <TextInput
          className="border border-gray-300 rounded-lg p-3 h-24"
          value={formData.cancellationPolicy}
          onChangeText={(text) => updateFormData({ cancellationPolicy: text })}
          placeholder="Describe your cancellation policy"
          multiline
        />
      </View>

      {/* Add Tags Section */}
      <View className="mb-4">
        <Text className="font-medium mb-1">Tags</Text>
        <View className="flex-row items-center mb-2">
          <TextInput
            className="border border-gray-300 rounded-lg p-3 flex-1 mr-2"
            value={tagInput}
            onChangeText={setTagInput}
            placeholder="Add a tag"
          />
          <TouchableOpacity className="bg-midnightBlue rounded-lg p-3" onPress={handleAddTag}>
            <Text className="text-white">Add</Text>
          </TouchableOpacity>
        </View>

        {/* Display added tags */}
        <View className="flex-row flex-wrap">
          {formData.tags.map((tag, index) => (
            <TouchableOpacity
              key={index}
              className="bg-slate-100 rounded-full px-3 py-1 mr-2 mb-2 flex-row items-center"
              onPress={() => handleRemoveTag(tag)}
            >
              <Text className="font-body text-xs text-gray-700 mr-1">#{tag}</Text>
              <MaterialIcons name="close" size={14} color="gray" />
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View className="flex-row justify-between mt-6">
        <FormButton title="Back" onPress={prevStep} secondary />
        <FormButton title="Next" onPress={handleNext} />
      </View>
    </ScrollView>
  );
};

export default DetailsStep;
