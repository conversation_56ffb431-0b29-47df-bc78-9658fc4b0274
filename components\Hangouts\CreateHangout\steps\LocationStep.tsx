import React, { useState, useEffect, useRef } from "react";
import { View, Text, TextInput } from "react-native";
import FormButton from "../FormButton";
import MapView, { Marker } from "react-native-maps";
import * as Location from "expo-location";
import { GooglePlacesAutocomplete } from "react-native-google-places-autocomplete";
import "react-native-get-random-values";

interface Location {
  type: string;
  coordinates: [number, number];
  address: string;
  placeId: string;
  placeName: string;
}

interface FormData {
  location: Location;
}

interface LocationStepProps {
  formData: FormData;
  updateFormData: (data: Partial<FormData>) => void;
  nextStep: () => void;
  prevStep: () => void;
}

const LocationStep: React.FC<LocationStepProps> = ({ formData, updateFormData, nextStep, prevStep }) => {
  const [errors, setErrors] = useState<{ location?: string; address?: string }>({});
  const mapRef = useRef<MapView>(null);

  useEffect(() => {
    if (formData.location.coordinates[0] && formData.location.coordinates[1]) {
      mapRef.current?.animateToRegion(
        {
          latitude: formData.location.coordinates[1],
          longitude: formData.location.coordinates[0],
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        },
        500
      );
    }
  }, [formData.location.coordinates]);

  const handleLocationSelect = (data: any, details: any) => {
    if (details) {
      const { lat, lng } = details.geometry.location;

      updateFormData({
        location: {
          type: "Point",
          coordinates: [lng, lat],
          address: data.description || details.name,
          placeId: details.place_id,
          placeName: details.name || data.structured_formatting?.main_text,
        },
      });

      // // Center the map on the selected location
      // mapRef.current?.animateToRegion(
      //   {
      //     latitude: lat,
      //     longitude: lng,
      //     latitudeDelta: 0.01,
      //     longitudeDelta: 0.01,
      //   },
      //   500
      // );
    }
  };

  const validate = () => {
    const newErrors: { location?: string; address?: string } = {};
    if (!formData.location.coordinates[0] && !formData.location.coordinates[1]) {
      newErrors.location = "Location is required";
    }
    if (!formData.location.address) newErrors.address = "Address is required";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validate()) {
      nextStep();
    }
  };

  return (
    <View className="flex-1">
      <Text className="text-2xl font-bold mb-4">Where is your hangout?</Text>

      <View className="mb-4 z-10">
        <GooglePlacesAutocomplete
          placeholder="Search for a location"
          onPress={handleLocationSelect}
          fetchDetails={true}
          disableScroll={true}
          query={{
            key: process.env.EXPO_PUBLIC_GOOGLE_MAPS_API_KEY || "", // Ensure the key is passed as a string
            language: "en",
          }}
          styles={{
            container: {
              flex: 0,
              zIndex: 1,
            },
            textInputContainer: {
              borderWidth: 1,
              borderColor: "#ccc",
              borderRadius: 8,
            },
            textInput: {
              height: 48,
              borderRadius: 8,
              paddingVertical: 5,
              paddingHorizontal: 10,
            },
            listView: {
              zIndex: 1,
            },
          }}
        />
        {errors.location && <Text className="text-red-500">{errors.location}</Text>}
      </View>

      <View className="h-64 mb-4 rounded-lg overflow-hidden">
        <MapView
          ref={mapRef}
          className="w-full h-full"
          initialRegion={{
            latitude: 12.97,
            longitude: 77.59,
            latitudeDelta: 0.0912,
            longitudeDelta: 0.0421,
          }}
          showsUserLocation={true}
          toolbarEnabled={false}
          zoomEnabled={true}
          rotateEnabled={false}
        >
          {formData.location.coordinates[0] !== 0 && formData.location.coordinates[1] !== 0 && (
            <Marker
              coordinate={{
                latitude: formData.location.coordinates[1],
                longitude: formData.location.coordinates[0],
              }}
            />
          )}
        </MapView>
      </View>

      <View className="mb-4">
        <Text className="font-medium mb-1">Address</Text>
        <TextInput
          className="border border-gray-300 rounded-lg p-3"
          value={formData?.location?.address}
          onChangeText={(text) =>
            updateFormData({
              location: { ...formData.location, address: text },
            })
          }
          placeholder="Address"
        />
        {errors.address && <Text className="text-red-500">{errors.address}</Text>}
      </View>

      <View className="flex-row justify-between mt-6">
        <FormButton title="Back" onPress={prevStep} secondary />
        <FormButton title="Next" onPress={handleNext} />
      </View>
    </View>
  );
};

export default LocationStep;
