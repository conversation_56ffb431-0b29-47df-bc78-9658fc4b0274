import React, { useState } from "react";
import { View, Text, FlatList, Image, TouchableOpacity, Alert, ActivityIndicator } from "react-native";
import FormButton from "../FormButton";
import * as ImagePicker from "expo-image-picker";
import { MaterialIcons } from "@expo/vector-icons";
import { Video } from "expo-av";
import { uploadFileToFirebase } from "../../../../services/firebaseStorageService";

interface FormData {
  images: string[];
  videos: string[];
}

interface MediaStepProps {
  formData: FormData;
  updateFormData: (data: Partial<FormData>) => void;
  nextStep: () => void;
  prevStep: () => void;
}

const MediaStep: React.FC<MediaStepProps> = ({ formData, updateFormData, nextStep, prevStep }) => {
  const [errors, setErrors] = useState<{ images?: string }>({});
  const [uploading, setUploading] = useState(false);

  const pickAndUploadFile = async (mediaType: "image" | "video", folder: string) => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (status !== "granted") {
      Alert.alert("Permission Denied", "We need camera roll permissions to make this work!");
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: mediaType === "image" ? ImagePicker.MediaTypeOptions.Images : ImagePicker.MediaTypeOptions.Videos,
      allowsEditing: true,
      aspect: mediaType === "image" ? [4, 3] : [16, 9],
      quality: 0.8,
    });

    if (!result.canceled) {
      const fileUri = result.assets[0].uri;

      try {
        setUploading(true);

        // Upload the file to Firebase Storage
        const publicUrl = await uploadFileToFirebase(fileUri, `hangouts/${mediaType}s`);

        if (publicUrl) {
          if (mediaType === "image") {
            updateFormData({ images: [...formData.images, publicUrl] });
          } else {
            updateFormData({ videos: [...formData.videos, publicUrl] });
          }
        } else {
          Alert.alert("Upload Failed", "Failed to upload the file to Firebase. Please try again.");
        }
      } catch (error) {
        console.error("Error uploading file to Firebase:", error);
        Alert.alert("Error", "An error occurred while uploading the file.");
      } finally {
        setUploading(false);
      }
    }
  };

  const removeFile = (index: number, type: "image" | "video") => {
    if (type === "image") {
      const updatedImages = [...formData.images];
      updatedImages.splice(index, 1);
      updateFormData({ images: updatedImages });
    } else {
      const updatedVideos = [...formData.videos];
      updatedVideos.splice(index, 1);
      updateFormData({ videos: updatedVideos });
    }
  };

  const validate = () => {
    const newErrors: { images?: string } = {};
    if (formData.images.length === 0) {
      newErrors.images = "At least one image is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validate()) {
      nextStep();
    }
  };

  const renderContent = () => (
    <View>
      <Text className="text-2xl font-bold mb-4">Add Media</Text>

      <View className="mb-6">
        <View className="flex-row justify-between items-center mb-2">
          <Text className="font-medium">Images</Text>
          <TouchableOpacity
            className="bg-blue-500 rounded-lg py-2 px-4 flex-row items-center"
            onPress={() => pickAndUploadFile("image", "images")}
          >
            <MaterialIcons name="add-photo-alternate" size={20} color="white" />
            <Text className="text-white ml-1">Add Image</Text>
          </TouchableOpacity>
        </View>

        {errors.images && <Text className="text-red-500 mb-2">{errors.images}</Text>}

        {formData.images.length > 0 ? (
          <FlatList
            data={formData.images}
            horizontal
            showsHorizontalScrollIndicator={false}
            keyExtractor={(item, index) => index.toString()}
            renderItem={({ item, index }) => (
              <View className="mr-3 relative">
                <Image source={{ uri: item }} className="w-32 h-32 rounded-lg" />
                <TouchableOpacity
                  className="absolute top-1 right-1 bg-black/50 rounded-full p-1"
                  onPress={() => removeFile(index, "image")}
                >
                  <MaterialIcons name="close" size={16} color="white" />
                </TouchableOpacity>
              </View>
            )}
          />
        ) : (
          <View className="border-2 border-dashed border-gray-300 rounded-lg h-32 items-center justify-center">
            <Text className="text-gray-400">No images added yet</Text>
          </View>
        )}
      </View>

      <View className="mb-6">
        <View className="flex-row justify-between items-center mb-2">
          <Text className="font-medium">Videos (optional)</Text>
          <TouchableOpacity
            className="bg-blue-500 rounded-lg py-2 px-4 flex-row items-center"
            onPress={() => pickAndUploadFile("video", "videos")}
          >
            <MaterialIcons name="videocam" size={20} color="white" />
            <Text className="text-white ml-1">Add Video</Text>
          </TouchableOpacity>
        </View>

        {formData.videos.length > 0 ? (
          <FlatList
            data={formData.videos}
            horizontal
            showsHorizontalScrollIndicator={false}
            keyExtractor={(item, index) => index.toString()}
            renderItem={({ item, index }) => (
              <View className="mr-3 relative">
                <Video source={{ uri: item }} className="w-48 h-32 rounded-lg" useNativeControls />
                <TouchableOpacity
                  className="absolute top-1 right-1 bg-black/50 rounded-full p-1"
                  onPress={() => removeFile(index, "video")}
                >
                  <MaterialIcons name="close" size={16} color="white" />
                </TouchableOpacity>
              </View>
            )}
          />
        ) : (
          <View className="border-2 border-dashed border-gray-300 rounded-lg h-32 items-center justify-center">
            <Text className="text-gray-400">No videos added yet</Text>
          </View>
        )}
      </View>

      {uploading && (
        <View className="flex-row justify-center items-center mb-4">
          <ActivityIndicator size="large" color="#0000ff" />
          <Text className="ml-2">Uploading...</Text>
        </View>
      )}

      <View className="flex-row justify-between mt-6">
        <FormButton title="Back" onPress={prevStep} secondary />
        <FormButton title="Next" onPress={handleNext} />
      </View>
    </View>
  );

  return (
    <FlatList
      data={[{ key: "content" }]} // Dummy data to render the content
      renderItem={renderContent}
      keyExtractor={(item) => item.key}
    />
  );
};

export default MediaStep;
