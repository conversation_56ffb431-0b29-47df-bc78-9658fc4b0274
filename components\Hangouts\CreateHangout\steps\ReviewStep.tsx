import React from "react";
import { View, Text, ScrollView, TouchableOpacity, Image, ActivityIndicator } from "react-native";
import FormButton from "../FormButton";
import { Ionicons, MaterialIcons } from "@expo/vector-icons";

interface ReviewStepProps {
  formData: any;
  prevStep: () => void;
  onSubmit: () => void;
  isSubmitting?: boolean;
}

const ReviewStep: React.FC<ReviewStepProps> = ({ formData, prevStep, onSubmit, isSubmitting = false }) => {
  // Format date and time
  const formatDate = (date: Date) => {
    if (!date) return "Not specified";
    return date.toLocaleDateString();
  };

  const formatTime = (date: Date) => {
    if (!date) return "";
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  return (
    <ScrollView className="flex-1 p-4">
      <Text className="text-2xl font-bold mb-4">Review Your Hangout Details</Text>

      {/* Basic Info Section */}
      <View className="mb-6 bg-white rounded-lg p-4 shadow-sm border border-gray-100">
        <View className="flex-row items-center mb-2">
          <View className="w-8 h-8 bg-primary-50 rounded-full items-center justify-center mr-2">
            <Ionicons name="information-circle" size={18} color="#4A6FFF" />
          </View>
          <Text className="font-bold text-lg">Basic Info</Text>
        </View>

        <View className="ml-10">
          <View className="mb-2">
            <Text className="text-gray-500 text-sm">Title</Text>
            <Text className="text-textColor font-medium">{formData.title || "Not specified"}</Text>
          </View>

          <View className="mb-2">
            <Text className="text-gray-500 text-sm">Description</Text>
            <Text className="text-textColor">{formData.description || "Not specified"}</Text>
          </View>

          <View className="flex-row mb-2">
            <View className="flex-1 mr-2">
              <Text className="text-gray-500 text-sm">Category</Text>
              <Text className="text-textColor font-medium">{formData.category || "Not specified"}</Text>
            </View>
            <View className="flex-1">
              <Text className="text-gray-500 text-sm">Subcategory</Text>
              <Text className="text-textColor font-medium">{formData.subcategory || "Not specified"}</Text>
            </View>
          </View>
        </View>
      </View>

      {/* Location Section */}
      <View className="mb-6 bg-white rounded-lg p-4 shadow-sm border border-gray-100">
        <View className="flex-row items-center mb-2">
          <View className="w-8 h-8 bg-primary-50 rounded-full items-center justify-center mr-2">
            <Ionicons name="location" size={18} color="#4A6FFF" />
          </View>
          <Text className="font-bold text-lg">Location</Text>
        </View>

        <View className="ml-10">
          <View className="mb-2">
            <Text className="text-gray-500 text-sm">Address</Text>
            <Text className="text-textColor font-medium">{formData.location?.address || "Not specified"}</Text>
          </View>

          <View className="mb-2">
            <Text className="text-gray-500 text-sm">Place Name</Text>
            <Text className="text-textColor">{formData.location?.placeName || "Not specified"}</Text>
          </View>
        </View>
      </View>

      {/* Activities Section */}
      <View className="mb-6 bg-white rounded-lg p-4 shadow-sm border border-gray-100">
        <View className="flex-row items-center mb-2">
          <View className="w-8 h-8 bg-primary-50 rounded-full items-center justify-center mr-2">
            <Ionicons name="extension-puzzle" size={18} color="#4A6FFF" />
          </View>
          <Text className="font-bold text-lg">Activities</Text>
        </View>

        <View className="ml-10">
          {formData.activities && formData.activities.length > 0 ? (
            formData.activities.map((activity: any, index: number) => (
              <View key={index} className="mb-3 pb-3 border-b border-gray-100">
                <View className="mb-1">
                  <Text className="text-gray-500 text-sm">Activity {index + 1}</Text>
                  <Text className="text-textColor font-medium">{activity.name}</Text>
                </View>

                <Text className="text-gray-600 mb-1">{activity.description}</Text>

                <View className="flex-row">
                  <View className="flex-1 mr-2">
                    <Text className="text-gray-500 text-xs">Duration</Text>
                    <Text className="text-textColor">{activity.duration} minutes</Text>
                  </View>

                  <View className="flex-1">
                    <Text className="text-gray-500 text-xs">Price</Text>
                    <Text className="text-textColor">
                      {activity.includedInPrice ? "Included in price" : `₹${activity.additionalCost || 0}`}
                    </Text>
                  </View>
                </View>
              </View>
            ))
          ) : (
            <Text className="text-gray-500 italic">No activities added</Text>
          )}
        </View>
      </View>

      {/* Details Section */}
      <View className="mb-6 bg-white rounded-lg p-4 shadow-sm border border-gray-100">
        <View className="flex-row items-center mb-2">
          <View className="w-8 h-8 bg-primary-50 rounded-full items-center justify-center mr-2">
            <Ionicons name="calendar" size={18} color="#4A6FFF" />
          </View>
          <Text className="font-bold text-lg">Event Details</Text>
        </View>

        <View className="ml-10">
          <View className="flex-row mb-2">
            <View className="flex-1 mr-2">
              <Text className="text-gray-500 text-sm">Date</Text>
              <Text className="text-textColor font-medium">{formatDate(formData.date)}</Text>
            </View>

            <View className="flex-1">
              <Text className="text-gray-500 text-sm">Time</Text>
              <Text className="text-textColor font-medium">{formatTime(formData.date)}</Text>
            </View>
          </View>

          <View className="flex-row mb-2">
            <View className="flex-1 mr-2">
              <Text className="text-gray-500 text-sm">Duration</Text>
              <Text className="text-textColor font-medium">{formData.duration} minutes</Text>
            </View>

            <View className="flex-1">
              <Text className="text-gray-500 text-sm">Max Participants</Text>
              <Text className="text-textColor font-medium">{formData.maxParticipants}</Text>
            </View>
          </View>

          <View className="mb-2">
            <Text className="text-gray-500 text-sm">Price</Text>
            <Text className="text-textColor font-medium">{formData.isPaid ? `₹${formData.price}` : "Free"}</Text>
          </View>

          <View className="mb-2">
            <Text className="text-gray-500 text-sm">Cancellation Policy</Text>
            <Text className="text-textColor">{formData.cancellationPolicy || "Not specified"}</Text>
          </View>
        </View>
      </View>

      {/* Media Section */}
      <View className="mb-6 bg-white rounded-lg p-4 shadow-sm border border-gray-100">
        <View className="flex-row items-center mb-2">
          <View className="w-8 h-8 bg-primary-50 rounded-full items-center justify-center mr-2">
            <Ionicons name="images" size={18} color="#4A6FFF" />
          </View>
          <Text className="font-bold text-lg">Media</Text>
        </View>

        <View className="ml-10">
          {/* Images preview */}
          {formData.images && formData.images.length > 0 ? (
            <View>
              <Text className="text-gray-500 text-sm mb-2">Images ({formData.images.length})</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} className="mb-3">
                {formData.images.map((image: any, index: number) => (
                  <View key={index} className="mr-2">
                    <Image source={{ uri: image.uri || image }} className="w-20 h-20 rounded-lg" resizeMode="cover" />
                  </View>
                ))}
              </ScrollView>
            </View>
          ) : (
            <Text className="text-gray-500 italic mb-2">No images uploaded</Text>
          )}

          {/* Videos preview */}
          {formData.videos && formData.videos.length > 0 ? (
            <View>
              <Text className="text-gray-500 text-sm mb-2">Videos ({formData.videos.length})</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                {formData.videos.map((video: any, index: number) => (
                  <View key={index} className="mr-2 bg-gray-100 w-20 h-20 rounded-lg items-center justify-center">
                    <Ionicons name="videocam" size={24} color="#4A6FFF" />
                    <Text className="text-xs text-gray-500">Video {index + 1}</Text>
                  </View>
                ))}
              </ScrollView>
            </View>
          ) : (
            <Text className="text-gray-500 italic">No videos uploaded</Text>
          )}
        </View>
      </View>

      <View className="flex-row justify-between mt-6 mb-10">
        <FormButton title="Back" onPress={prevStep} secondary disabled={isSubmitting} />
        <FormButton
          title={isSubmitting ? "Submitting..." : "Submit"}
          onPress={onSubmit}
          disabled={isSubmitting}
          icon={isSubmitting ? () => <ActivityIndicator size="small" color="#FFFFFF" /> : undefined}
        />
      </View>
    </ScrollView>
  );
};

export default ReviewStep;
