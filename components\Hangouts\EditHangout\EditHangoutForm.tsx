import React, { useState, useEffect } from "react";
import { View, Text, Alert, ActivityIndicator } from "react-native";
import { router } from "expo-router";
import BasicInfoStep from "../CreateHangout/steps/BasicInfoStep";
import LocationStep from "../CreateHangout/steps/LocationStep";
import ActivitiesStep from "../CreateHangout/steps/ActivitiesStep";
import DetailsStep from "../CreateHangout/steps/DetailsStep";
import MediaStep from "../CreateHangout/steps/MediaStep";
import ReviewStep from "../CreateHangout/steps/ReviewStep";
import FormProgress from "../CreateHangout/FormProgress";
import { hangoutsService } from "../../../api/hangoutsService";
import { useHangoutDetails } from "../../../hooks/useHangoutDetails";

interface EditHangoutFormProps {
  hangoutId: string;
}

const EditHangoutForm: React.FC<EditHangoutFormProps> = ({ hangoutId }) => {
  const { data, isLoading, error, refetch } = useHangoutDetails(hangoutId);
  const [currentStep, setCurrentStep] = useState<number>(0);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [formData, setFormData] = useState<any>(null);

  const { hangout: hangoutData } = data || {};

  // Initialize form data with hangout data when it's loaded
  useEffect(() => {
    if (hangoutData) {
      // Ensure location data is properly formatted for the LocationStep component
      const locationData = hangoutData.location || {
        type: "Point",
        coordinates: [0, 0],
        address: "",
        placeId: "",
        placeName: "",
      };

      // Make sure coordinates are in the correct format
      const coordinates = Array.isArray(locationData.coordinates) ? locationData.coordinates : [0, 0];

      setFormData({
        title: hangoutData.title || "",
        description: hangoutData.description || "",
        location: {
          type: locationData.type || "Point",
          coordinates: coordinates,
          address: locationData.address || "",
          placeId: locationData.placeId || "",
          placeName: locationData.placeName || "",
        },
        date: new Date(hangoutData.date) || new Date(),
        category: hangoutData.category,
        subcategory: hangoutData.subcategory || "",
        activities: hangoutData.activities || [],
        maxParticipants: hangoutData.maxParticipants || 10,
        price: hangoutData.price || 0,
        isPaid: hangoutData.isPaid || false,
        images: hangoutData.images || [],
        videos: hangoutData.videos || [],
        tags: hangoutData.tags || [],
        duration: hangoutData.duration || 60,
        cancellationPolicy: hangoutData.cancellationPolicy || "",
        socialLinks: hangoutData.socialLinks || [],
      });
    }
  }, [hangoutData]);

  const prepareDataForSubmission = (data: any) => {
    // Format activities - remove temporary _id if present
    const processedActivities = data.activities.map((activity: any) => {
      if (typeof activity === "string") {
        return {
          name: activity,
          description: "No description provided",
          duration: 30,
          includedInPrice: true,
        };
      }

      // If activity has a temporary _id, remove it
      const { _id, ...activityWithoutId } = activity;
      return activityWithoutId;
    });

    return {
      ...data,
      activities: processedActivities,
    };
  };

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);

      // Format data for API
      const updatedHangoutData = prepareDataForSubmission(formData);

      // Call the API to update the hangout
      const response = await hangoutsService.updateHangout(hangoutId, updatedHangoutData);

      if (response.status === 200) {
        refetch();
        // Show success message
        Alert.alert("Success", "Your hangout has been updated successfully!", [
          {
            text: "OK",
            onPress: () => router.replace(`/hangouts/${hangoutId}`),
          },
        ]);
      } else {
        Alert.alert("Error", "Failed to update hangout", [{ text: "OK" }]);
      }
    } catch (error: any) {
      // Format error message
      let errorMessage = "An unexpected error occurred";
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      // Show error as alert
      Alert.alert("Error", "Failed to update hangout:\n\n" + errorMessage, [{ text: "OK" }]);
      console.error("Error updating hangout:", error.response?.data || errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const steps: { title: string; component: React.ComponentType<any> }[] = [
    { title: "Basic Info", component: BasicInfoStep },
    { title: "Location", component: LocationStep },
    { title: "Activities", component: ActivitiesStep },
    { title: "Details", component: DetailsStep },
    { title: "Media", component: MediaStep },
    {
      title: "Review",
      component: (props: any) => <ReviewStep {...props} onSubmit={handleSubmit} isSubmitting={isSubmitting} />,
    },
  ];

  const updateFormData = (data: Partial<any>) => {
    setFormData((prev: any) => ({ ...prev, ...data }));
  };

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  if (isLoading || !formData) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="large" color="#4A6FFF" />
        <Text className="mt-4 text-gray-600 font-body">Loading hangout data...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View className="flex-1 justify-center items-center p-4">
        <Text className="text-red-500 font-body text-center">Error loading hangout data. Please try again later.</Text>
      </View>
    );
  }

  const CurrentStepComponent = steps[currentStep].component;

  return (
    <View className="flex-1 p-4">
      <FormProgress steps={steps} currentStep={currentStep} />
      <CurrentStepComponent
        formData={formData}
        updateFormData={updateFormData}
        nextStep={nextStep}
        prevStep={prevStep}
        isLastStep={currentStep === steps.length - 1}
        isEditMode={true}
      />
    </View>
  );
};

export default EditHangoutForm;
