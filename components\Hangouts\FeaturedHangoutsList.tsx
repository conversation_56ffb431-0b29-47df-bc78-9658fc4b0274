import React from "react";
import { View, FlatList, StyleSheet } from "react-native";
import ContentCard from "../Common/ContentCard";
import { FeaturedHangoutSkeleton } from "./SkeletonLoaders";
import { useRouter } from "expo-router";
import { SectionTitle } from "../Common/SectionTitle";
import HeadingWithCTA from "../HomePage/HeadingWithCTA";

interface FeaturedHangoutsListProps {
  hangouts: any[];
  isLoading?: boolean;
}

const FeaturedHangoutsList: React.FC<FeaturedHangoutsListProps> = ({ hangouts, isLoading }) => {
  const router = useRouter();

  if (isLoading) {
    return (
      <View className="mb-8">
        <SectionTitle title="Featured" />
        <FeaturedHangoutSkeleton />
      </View>
    );
  }

  if (!hangouts || hangouts.length === 0) {
    return null;
  }

  return (
    <View className="mb-8">
      <SectionTitle title="Featured" />

      <FlatList
        data={hangouts}
        renderItem={({ item }) => (
          <ContentCard
            item={item}
            featured={true}
            contentType="hangout"
            onPress={() => {
              router.push({
                pathname: `/hangouts/${item._id}`,
              });
            }}
          />
        )}
        keyExtractor={(item) => `featured-${item._id}`}
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.list}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  list: {
    marginTop: 8,
  },
});

export default FeaturedHangoutsList;
