import React from "react";
import { View, Text } from "react-native";
import { Ionicons } from "@expo/vector-icons";

export interface Activity {
  _id: string;
  name: string;
  description: string;
  duration: number;
  includedInPrice: boolean;
  additionalCost: number;
}

interface ActivityItemProps {
  activity: Activity;
}

const HangoutActivityItem: React.FC<ActivityItemProps> = ({ activity }) => (
  <View className="flex-row p-3 border border-slate-200 rounded-xl mb-3">
    <View className="w-10 h-10 bg-primary-50 rounded-full items-center justify-center mr-3">
      <Ionicons name="extension-puzzle-outline" size={18} color="#4A6FFF" />
    </View>
    <View className="flex-1">
      <View className="flex-row justify-between items-center">
        <Text className="font-heading text-textColor">{activity.name}</Text>
        {activity.includedInPrice ? (
          <View className="bg-green-100 rounded-full px-2 py-1">
            <Text className="text-green-700 text-xs font-body-medium">Included</Text>
          </View>
        ) : (
          <View className="bg-gray-100 rounded-full px-2 py-1">
            <Text className="text-gray-700 text-xs font-body-medium">+₹{activity.additionalCost}</Text>
          </View>
        )}
      </View>
      <Text className="text-gray-600 text-xs mt-1">{activity.duration} mins</Text>
      <Text className="text-gray-600 text-sm mt-2">{activity.description}</Text>
    </View>
  </View>
);

export default HangoutActivityItem;
