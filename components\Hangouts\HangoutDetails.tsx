import React, { useState, useRef } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Dimensions,
  Animated,
  Image,
  Modal,
  ActivityIndicator,
  Alert,
  Share,
} from "react-native";
import { Ionicons, MaterialIcons } from "@expo/vector-icons";
import { router } from "expo-router";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useHangoutDetails } from "../../hooks/useHangoutDetails";
import { useJoinHangout } from "../../hooks/useJoinHangout";
import { formatDate, formatTime, formatPrice } from "../../lib/utils/formatters";
import ParticipantAvatars from "../Common/ParticipantAvatars";
import { toTitleCase } from "../../lib/utils/commonUtils";
import { SectionTitle } from "../Common/SectionTitle";
import TabButton from "../Common/TabButton";
import ImageGallery from "../Common/ImageGallery";
import HangoutTicket from "./HangoutTicket";
import HangoutLocationMap from "./HangoutLocationMap";
import HangoutLocationFallback from "./HangoutLocationFallback";
import { LinearGradient } from "expo-linear-gradient";
import HangoutActivityItem, { Activity } from "./HangoutActivityItem";
import { useAppSelector } from "../../reduxStore/hooks";
import { RootState } from "../../reduxStore/store";
import { usePayment } from "../../hooks/usePayment";
import PaymentStatus from "./PaymentStatus";
import { showToast } from "../../lib/utils/showToast";
import { Platform } from "react-native";
import { hangoutsService } from "../../api/hangoutsService";
import ProfileValidator from "../Profile/ProfileValidator";
import TermsAgreementModal from "../Legal/TermsAgreementModal";
import { validateUserProfile } from "../../lib/utils/profileValidation";
import InstagramProfile from "../Profile/InstagramProfile";
import { getHangoutLink } from "../../constants/common";

// Main Component
interface HangoutDetailsProps {
  hangoutId: string;
}

const HangoutDetails: React.FC<HangoutDetailsProps> = ({ hangoutId }) => {
  const { data, isLoading, error, refetch } = useHangoutDetails(hangoutId);
  const joinHangoutMutation = useJoinHangout();
  const insets = useSafeAreaInsets();
  const { user } = useAppSelector((state: RootState) => state.user);
  const [activeTab, setActiveTab] = useState<"overview" | "gallery" | "location" | "activities">("overview");
  const [showTicket, setShowTicket] = useState(false);
  const [ticketData, setTicketData] = useState<any>(null);
  const [useMapFallback, setUseMapFallback] = useState(false);
  const { initiatePayment, loading: paymentLoading } = usePayment();
  const [showPaymentStatus, setShowPaymentStatus] = useState(false);
  const [showProfileValidator, setShowProfileValidator] = useState(false);
  const [pendingJoinAction, setPendingJoinAction] = useState(false);
  const [showTermsAgreement, setShowTermsAgreement] = useState(false);
  const [missingFields, setMissingFields] = useState<string[]>([]);

  const scrollY = useRef(new Animated.Value(0)).current;
  const windowHeight = Dimensions.get("window").height;
  const windowWidth = Dimensions.get("window").width;

  const headerHeight = windowHeight * 0.45;
  const headerImageOpacity = scrollY.interpolate({
    inputRange: [0, headerHeight - 100],
    outputRange: [1, 0.3],
    extrapolate: "clamp",
  });

  const headerTitleOpacity = scrollY.interpolate({
    inputRange: [headerHeight - 150, headerHeight - 100],
    outputRange: [0, 1],
    extrapolate: "clamp",
  });

  const handleJoinHangout = async () => {
    // Check if profile is complete
    const { isComplete, missingFields } = validateUserProfile(user);
    setMissingFields(missingFields);

    if (isComplete) {
      // Profile is complete, show terms agreement directly
      setShowTermsAgreement(true);
    } else {
      // Profile needs completion, show validator
      setShowProfileValidator(true);
      setPendingJoinAction(true);
    }
  };

  const processJoinHangout = async () => {
    try {
      setPendingJoinAction(false);

      if (!hangout.price || hangout.price <= 0) {
        // For free hangouts, directly join
        const response = await joinHangoutMutation.mutateAsync(hangoutId);
        setTicketData(response);
        setShowTicket(true);
      } else {
        // For paid hangouts, initiate payment flow
        const response = await initiatePayment(hangoutId, hangout.title);
        // Show payment status modal
        if (response.result === "cancelled") {
          showToast("info", "Payment cancelled. You can try again later.");
          return;
        } else {
          setShowPaymentStatus(true);
        }
      }
    } catch (error) {
      console.log("Error joining hangout:", error);
      // Show error message based on platform
      const errorMessage = error instanceof Error ? error.message : "Failed to join hangout. Please try again.";
      showToast("error", errorMessage);
    }
  };

  const handleProfileValidatorClose = () => {
    setShowProfileValidator(false);
    setPendingJoinAction(false);
  };

  // Add this new function to handle profile validation completion
  const handleProfileValidationComplete = () => {
    setShowProfileValidator(false);
    // Now show terms agreement
    setShowTermsAgreement(true);
  };

  const handlePaymentSuccess = async () => {
    setShowPaymentStatus(false);
    await handleViewTicket();
    refetch();
  };

  // Add this function to fetch participant details and show ticket
  const handleViewTicket = () => {
    // Navigate to the dedicated ticket page
    router.push(`/hangouts/${hangoutId}/ticket`);
  };

  const handleShareHangout = async () => {
    try {
      // Create a deep link to the hangout details page
      const hangoutLink = getHangoutLink(hangoutId);

      await Share.share({
        message: `Check out this hangout on Logoutloud: ${hangout.title} on ${formatDate(hangout.date)} at ${formatTime(
          hangout.date
        )}. Join me at ${hangout.location?.address}! ${hangoutLink}`,
        title: "Join me at this Logoutloud Hangout!",
        url: hangoutLink, // This will be the clickable link
      });
    } catch (error) {
      console.error("Error sharing hangout:", error);
      showToast("error", "Failed to share hangout");
    }
  };

  if (isLoading) {
    return (
      <View className="flex-1 bg-background">
        <View style={{ height: headerHeight }} className="bg-gray-200" />
        <View className="bg-white rounded-t-[32px] -mt-8 p-4 flex-1 items-center justify-center">
          <ActivityIndicator size="large" color="#4A6FFF" />
          <Text className="mt-4 text-gray-600 font-body">Loading hangout details...</Text>
        </View>
      </View>
    );
  }

  if (error || !data) {
    return (
      <View className="flex-1 bg-background">
        <View style={{ height: headerHeight }} className="bg-gray-200" />
        <View className="bg-white rounded-t-[32px] -mt-8 p-4 flex-1 items-center justify-center">
          <Ionicons name="alert-circle-outline" size={48} color="#f43f5e" />
          <Text className="mt-4 text-gray-600 font-body text-center">
            Failed to load hangout details. Please try again later.
          </Text>
          <TouchableOpacity className="mt-4 bg-primary p-3 rounded-full px-6" onPress={() => router.back()}>
            <Text className="text-white font-body-medium">Go Back</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  const { hangout, participants } = data;

  // Activities data - use type assertion to avoid TypeScript error
  const activities = (hangout as any).activities || [];

  // Handle edit hangout
  const handleEditHangout = () => {
    router.push(`/hangouts/edit/${hangoutId}`);
  };

  return (
    <View className="flex-1 bg-background">
      {/* Header Image */}
      <Animated.View
        style={{
          height: headerHeight,
          opacity: headerImageOpacity,
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          zIndex: 0,
        }}
      >
        <Image source={{ uri: hangout.images[0] }} style={{ width: "100%", height: "100%" }} resizeMode="cover" />
        <LinearGradient
          colors={["rgba(0,0,0,0.5)", "transparent", "rgba(0,0,0,0.7)"]}
          style={{
            position: "absolute",
            left: 0,
            right: 0,
            top: 0,
            height: headerHeight,
          }}
        />

        {/* Date Badge */}
        <View className="absolute top-20 right-5 bg-white/20 backdrop-blur-md rounded-xl p-3">
          <Text className="text-white font-heading">{formatDate(hangout.date)}</Text>
          <Text className="text-white text-xs font-body">{formatTime(hangout.date)}</Text>
        </View>
      </Animated.View>

      {/* Back Button */}
      <View style={{ top: insets.top + 10 }} className="absolute left-4 z-20">
        <TouchableOpacity
          className="w-10 h-10 bg-black/30 rounded-full items-center justify-center"
          onPress={() => router.back()}
        >
          <Ionicons name="chevron-back" size={24} color="white" />
        </TouchableOpacity>
      </View>

      {/* Floating Title - Only visible on scroll */}
      {/* <Animated.View
        style={{
          position: "absolute",
          top: insets.top,
          left: 20,
          right: 0,
          zIndex: 10,
          opacity: headerTitleOpacity,
          padding: 8,
        }}
      >
        <BlurView intensity={80} className="absolute inset-0" />
        <View className="flex-row items-center ml-8  justify-center p-2">
          <Text className="font-heading text-headline-400 text-white text-textColor" numberOfLines={1}>
            {hangout.title}
          </Text>
        </View>
      </Animated.View> */}

      {/* Scrollable Content */}
      <Animated.ScrollView
        showsVerticalScrollIndicator={false}
        onScroll={Animated.event([{ nativeEvent: { contentOffset: { y: scrollY } } }], { useNativeDriver: true })}
        scrollEventThrottle={16}
        style={{ zIndex: 5 }}
      >
        {/* Spacer to push content below header */}
        <View style={{ height: headerHeight - 32 }} />

        {/* Content Container */}
        <View className="bg-white rounded-t-[32px] px-5 pt-6 pb-20">
          {/* Title and Status Row */}
          <View>
            <Text className="font-heading text-xl text-textColor mb-1">{hangout.title}</Text>
            <View className="flex-row items-center mb-4">
              <Ionicons name="location-sharp" size={18} color="#D72638" />
              <Text className="font-body text-body-100 text-gray-600 ml-1">{hangout.location?.address}</Text>
            </View>

            <View className="flex-row items-center justify-between mb-4">
              <View className="flex-row">
                {/* <View
                  className={`rounded-lg px-3 py-2 flex-row items-center ${
                    hangout.status === "completed"
                      ? "bg-gray-100"
                      : hangout.status === "upcoming"
                      ? "bg-primary/20"
                      : "bg-accent/20"
                  }`}
                >
                  <Ionicons
                    name={hangout.status === "completed" ? "checkmark-circle" : "time-outline"}
                    size={14}
                    color={
                      hangout.status === "completed" ? "#6B7280" : hangout.status === "upcoming" ? "#4A6FFF" : "#D72638"
                    }
                  />
                  <Text
                    className={`font-body-medium text-sm ml-1 ${
                      hangout.status === "completed"
                        ? "text-gray-700"
                        : hangout.status === "upcoming"
                        ? "text-primary-600"
                        : "text-accent"
                    } capitalize`}
                  >
                    {hangout.status}
                  </Text>
                </View> */}

                <View className="flex-row items-center ml-3 bg-slate-100 rounded-lg px-3 py-2">
                  <Ionicons name="people" size={14} color="#4A6FFF" />
                  <Text className="font-body text-sm text-textColor ml-1">{participants.length} joined</Text>
                </View>
                <View className="bg-slate-100 rounded-lg px-3 py-2 ml-2">
                  <Text className="font-body text-sm text-textColor">{hangout.duration} hrs</Text>
                </View>
              </View>
              {/* Share Button */}
              <TouchableOpacity
                className="bg-slate-100 rounded-lg px-3 py-2 ml-2 flex-row items-center"
                onPress={handleShareHangout}
              >
                <Ionicons name="share-social-outline" size={14} color="#4A6FFF" />
                <Text className="font-body text-sm text-textColor ml-1">Share</Text>
              </TouchableOpacity>
            </View>

            <View className="h-1 w-full bg-slate-100 mb-6" />
          </View>

          {/* Tabs */}
          <View className="flex-row bg-slate-100 mb-6 rounded-xl overflow-hidden justify-around">
            <TabButton
              title="Overview"
              isActive={activeTab === "overview"}
              onPress={() => setActiveTab("overview")}
              smallScreen={true}
            />
            <TabButton
              title="Activities"
              isActive={activeTab === "activities"}
              onPress={() => setActiveTab("activities")}
              smallScreen={true}
            />
            <TabButton
              title="Location"
              isActive={activeTab === "location"}
              onPress={() => setActiveTab("location")}
              smallScreen={true}
            />
          </View>

          {/* Tab Content */}
          {activeTab === "overview" && (
            <>
              {/* Description */}
              <View className="mb-6">
                <SectionTitle title="About this Hangout" />
                <Text className="font-body text-body-100 text-textColor leading-6">{hangout.description}</Text>
              </View>

              {/* Photos Grid */}
              <View className="mb-6">
                <SectionTitle title="Photos" />
                <ImageGallery
                  images={hangout.images}
                  dynamicSize={true}
                  onImagePress={(index) => {
                    // TODO: Implement image viewer
                  }}
                />
              </View>

              {/* Organizer Section */}
              <View className="mb-6">
                <SectionTitle title="Organizer" />
                <View className="flex-row items-center bg-slate-50 p-4 rounded-xl">
                  <Image source={{ uri: hangout.organizer.profilePic }} className="w-14 h-14 rounded-full" />
                  <View className="ml-3">
                    <Text className="font-heading text-body-100 text-textColor">
                      {hangout.organizer.firstName} {hangout.organizer.lastName}
                    </Text>
                    {hangout.organizerInstagramProfile && <InstagramProfile url={hangout.organizerInstagramProfile} />}
                    {/* <Text className="text-gray-500 text-sm">Hangout Organizer</Text> */}
                  </View>
                </View>
              </View>

              {/* Category Info */}
              <View className="mb-6">
                <SectionTitle title="Category" />
                <View className="bg-slate-50 p-4 rounded-xl flex-row items-center">
                  <View className="w-10 h-10 bg-primary-50 rounded-full items-center justify-center mr-3">
                    <MaterialIcons name="category" size={20} color="#4A6FFF" />
                  </View>
                  <View>
                    <Text className="font-heading text-body-100 text-textColor">
                      {toTitleCase(hangout.category.toLowerCase())}
                    </Text>
                    <Text className="text-gray-500 text-sm">{toTitleCase(hangout.subcategory.toLowerCase())}</Text>
                  </View>
                </View>
              </View>

              {/* Participants Section */}
              {participants.length > 0 && (
                <View className="mb-6">
                  <SectionTitle title="Participants" />
                  <View className="bg-slate-50 p-4 rounded-xl">
                    <View className="flex-row items-center">
                      <ParticipantAvatars avatars={participants.slice(0, 4)} imageSize={42} leftGap={22} />
                      <View className="ml-6">
                        <Text className="text-textColor font-heading">
                          {participants.length}
                          {participants.length > 0 ? "+" : ""} People attending
                        </Text>
                        <Text className="text-gray-500 text-sm">Join them now!</Text>
                      </View>
                    </View>
                  </View>
                </View>
              )}

              {/* Tags Section */}
              {hangout.tags.length > 0 && (
                <View className="mb-6">
                  <SectionTitle title="Tags" />
                  <View className="flex-row flex-wrap">
                    {hangout.tags.map((tag, index) => (
                      <View key={index} className="bg-slate-100 rounded-full px-3 py-1 mr-2 mb-2">
                        <Text className="font-body text-xs text-gray-700">#{tag}</Text>
                      </View>
                    ))}
                  </View>
                </View>
              )}

              {/* Quick Info */}
              {/* <View className="mb-6">
                <SectionTitle title="Quick Info" />
                <View className="bg-slate-50 p-4 rounded-xl">
                  <View className="flex-row items-center mb-3">
                    <View className="w-10 h-10 bg-button rounded-full items-center justify-center mr-3">
                      <Ionicons name="location-outline" size={18} color="white" />
                    </View>
                    <View>
                      <Text className="font-heading text-body-100 text-textColor">Location</Text>
                      <Text className="text-gray-600 text-sm">{hangout.location?.address}</Text>
                    </View>
                  </View>

                  <View className="flex-row items-center mb-3">
                    <View className="w-10 h-10 bg-slate-200 rounded-full items-center justify-center mr-3">
                      <Ionicons name="calendar-outline" size={16} color="gray" />
                    </View>
                    <View>
                      <Text className="font-heading text-body-100 text-textColor">Date & Time</Text>
                      <Text className="text-gray-600 text-sm">
                        {formatDate(hangout.date)} | {formatTime(hangout.date)}
                      </Text>
                    </View>
                  </View>

                  <View className="flex-row items-center">
                    <View className="w-10 h-10 bg-slate-200 rounded-full items-center justify-center mr-3">
                      <MaterialIcons name="access-time" size={16} color="gray" />
                    </View>
                    <View>
                      <Text className="font-heading text-body-100 text-textColor">Duration</Text>
                      <Text className="text-gray-600 text-sm">{hangout.duration} hours</Text>
                    </View>
                  </View>
                </View>
              </View> */}
            </>
          )}

          {activeTab === "activities" &&
            (activities.length > 0 ? (
              <View>
                <View className="mb-6">
                  <SectionTitle title="Activities Included" />
                  <Text className="font-body text-gray-600 mb-4">
                    Check out the activities planned for this hangout
                  </Text>

                  {activities.map((activity: Activity, index: number) => (
                    <HangoutActivityItem key={activity._id} activity={activity} />
                  ))}
                </View>
              </View>
            ) : (
              <View className="flex-1 items-center justify-center py-8">
                <Text className="text-textColor/60">No activities planned for this hangout</Text>
              </View>
            ))}

          {activeTab === "location" && (
            <View className="flex-1">
              <SectionTitle title="Location" />
              {hangout.location && hangout.location.coordinates ? (
                <>
                  {useMapFallback ? (
                    <HangoutLocationFallback location={hangout.location} />
                  ) : (
                    <View>
                      <HangoutLocationMap location={hangout.location} />
                      <TouchableOpacity
                        style={{ marginTop: -8, marginBottom: 16 }}
                        onPress={() => setUseMapFallback(true)}
                      >
                        <Text className="text-center text-textColor/60 font-body text-body-200 mb-4">
                          Having trouble with the map? Try simplified view
                        </Text>
                      </TouchableOpacity>
                    </View>
                  )}
                </>
              ) : (
                <View className="flex-1 items-center justify-center py-8">
                  <Text className="text-textColor/60">No location details available</Text>
                </View>
              )}
            </View>
          )}
        </View>
      </Animated.ScrollView>

      <View
        style={{
          position: "absolute",
          bottom: 0,
          left: 0,
          right: 0,
          paddingBottom: insets.bottom + 10,
          paddingTop: 15,
          paddingHorizontal: 20,
          backgroundColor: "white",
          borderTopWidth: 1,
          borderTopColor: "#f1f5f9",
          zIndex: 20,
        }}
      >
        <View className="flex-row items-center justify-between">
          <View>
            <Text className="text-gray-600 text-xs font-body">Price per person</Text>
            <View className="flex-row items-baseline">
              {hangout.isPaid ? (
                <Text className="font-heading text-2xl text-primary-600">{formatPrice(hangout.price)}</Text>
              ) : (
                <>
                  <Text className="font-heading text-2xl text-gray-400 line-through">{formatPrice(hangout.price)}</Text>
                  <Text className="font-heading text-xl text-primary-600 ml-2">Free</Text>
                </>
              )}
            </View>
          </View>

          {user?.isOrganizer ? (
            <TouchableOpacity className="rounded-xl py-3 px-8 bg-secondary" onPress={handleEditHangout}>
              <Text className="font-heading text-md text-white">Edit Hangout</Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              className={`rounded-xl py-3 px-8 ${
                hangout.status === "completed"
                  ? "bg-gray-200"
                  : joinHangoutMutation.isPending || paymentLoading
                  ? "bg-primary/70"
                  : "bg-primary"
              }`}
              disabled={hangout.status === "completed" || joinHangoutMutation.isPending || paymentLoading}
              onPress={data?.userBookingStatus?.isBooked ? handleViewTicket : handleJoinHangout}
            >
              {joinHangoutMutation.isPending || paymentLoading ? (
                <ActivityIndicator color="white" size="small" />
              ) : (
                <Text
                  className={`font-heading text-md ${
                    hangout.status === "completed" ? "text-gray-500" : "text-textColor"
                  }`}
                >
                  {hangout.status === "completed"
                    ? "Event Ended"
                    : data?.userBookingStatus?.isBooked
                    ? "View Ticket"
                    : hangout.price && hangout.price > 0
                    ? `Pay ₹${hangout.price}`
                    : "Join Now"}
                </Text>
              )}
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Payment Status Modal */}
      {showPaymentStatus && (
        <PaymentStatus
          hangoutId={hangoutId}
          onClose={() => {
            refetch();
            setShowPaymentStatus(false);
          }}
          onSuccess={handlePaymentSuccess}
        />
      )}

      {/* Ticket Modal */}
      {showTicket && ticketData && (
        <Modal visible={showTicket} transparent animationType="fade">
          <HangoutTicket
            hangout={{
              _id: ticketData.hangout._id,
              title: ticketData.hangout.title,
              description: ticketData.hangout.description,
              location: ticketData.hangout.location?.address,
              date: ticketData.hangout.date,
              category: ticketData.hangout.category,
              subcategory: ticketData.hangout.subcategory,
              duration: ticketData.hangout.duration,
              images: ticketData.hangout.images,
            }}
            participant={ticketData.participant}
            qrCode={ticketData.qrCode}
            onClose={() => setShowTicket(false)}
          />
        </Modal>
      )}

      <ProfileValidator
        isVisible={showProfileValidator}
        onClose={handleProfileValidatorClose}
        onComplete={handleProfileValidationComplete}
        missingFields={missingFields}
      />

      {/* Terms Agreement Modal */}
      <TermsAgreementModal
        isVisible={showTermsAgreement}
        onClose={() => setShowTermsAgreement(false)}
        onAccept={() => {
          setShowTermsAgreement(false);
          processJoinHangout();
        }}
        hangoutId={hangoutId}
        isPaid={hangout.isPaid || (hangout.price && hangout.price > 0) ? true : false}
      />
    </View>
  );
};

export default HangoutDetails;
