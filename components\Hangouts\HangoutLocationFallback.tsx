import React from "react";
import { View, Text, TouchableOpacity, Linking, Platform } from "react-native";
import { Ionicons } from "@expo/vector-icons";

interface HangoutLocationFallbackProps {
  location: {
    address: string;
    coordinates: number[];
    placeName?: string;
    placeId?: string;
  };
}

const HangoutLocationFallback: React.FC<HangoutLocationFallbackProps> = ({ location }) => {
  const openInMaps = () => {
    try {
      const latitude = location.coordinates[1];
      const longitude = location.coordinates[0];
      const label = location.placeName || location.address;
      const url =
        Platform.select({
          ios: `maps:0,0?q=${label}@${latitude},${longitude}`,
          android: `geo:0,0?q=${latitude},${longitude}(${label})`,
        }) || `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`;

      Linking.canOpenURL(url).then((supported) => {
        if (supported) {
          Linking.openURL(url);
        } else {
          // Fallback to Google Maps web URL
          Linking.openURL(`https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`);
        }
      });
    } catch (error) {
      console.error("Error opening maps:", error);
    }
  };

  return (
    <View style={{ flex: 1 }}>
      <View
        style={{
          backgroundColor: "white",
          borderRadius: 12,
          padding: 16,
          marginBottom: 16,
          shadowColor: "#000",
          shadowOpacity: 0.1,
          shadowOffset: { width: 0, height: 2 },
          shadowRadius: 4,
          elevation: 2,
        }}
      >
        <View style={{ marginBottom: 16 }}>
          <Text style={{ fontFamily: "heading", fontSize: 18, color: "#333", marginBottom: 8 }}>
            {location.placeName || "Event Location"}
          </Text>
          <Text style={{ fontFamily: "body", fontSize: 14, color: "rgba(51, 51, 51, 0.8)" }}>{location.address}</Text>
        </View>

        <View style={{ marginBottom: 16 }}>
          <Text style={{ fontFamily: "body", fontSize: 14, color: "rgba(51, 51, 51, 0.8)", marginBottom: 4 }}>
            Coordinates:
          </Text>
          <Text style={{ fontFamily: "body", fontSize: 14, color: "rgba(51, 51, 51, 0.8)" }}>
            Latitude: {location.coordinates[1].toFixed(6)}
          </Text>
          <Text style={{ fontFamily: "body", fontSize: 14, color: "rgba(51, 51, 51, 0.8)" }}>
            Longitude: {location.coordinates[0].toFixed(6)}
          </Text>
        </View>

        <TouchableOpacity
          style={{
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "center",
            backgroundColor: "#3b82f6",
            paddingVertical: 12,
            paddingHorizontal: 16,
            borderRadius: 8,
          }}
          onPress={openInMaps}
        >
          <Ionicons name="navigate-outline" size={20} color="white" style={{ marginRight: 8 }} />
          <Text style={{ fontFamily: "heading", fontSize: 14, color: "white" }}>Open in Maps</Text>
        </TouchableOpacity>
      </View>

      <View
        style={{
          backgroundColor: "white",
          borderRadius: 12,
          padding: 16,
          marginBottom: 16,
        }}
      >
        <Text style={{ fontFamily: "body", fontSize: 14, color: "rgba(51, 51, 51, 0.8)", textAlign: "center" }}>
          Map view is currently unavailable.{"\n"}
          Please use the button above to view the location in your maps app.
        </Text>
      </View>
    </View>
  );
};

export default HangoutLocationFallback;
