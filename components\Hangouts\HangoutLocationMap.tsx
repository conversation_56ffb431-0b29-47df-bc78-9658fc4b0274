import React, { useRef, useEffect, useState } from "react";
import { View, Text, Linking, TouchableOpacity, Platform } from "react-native";
import MapView, { <PERSON><PERSON>, PROVIDER_DEFAULT } from "react-native-maps";
import { Ionicons } from "@expo/vector-icons";

interface HangoutLocationMapProps {
  location: {
    address: string;
    coordinates: number[];
    placeName?: string;
    placeId?: string;
  };
}

const HangoutLocationMap: React.FC<HangoutLocationMapProps> = ({ location }) => {
  const mapRef = useRef<MapView>(null);
  const [errorMsg, setErrorMsg] = useState<string | null>(null);
  const [mapError, setMapError] = useState(false);

  // Validate location data
  useEffect(() => {
    try {
      // Check if coordinates are valid numbers
      if (
        typeof location.coordinates[1] !== "number" ||
        typeof location.coordinates[0] !== "number" ||
        isNaN(location.coordinates[1]) ||
        isNaN(location.coordinates[0])
      ) {
        setErrorMsg("Invalid coordinates");
        console.error("Invalid coordinates:", location.coordinates);
        return;
      }

      // Check if coordinates are within valid range
      if (Math.abs(location.coordinates[1]) > 90 || Math.abs(location.coordinates[0]) > 180) {
        setErrorMsg("Coordinates out of range");
        console.error("Coordinates out of range:", location.coordinates);
        return;
      }
    } catch (error) {
      setErrorMsg("Error validating location data");
      console.error("Error validating location data:", error);
    }
  }, [location]);

  useEffect(() => {
    // Animate to the location when component mounts
    if (mapRef.current && location.coordinates && !errorMsg) {
      try {
        setTimeout(() => {
          mapRef.current?.animateToRegion(
            {
              latitude: location.coordinates[1],
              longitude: location.coordinates[0],
              latitudeDelta: 0.01,
              longitudeDelta: 0.01,
            },
            1000
          );
        }, 500);
      } catch (error) {
        setErrorMsg("Error animating to region");
        console.error("Error animating to region:", error);
      }
    }
  }, [location, errorMsg]);

  const openInMaps = () => {
    try {
      const latitude = location.coordinates[1];
      const longitude = location.coordinates[0];
      const label = location.placeName || location.address;
      const url =
        Platform.select({
          ios: `maps:0,0?q=${label}@${latitude},${longitude}`,
          android: `geo:0,0?q=${latitude},${longitude}(${label})`,
        }) || `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`;

      Linking.canOpenURL(url).then((supported) => {
        if (supported) {
          Linking.openURL(url);
        } else {
          // Fallback to Google Maps web URL
          Linking.openURL(`https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`);
        }
      });
    } catch (error) {
      setErrorMsg("Error opening maps");
      console.error("Error opening maps:", error);
    }
  };

  // If there's an error, show error message instead of map
  if (errorMsg || mapError) {
    return (
      <View style={{ flex: 1, justifyContent: "center", alignItems: "center", padding: 16 }}>
        <Text style={{ color: "red", marginBottom: 8 }}>Error: {errorMsg || "Map could not be loaded"}</Text>
        <Text style={{ textAlign: "center", marginBottom: 16 }}>
          There was a problem loading the map. Please try again later.
        </Text>

        {/* Show location details even if map fails */}
        <View style={{ width: "100%", backgroundColor: "white", borderRadius: 12, padding: 16, marginTop: 16 }}>
          <Text style={{ fontFamily: "heading", fontSize: 18, color: "#333", marginBottom: 8 }}>
            {location.placeName || "Event Location"}
          </Text>
          <Text style={{ fontFamily: "body", fontSize: 14, color: "rgba(51, 51, 51, 0.8)", marginBottom: 16 }}>
            {location.address}
          </Text>

          <TouchableOpacity
            style={{
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "center",
              backgroundColor: "#3b82f6",
              paddingVertical: 12,
              paddingHorizontal: 16,
              borderRadius: 8,
            }}
            onPress={openInMaps}
          >
            <Ionicons name="navigate-outline" size={20} color="white" style={{ marginRight: 8 }} />
            <Text style={{ fontFamily: "heading", fontSize: 14, color: "white", marginLeft: 8 }}>Get Directions</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  // Wrap MapView in try-catch to handle potential rendering errors
  try {
    return (
      <View style={{ flex: 1 }}>
        <View style={{ height: 256, borderRadius: 12, overflow: "hidden", marginBottom: 16 }}>
          <MapView
            ref={mapRef}
            style={{ width: "100%", height: "100%" }}
            provider={PROVIDER_DEFAULT}
            initialRegion={{
              latitude: location.coordinates[1],
              longitude: location.coordinates[0],
              latitudeDelta: 0.01,
              longitudeDelta: 0.01,
            }}
            showsCompass
            showsScale
            showsBuildings
            showsTraffic
            showsUserLocation={true}
          >
            <Marker
              coordinate={{
                latitude: location.coordinates[1],
                longitude: location.coordinates[0],
              }}
              title={location.placeName || "Event Location"}
              description={location.address}
            />
          </MapView>
        </View>

        <View
          style={{
            backgroundColor: "white",
            borderRadius: 12,
            padding: 16,
            marginBottom: 16,
            shadowColor: "#000",
            shadowOpacity: 0.1,
            shadowOffset: { width: 0, height: 2 },
            shadowRadius: 4,
            elevation: 2,
          }}
        >
          <Text style={{ fontFamily: "heading", fontSize: 18, color: "#333", marginBottom: 8 }}>
            {location.placeName || "Event Location"}
          </Text>
          <Text style={{ fontFamily: "body", fontSize: 14, color: "rgba(51, 51, 51, 0.8)", marginBottom: 16 }}>
            {location.address}
          </Text>

          <TouchableOpacity
            style={{
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "center",
              backgroundColor: "#3b82f6",
              paddingVertical: 12,
              paddingHorizontal: 16,
              borderRadius: 8,
            }}
            onPress={openInMaps}
          >
            <Ionicons name="navigate-outline" size={20} color="white" style={{ marginRight: 8 }} />
            <Text style={{ fontFamily: "heading", fontSize: 14, color: "white", marginLeft: 8 }}>Get Directions</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  } catch (error) {
    console.error("Error rendering map:", error);
    setMapError(true);
    return (
      <View style={{ flex: 1, justifyContent: "center", alignItems: "center", padding: 16 }}>
        <Text style={{ color: "red", marginBottom: 8 }}>Error: Map could not be loaded</Text>
        <Text style={{ textAlign: "center" }}>There was a problem loading the map. Please try again later.</Text>
      </View>
    );
  }
};

export default HangoutLocationMap;
