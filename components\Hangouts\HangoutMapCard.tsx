import React from "react";
import { View, Text, Image, TouchableOpacity, StyleProp, ViewStyle } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { Hangout } from "../../app/types/hangout";
import { formatDate, formatTime, formatPrice } from "../../lib/utils/formatters";

interface HangoutMapCardProps {
  hangout: Hangout;
  onPress: () => void;
  style?: StyleProp<ViewStyle>;
}

const HangoutMapCard: React.FC<HangoutMapCardProps> = ({ hangout, onPress, style }) => {
  // Use the first image from the images array or a default image
  const imageUri = hangout.featuredImage || "https://via.placeholder.com/300x200?text=No+Image";

  return (
    <TouchableOpacity
      className="bg-white rounded-xl overflow-hidden shadow-md my-2"
      style={style}
      onPress={onPress}
      activeOpacity={0.9}
    >
      <View className="relative h-32">
        <Image source={{ uri: imageUri }} className="w-full h-full" resizeMode="cover" />
        <View className="absolute top-2 right-2 bg-primary/90 px-2 py-1 rounded-full">
          <Text className="font-body-medium text-xs text-white">
            {hangout.status.charAt(0).toUpperCase() + hangout.status.slice(1)}
          </Text>
        </View>
      </View>

      <View className="p-3">
        <Text className="font-heading text-headline-200 text-textColor mb-2" numberOfLines={1}>
          {hangout.title}
        </Text>

        <View className="flex-row items-center mb-1.5">
          <Ionicons name="location-outline" size={12} color="#666" style={{ marginRight: 6 }} />
          <Text className="font-body text-body-100 text-textColor/70 flex-1" numberOfLines={1}>
            {hangout.location.address}
          </Text>
        </View>

        <View className="flex-row items-center mb-1.5">
          <Ionicons name="calendar-outline" size={12} color="#666" style={{ marginRight: 6 }} />
          <Text className="font-body text-body-100 text-textColor/70">
            {formatDate(hangout.date)} | {formatTime(hangout.date)}
          </Text>
        </View>

        <View className="flex-row justify-between items-center mt-2 pt-2 border-t border-gray-100">
          <View className="flex-row items-baseline">
            <Text className="font-body text-xs text-textColor/60 mr-1">Price:</Text>
            <Text className={`font-heading text-headline-400 text-textColor ${!hangout.isPaid ? "line-through" : ""}`}>
              {formatPrice(hangout.price)}
            </Text>
            {!hangout.isPaid && <Text className="font-body-medium text-body-100 text-accent ml-1.5">Free</Text>}
          </View>

          <View className="flex-row items-center">
            <Text className="font-body text-body-100 text-textColor/60 mr-1">
              {hangout.participants.length}/{hangout.maxParticipants}
            </Text>
            <Ionicons name="people" size={12} color="#666" />
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default HangoutMapCard;
