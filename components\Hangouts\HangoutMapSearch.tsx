import React, { useState, useRef, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  FlatList,
  ActivityIndicator,
  Dimensions,
  Animated,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useHangouts } from "../../hooks/useHangouts";
import { HangoutFilters, Hangout } from "../../app/types/hangout";
import MapView, { Marker, PROVIDER_DEFAULT, Region } from "react-native-maps";
import { router } from "expo-router";
import { useLocation } from "../../hooks/useLocation";
import HangoutMapCard from "./HangoutMapCard";
import FilterButton from "../Common/FilterButton";
import { useSafeAreaInsets } from "react-native-safe-area-context";

const { width, height } = Dimensions.get("window");
const CARD_WIDTH = width * 0.8;
const CARD_HEIGHT = height * 0.25;
const SPACING = 10;

interface HangoutMapSearchProps {
  initialRegion?: Region;
  initialFilters?: HangoutFilters;
}

const HangoutMapSearch: React.FC<HangoutMapSearchProps> = ({ initialRegion, initialFilters = {} }) => {
  const insets = useSafeAreaInsets();
  const mapRef = useRef<MapView>(null);
  const flatListRef = useRef<FlatList>(null);
  const [searchText, setSearchText] = useState("");
  const [selectedHangout, setSelectedHangout] = useState<Hangout | null>(null);
  const [filters, setFilters] = useState<HangoutFilters>({
    filter: "popular",
    ...initialFilters,
  });
  const [region, setRegion] = useState<Region>(
    initialRegion || {
      latitude: 12.9716,
      longitude: 77.5946,
      latitudeDelta: 0.0922,
      longitudeDelta: 0.0421,
    }
  );
  const { location, getLocation, isLoading: isLocationLoading } = useLocation();
  const [isFilterVisible, setIsFilterVisible] = useState(false);
  const [activeFilter, setActiveFilter] = useState<string | null>(initialFilters.filter || "popular");
  const [isUsingLocationFilter, setIsUsingLocationFilter] = useState(false);

  // Animation for the bottom card
  const scrollX = useRef(new Animated.Value(0)).current;
  const hangoutsQuery = useHangouts(filters);

  // Get user location on mount, but don't filter by it automatically
  useEffect(() => {
    const fetchLocation = async () => {
      const userLocation = await getLocation();
      if (userLocation) {
        setRegion({
          latitude: userLocation.coordinates.latitude,
          longitude: userLocation.coordinates.longitude,
          latitudeDelta: 0.0922,
          longitudeDelta: 0.0421,
        });
      }
    };

    if (!initialRegion) {
      fetchLocation();
    }
  }, []);

  // Handle search
  const handleSearch = () => {
    setFilters((prev) => ({
      ...prev,
      search: searchText,
    }));
  };

  // Extract all hangouts from the infinite query
  const hangouts = hangoutsQuery.data?.pages.flatMap((page) => page.data) || [];

  // Animate to first hangout when results change
  useEffect(() => {
    // Check if we have hangouts and the query is not loading
    if (hangouts.length > 0 && !hangoutsQuery.isLoading) {
      // Set the first hangout as selected
      setSelectedHangout(hangouts[0]);

      // Reset the flatlist scroll position to the beginning
      flatListRef.current?.scrollToOffset({ offset: 0, animated: false });

      // Animate map to the first hangout location
      mapRef.current?.animateToRegion(
        {
          latitude: hangouts[0].location.coordinates[1],
          longitude: hangouts[0].location.coordinates[0],
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        },
        350
      );
    }
  }, [hangoutsQuery.data, hangoutsQuery.isLoading]);

  // Handle filter selection
  const handleFilterSelect = (filterType: string) => {
    let newFilters: HangoutFilters = { ...filters };

    if (activeFilter === filterType) {
      // Toggle off the filter
      setActiveFilter("popular"); // Default to popular instead of null

      // Remove the specific filter
      if (filterType === "upcoming" || filterType === "ongoing" || filterType === "completed") {
        delete newFilters.status;
      } else if (filterType === "free" || filterType === "paid") {
        delete newFilters.isPaid;
      } else {
        delete newFilters.filter;
      }

      // Set filter back to popular
      newFilters.filter = "popular";
    } else {
      // Set the new filter
      setActiveFilter(filterType);

      // Apply the filter
      if (filterType === "upcoming" || filterType === "ongoing" || filterType === "completed") {
        newFilters.status = filterType;
        delete newFilters.filter; // Remove any existing filter
      } else if (filterType === "free") {
        newFilters.isPaid = false;
        delete newFilters.filter; // Remove any existing filter
      } else if (filterType === "paid") {
        newFilters.isPaid = true;
        delete newFilters.filter; // Remove any existing filter
      } else {
        // For popular filter
        newFilters.filter = filterType;
        // Remove any status or isPaid filters
        delete newFilters.status;
        delete newFilters.isPaid;
      }
    }

    setFilters(newFilters);
  };

  // Toggle location filter
  const toggleLocationFilter = async () => {
    if (isUsingLocationFilter) {
      // Turn off location filter
      const newFilters = { ...filters };
      delete newFilters.location;
      delete newFilters.maxDistance;
      setFilters(newFilters);
      setIsUsingLocationFilter(false);
    } else {
      // Turn on location filter
      const userLocation = location || (await getLocation());
      if (userLocation) {
        setFilters((prev) => ({
          ...prev,
          location: `${userLocation.coordinates.longitude},${userLocation.coordinates.latitude}`,
          maxDistance: 50000, // 50km radius
        }));
        setIsUsingLocationFilter(true);

        // Animate map to user location
        mapRef.current?.animateToRegion(
          {
            latitude: userLocation.coordinates.latitude,
            longitude: userLocation.coordinates.longitude,
            latitudeDelta: 0.05,
            longitudeDelta: 0.05,
          },
          500
        );
      }
    }
  };

  // Handle marker press
  const handleMarkerPress = (hangout: Hangout, index: number) => {
    setSelectedHangout(hangout);

    // Animate to the marker
    mapRef.current?.animateToRegion(
      {
        latitude: hangout.location.coordinates[1],
        longitude: hangout.location.coordinates[0],
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      },
      500
    );

    // Scroll to the card
    flatListRef.current?.scrollToIndex({
      index,
      animated: true,
      viewPosition: 0.5,
    });
  };

  // Handle card press
  const handleCardPress = (hangoutId: string) => {
    router.push(`/hangouts/${hangoutId}`);
  };

  // Handle scroll on the flatlist
  const handleScroll = Animated.event([{ nativeEvent: { contentOffset: { x: scrollX } } }], { useNativeDriver: true });

  // Sync map with card scroll
  useEffect(() => {
    const listener = scrollX.addListener(({ value }) => {
      // Calculate the index more precisely based on the scroll position
      const index = Math.round(value / (CARD_WIDTH + SPACING));

      if (index >= 0 && index < hangouts.length) {
        const hangout = hangouts[index];
        setSelectedHangout(hangout);

        // Don't animate the map during active scrolling
        // Just update the selected hangout
      }
    });

    return () => scrollX.removeListener(listener);
  }, [hangouts, scrollX]);

  return (
    <View className="flex-1 bg-background">
      {/* Search and Filter Header */}
      <View
        className="absolute top-0 left-0 right-0 z-10 bg-white shadow-md px-4 pb-2"
        style={{ paddingTop: insets.top + 8 }}
      >
        <View className="flex-row items-center mb-2">
          <TouchableOpacity className="mr-2 p-2 rounded-full bg-slate-200" onPress={() => router.back()}>
            <Ionicons name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>

          <View className="flex-1 flex-row items-center bg-slate-200 rounded-full px-4 py-2">
            <Ionicons name="search" size={20} color="#666" />
            <TextInput
              className="flex-1 ml-2 font-body text-body-200 text-textColor"
              placeholder="Search hangouts..."
              value={searchText}
              onChangeText={setSearchText}
              onSubmitEditing={handleSearch}
              returnKeyType="search"
            />
            {searchText.length > 0 && (
              <TouchableOpacity onPress={() => setSearchText("")}>
                <Ionicons name="close-circle" size={20} color="#666" />
              </TouchableOpacity>
            )}
          </View>

          <TouchableOpacity
            className="ml-2 p-2 rounded-full bg-slate-200"
            onPress={() => setIsFilterVisible(!isFilterVisible)}
          >
            <Ionicons name="options" size={24} color="#333" />
          </TouchableOpacity>
        </View>

        {isFilterVisible && (
          <View className="flex-row flex-wrap mb-2">
            <FilterButton
              title="Popular"
              isActive={activeFilter === "popular"}
              onPress={() => handleFilterSelect("popular")}
            />
            <FilterButton
              title="Upcoming"
              isActive={activeFilter === "upcoming"}
              onPress={() => handleFilterSelect("upcoming")}
            />
            <FilterButton
              title="Ongoing"
              isActive={activeFilter === "ongoing"}
              onPress={() => handleFilterSelect("ongoing")}
            />
            <FilterButton title="Free" isActive={activeFilter === "free"} onPress={() => handleFilterSelect("free")} />
            <FilterButton title="Paid" isActive={activeFilter === "paid"} onPress={() => handleFilterSelect("paid")} />
            <FilterButton title="Near Me" isActive={isUsingLocationFilter} onPress={toggleLocationFilter} />
          </View>
        )}
      </View>

      {/* Map View */}
      <MapView
        ref={mapRef}
        className="flex-1 w-full h-full"
        provider={PROVIDER_DEFAULT}
        initialRegion={region}
        showsUserLocation
        showsMyLocationButton
      >
        {hangouts.map((hangout, index) => (
          <Marker
            key={hangout._id}
            coordinate={{
              latitude: hangout.location.coordinates[1],
              longitude: hangout.location.coordinates[0],
            }}
            title={hangout.title}
            description={hangout.location.address}
            onPress={() => handleMarkerPress(hangout, index)}
            pinColor={selectedHangout?._id === hangout._id ? "#FF6B6B" : "#3b82f6"}
          >
            <Ionicons name="location" size={36} color={selectedHangout?._id === hangout._id ? "#FF6B6B" : "#3b82f6"} />
          </Marker>
        ))}
      </MapView>

      {/* Loading Indicator */}
      {(hangoutsQuery.isLoading || isLocationLoading) && (
        <View className="absolute top-1/2 left-1/2 -ml-6 -mt-6 bg-white/80 p-3 rounded-lg">
          <ActivityIndicator size="large" color="#3b82f6" />
        </View>
      )}

      {/* Bottom Cards */}
      <View className="absolute bottom-0 left-0 right-0 pb-4" style={{ paddingBottom: insets.bottom + 8 }}>
        {hangouts.length > 0 ? (
          <Animated.FlatList
            ref={flatListRef}
            data={hangouts}
            keyExtractor={(item) => item._id}
            horizontal
            pagingEnabled
            snapToInterval={CARD_WIDTH + SPACING}
            snapToAlignment="center"
            decelerationRate="fast"
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{
              paddingHorizontal: (width - CARD_WIDTH) / 2,
            }}
            onScroll={handleScroll}
            scrollEventThrottle={16} // Back to default for better performance
            renderItem={({ item }) => (
              <HangoutMapCard
                hangout={item}
                onPress={() => handleCardPress(item._id)}
                style={{
                  width: CARD_WIDTH,
                  marginHorizontal: SPACING / 2,
                }}
              />
            )}
            onMomentumScrollEnd={(event) => {
              // Only animate the map when scrolling ends
              const contentOffset = event.nativeEvent.contentOffset.x;
              const index = Math.round(contentOffset / (CARD_WIDTH + SPACING));

              if (index >= 0 && index < hangouts.length) {
                const hangout = hangouts[index];
                setSelectedHangout(hangout);

                // Animate to the marker with a smoother animation
                mapRef.current?.animateToRegion(
                  {
                    latitude: hangout.location.coordinates[1],
                    longitude: hangout.location.coordinates[0],
                    latitudeDelta: 0.01,
                    longitudeDelta: 0.01,
                  },
                  350 // Slightly faster animation for better responsiveness
                );
              }
            }}
          />
        ) : (
          hangoutsQuery.isSuccess && (
            <View className="bg-white mx-4 p-4 rounded-xl shadow-md">
              <Text className="font-heading text-headline-400 text-textColor text-center">No hangouts found</Text>
              <Text className="font-body text-body-200 text-textColor/60 text-center mt-1">
                Try adjusting your filters or search in a different area
              </Text>
            </View>
          )
        )}
      </View>

      {/* My Location Button */}
      <TouchableOpacity
        className="absolute right-4 top-0 mt-56 bg-white p-3 rounded-full shadow-md"
        onPress={() => {
          if (location) {
            mapRef.current?.animateToRegion(
              {
                latitude: location.coordinates.latitude,
                longitude: location.coordinates.longitude,
                latitudeDelta: 0.01,
                longitudeDelta: 0.01,
              },
              500
            );
          } else {
            getLocation();
          }
        }}
      >
        <Ionicons name="locate" size={24} color="#3b82f6" />
      </TouchableOpacity>

      {/* Near Me Filter Button */}
      <TouchableOpacity
        className={`absolute right-4 top-0 mt-72 py-2 px-3 rounded-full shadow-md flex-row items-center ${
          isUsingLocationFilter ? "bg-button" : "bg-white"
        }`}
        onPress={toggleLocationFilter}
      >
        <Ionicons name="locate-outline" size={18} color={isUsingLocationFilter ? "white" : "#3b82f6"} />
        <Text className={`font-body-medium text-xs ml-2 ${isUsingLocationFilter ? "text-white" : "text-button"}`}>
          {isUsingLocationFilter ? "Location Filter On" : "Filter Near Me"}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default HangoutMapSearch;
