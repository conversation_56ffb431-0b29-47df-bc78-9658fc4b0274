import React, { useRef } from "react";
import { View, Text, Image, TouchableOpacity, Share, Platform, Alert } from "react-native";
import { Ionicons, MaterialCommunityIcons, MaterialIcons } from "@expo/vector-icons";
import { formatDate, formatTime } from "../../lib/utils/formatters";
import * as FileSystem from "expo-file-system";
import * as MediaLibrary from "expo-media-library";
import * as Sharing from "expo-sharing";
import ViewShot from "react-native-view-shot";
import { HANGOUT_PARTICIPANT_STATUS, getHangoutLink } from "../../constants/common";

interface HangoutTicketProps {
  hangout: {
    _id: string;
    title: string;
    description: string;
    location: string;
    date: string;
    category: string;
    subcategory: string;
    duration: number;
    images: string[];
  };
  participant: any;
  qrCode: string;
  onClose: () => void;
  fullPage?: boolean; // New prop to indicate if shown as full page
}

const HangoutTicket: React.FC<HangoutTicketProps> = ({ hangout, participant, qrCode, onClose, fullPage = false }) => {
  const ticketRef = useRef<ViewShot>(null);

  const captureTicket = async (): Promise<string | null> => {
    try {
      if (!ticketRef.current) {
        Alert.alert("Error", "Could not capture ticket image");
        return null;
      }

      const uri = await ticketRef.current.capture?.();
      return uri || null;
    } catch (error) {
      console.error("Error capturing ticket:", error);
      Alert.alert("Error", "Failed to capture ticket image");
      return null;
    }
  };

  const downloadTicket = async () => {
    try {
      // Request permissions
      const { status } = await MediaLibrary.requestPermissionsAsync();
      if (status !== "granted") {
        Alert.alert("Sorry, we need media library permissions to save the ticket!");
        return;
      }

      // Capture the ticket view
      const uri = await captureTicket();
      if (!uri) return;

      const filename = `logoutloud-ticket-${participant._id}.png`;
      const fileUri = FileSystem.documentDirectory + filename;

      // Copy the captured image to a permanent location
      await FileSystem.copyAsync({
        from: uri,
        to: fileUri,
      });

      if (Platform.OS === "android") {
        // Save to media library on Android
        const asset = await MediaLibrary.createAssetAsync(fileUri);
        await MediaLibrary.createAlbumAsync("Logoutloud", asset, false);
        Alert.alert("Ticket saved to gallery!");
      } else {
        // Share on iOS
        await Sharing.shareAsync(fileUri);
      }
    } catch (error) {
      console.error("Error saving ticket:", error);
      Alert.alert("Failed to save ticket. Please try again.");
    }
  };

  const shareTicket = async () => {
    try {
      // Capture the ticket view
      const uri = await captureTicket();
      if (!uri) return;

      // Share the image and text
      await Share.share({
        message: `I've joined a hangout on Logoutloud: ${hangout.title} on ${formatDate(hangout.date)} at ${formatTime(
          hangout.date
        )}. \nJoin me - ${getHangoutLink(hangout._id)}`,
        title: "Logoutloud Hangout",
        url: uri, // This will attach the image on supported platforms
      });
    } catch (error) {
      console.error("Error sharing ticket:", error);
      Alert.alert("Failed to share ticket. Please try again.");
    }
  };

  return (
    <View className={`${fullPage ? "flex-1 p-5" : "flex-1 justify-center items-center bg-black/80 p-5"}`}>
      <ViewShot ref={ticketRef} options={{ format: "png", quality: 0.9 }} style={{ width: "100%" }}>
        <View className="w-full bg-white rounded-2xl overflow-hidden shadow-lg">
          {/* Ticket Header */}
          {!fullPage && (
            <TouchableOpacity onPress={onClose} className="absolute top-2 right-2 p-1 bg-gray-100 rounded-full">
              <Ionicons name="close" size={16} color="#2b2b2b" />
            </TouchableOpacity>
          )}

          {/* Ticket ID */}
          <View className="flex-row mt-4 justify-center">
            <Text className="font-body text-body-100 text-textColor/60 mr-1">Ticket ID:</Text>
            <Text className="font-body-medium text-body-100 text-textColor">{participant._id}</Text>
          </View>

          {/* Joined Date */}
          <Text className="font-body text-body-100 text-textColor/60 mt-2 text-center">
            Joined on {new Date(participant.joinedAt).toLocaleDateString()}
          </Text>

          {/* QR Code */}
          <View className="items-center my-5">
            <Image source={{ uri: qrCode }} className="w-[200px] h-[200px] mb-2" />
            <Text className="font-body text-body-100 text-textColor/60 text-center">
              Scan this QR code at the hangout
            </Text>
          </View>

          {/* dashed line with rounded black edges like a ticket */}
          <View className="flex-row justify-center items-center">
            <View className="w-[10px] h-[10px] bg-black" />
            <View className="w-full h-[1px] bg-slate-200" />
            <View className="w-[10px] h-[10px] bg-black " />
          </View>

          {/* Ticket Content */}
          <View className="p-5">
            <View className="mb-3">
              <Text className="text-headline-500 font-heading text-midnightBlue ">{hangout.title}</Text>
              <Text className="font-body text-body-100 text-textColor">{hangout.location}</Text>
            </View>

            <View className="flex-row justify-between mb-3">
              <View className="flex-1">
                <Text className="font-body  text-textColor">Date:</Text>
                <Text className="font-body font-bold text-headline-400 text-textColor">{formatDate(hangout.date)}</Text>
              </View>
              <View className="flex-1 items-end">
                <Text className="font-body text-textColor">Time:</Text>
                <Text className="font-body font-bold text-headline-400 text-textColor">{formatTime(hangout.date)}</Text>
              </View>
            </View>

            <View className="flex-row justify-between">
              <View className="flex-1">
                <Text className="font-body text-textColor mb-1">Duration:</Text>
                <Text className="font-body font-bold text-headline-400 text-textColor">{hangout.duration} hours</Text>
              </View>
              <View className="flex-1 items-end">
                <Text className="font-body text-textColor mb-1">Status:</Text>

                <Text
                  className={`font-body font-bold text-headline-400 ${
                    participant.status === HANGOUT_PARTICIPANT_STATUS.APPROVED ? "text-[#0a8043]" : "text-[#856404]"
                  }`}
                >
                  {participant.status.charAt(0).toUpperCase() + participant.status.slice(1)}
                </Text>
              </View>
            </View>
          </View>

          {/* Ticket Actions */}
          <View className="flex-row gap-2 justify-center p-4 border-t border-slate-200">
            <TouchableOpacity
              className="flex-row items-center bg-midnightBlue py-2.5 px-5 rounded-lg"
              onPress={downloadTicket}
            >
              <MaterialIcons name="downloading" size={20} color="#fff" />
              <Text className="text-white font-body-medium ml-2">Save</Text>
            </TouchableOpacity>

            <TouchableOpacity
              className="flex-row items-center bg-midnightBlue py-2.5 px-5 rounded-lg"
              onPress={shareTicket}
            >
              <MaterialCommunityIcons name="share" size={20} color="#fff" />
              <Text className="text-white font-body-medium ml-2">Share</Text>
            </TouchableOpacity>
          </View>

          {/* Ticket Footer */}
          <View className="items-center py-2 bg-primary">
            <Text className="font-body text-[12px] text-textColor/60">Logoutloud</Text>
          </View>
        </View>
      </ViewShot>

      {/* Add a back button for full page mode */}
      {/* {fullPage && (
        <View className="mt-4 items-center">
          <TouchableOpacity
            className="bg-primary py-3 px-8 rounded-xl"
            onPress={() => {
              // Use router.back() to navigate back
              const router = require("expo-router").router;
              router.back();
            }}
          >
            <Text className="text-white font-heading">Back to Hangout</Text>
          </TouchableOpacity>
        </View>
      )} */}
    </View>
  );
};

export default HangoutTicket;
