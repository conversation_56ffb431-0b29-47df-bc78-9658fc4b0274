import React, { useEffect, useState } from "react";
import { View, Text, Modal, ActivityIndicator, TouchableOpacity } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { usePayment } from "../../hooks/usePayment";
import { HANGOUT_PARTICIPANT_STATUS } from "../../constants/common";

interface PaymentStatusProps {
  hangoutId: string;
  onClose: () => void;
  onSuccess: () => void;
}

const PaymentStatus: React.FC<PaymentStatusProps> = ({ hangoutId, onClose, onSuccess }) => {
  const [status, setStatus] = useState<"processing" | "success" | "failed">("processing");
  const [message, setMessage] = useState("Processing your payment...");
  const [timeoutReached, setTimeoutReached] = useState(false);
  const { checkPaymentStatus } = usePayment();

  useEffect(() => {
    let isMounted = true;
    let attempts = 0;
    const maxAttempts = 10;
    const interval = 3000; // 3 seconds

    const checkStatus = async () => {
      try {
        if (attempts >= maxAttempts) {
          if (isMounted) {
            setTimeoutReached(true);
            setMessage(
              "Payment verification is taking longer than expected. You can close this window and check your tickets later."
            );
          }
          return;
        }

        attempts++;
        const response = await checkPaymentStatus(hangoutId);
        console.log("payment status response", response);
        if (isMounted) {
          if (
            response &&
            response.participantStatus === HANGOUT_PARTICIPANT_STATUS.APPROVED &&
            response.payment.status === "completed"
          ) {
            setStatus("success");
            setMessage("Payment successful!");
            clearInterval(statusInterval);
            setTimeout(() => {
              if (isMounted) {
                onSuccess();
              }
            }, 2000);
          }
        }
      } catch (error) {
        console.error("Error checking payment status:", error);
        if (isMounted && attempts >= maxAttempts) {
          setStatus("failed");
          setMessage(
            "We couldn't verify your payment. If your payment was successful, your ticket will appear in your profile."
          );
        }
      }
    };

    const statusInterval = setInterval(checkStatus, interval);
    checkStatus(); // Check immediately on mount

    // Set a timeout for the overall process
    const timeout = setTimeout(() => {
      if (isMounted && status === "processing") {
        setTimeoutReached(true);
        setMessage(
          "Payment verification is taking longer than expected. You can close this window and check your tickets later."
        );
      }
    }, interval * maxAttempts);

    return () => {
      isMounted = false;
      clearInterval(statusInterval);
      clearTimeout(timeout);
    };
  }, [hangoutId]);

  return (
    <Modal transparent animationType="fade" visible={true}>
      <View className="flex-1 justify-center items-center bg-black/80 p-5">
        <View className="w-full bg-white rounded-2xl overflow-hidden shadow-lg p-6 max-w-md">
          {/* Close button */}
          {(status !== "processing" || timeoutReached) && (
            <TouchableOpacity onPress={onClose} className="absolute top-2 right-2 p-2 z-10">
              <Ionicons name="close" size={24} color="#2b2b2b" />
            </TouchableOpacity>
          )}

          <View className="items-center justify-center py-4">
            {status === "processing" && (
              <View className="items-center">
                <ActivityIndicator size="large" color="#4A6FFF" />
                <Text className="text-lg font-heading text-center mt-4 text-textColor">{message}</Text>
                {timeoutReached && (
                  <TouchableOpacity className="mt-6 bg-secondary py-2 px-6 rounded-lg" onPress={onClose}>
                    <Text className="text-white font-body-medium">Close</Text>
                  </TouchableOpacity>
                )}
              </View>
            )}

            {status === "success" && (
              <View className="items-center">
                <View className="bg-green-100 rounded-full p-4 mb-4">
                  <Ionicons name="checkmark-circle" size={48} color="#10B981" />
                </View>
                <Text className="text-lg font-heading text-center mt-4 text-textColor">{message}</Text>
                <TouchableOpacity className="mt-6 bg-[#0a8043]  py-2 px-6 rounded-lg" onPress={onSuccess}>
                  <Text className="text-white font-body-medium">View Ticket</Text>
                </TouchableOpacity>
              </View>
            )}

            {status === "failed" && (
              <View className="items-center">
                <Ionicons name="alert-circle" size={64} color="#f43f5e" />
                <Text className="text-lg font-heading text-center mt-4 text-textColor">{message}</Text>
                <TouchableOpacity className="mt-6 bg-secondary py-2 px-6 rounded-lg" onPress={onClose}>
                  <Text className="text-white font-body-medium">Close</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default PaymentStatus;
