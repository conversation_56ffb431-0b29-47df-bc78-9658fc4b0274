import React from "react";
import { View, Text, TouchableOpacity } from "react-native";
import { Ionicons } from "@expo/vector-icons";

interface ClearFiltersButtonProps {
  onPress: () => void;
}

const ClearFiltersButton: React.FC<ClearFiltersButtonProps> = ({ onPress }) => {
  return (
    <View
      style={{
        position: "absolute",
        bottom: 24,
        width: "100%",
        alignItems: "center",
        paddingHorizontal: 16,
        zIndex: 10,
      }}
    >
      <TouchableOpacity
        style={{
          backgroundColor: "rgba(0, 0, 0, 0.7)",
          borderRadius: 999,
          paddingVertical: 10,
          paddingHorizontal: 20,
          flexDirection: "row",
          alignItems: "center",
          shadowColor: "#000",
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.25,
          shadowRadius: 3.84,
          elevation: 5,
        }}
        onPress={onPress}
        activeOpacity={0.8}
      >
        <Ionicons name="close-circle" size={18} color="white" />
        <Text
          style={{
            marginLeft: 8,
            color: "white",
            fontFamily: "Roboto-Medium",
            fontSize: 14,
          }}
        >
          Clear Filters
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default ClearFiltersButton;
