import React from "react";
import { View, Text, TouchableOpacity, ScrollView, Animated, Dimensions } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { HangoutFilters } from "../../../app/types/hangout";
import { HANGOUT_CATEGORIES, HANGOUT_STATUSES, HANGOUT_SORT_OPTIONS } from "../../../constants/hangouts";

interface SearchFiltersProps {
  bottomSheetAnim: Animated.Value;
  filterCategory: string | null;
  filterStatus: string | null;
  filterIsPaid: boolean | null;
  filterSortBy: string;
  filterSortOrder: "asc" | "desc";
  onApplyFilters: () => void;
  onResetFilters: () => void;
  onCategoryChange: (category: string | null) => void;
  onStatusChange: (status: string | null) => void;
  onIsPaidChange: (isPaid: boolean | null) => void;
  onSortByChange: (sortBy: string) => void;
  onSortOrderChange: (sortOrder: "asc" | "desc") => void;
}

const { height } = Dimensions.get("window");

const SearchFilters: React.FC<SearchFiltersProps> = ({
  bottomSheetAnim,
  filterCategory,
  filterStatus,
  filterIsPaid,
  filterSortBy,
  filterSortOrder,
  onApplyFilters,
  onResetFilters,
  onCategoryChange,
  onStatusChange,
  onIsPaidChange,
  onSortByChange,
  onSortOrderChange,
}) => {
  const hasActiveFilters = filterCategory || filterStatus || filterIsPaid !== null;

  return (
    <Animated.View
      className="bg-white rounded-t-3xl p-5"
      style={{
        transform: [{ translateY: bottomSheetAnim }],
        maxHeight: height * 0.8,
      }}
    >
      <View className="items-center mb-3">
        <View className="w-10 h-1 bg-gray-300 rounded-full" />
      </View>

      <View className="flex-row justify-between items-center mb-4">
        <Text className="font-heading text-headline-500">Filters & Sorting</Text>
        {hasActiveFilters && (
          <TouchableOpacity onPress={onResetFilters}>
            <Text className="text-link font-body text-body-200">Reset All</Text>
          </TouchableOpacity>
        )}
      </View>

      <ScrollView showsVerticalScrollIndicator={false} className="mb-4">
        {/* Categories */}
        <View className="mb-5">
          <Text className="font-heading text-body-200 mb-2">Category</Text>
          <View className="flex-row flex-wrap">
            {HANGOUT_CATEGORIES.map((category) => (
              <TouchableOpacity
                key={category.id}
                className={`px-3 py-1.5 rounded-xl mr-2 mb-2 ${
                  filterCategory === category.id ? "bg-button" : "bg-slate-50 border border-slate-200"
                }`}
                onPress={() => onCategoryChange(filterCategory === category.id ? null : category.id)}
              >
                <Text
                  className={`text-xs font-body-medium ${
                    filterCategory === category.id ? "text-white" : "text-textColor/70"
                  }`}
                >
                  {category.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Status */}
        <View className="mb-5">
          <Text className="font-heading text-body-200 mb-2">Status</Text>
          <View className="flex-row flex-wrap">
            {HANGOUT_STATUSES.map((status) => (
              <TouchableOpacity
                key={status.id}
                className={`px-3 py-1.5 rounded-xl mr-2 mb-2 ${
                  filterStatus === status.id ? "bg-button" : "bg-slate-50 border border-slate-200"
                }`}
                onPress={() => onStatusChange(filterStatus === status.id ? null : status.id)}
              >
                <Text
                  className={`text-xs font-body-medium ${
                    filterStatus === status.id ? "text-white" : "text-textColor/70"
                  }`}
                >
                  {status.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Price */}
        <View className="mb-5">
          <Text className="font-heading text-body-200 mb-2">Price</Text>
          <View className="flex-row">
            <TouchableOpacity
              className={`px-3 py-1.5 rounded-xl mr-2 ${
                filterIsPaid === false ? "bg-button" : "bg-slate-50 border border-slate-200"
              }`}
              onPress={() => onIsPaidChange(filterIsPaid === false ? null : false)}
            >
              <Text
                className={`text-xs font-body-medium ${filterIsPaid === false ? "text-white" : "text-textColor/70"}`}
              >
                Free
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              className={`px-3 py-1.5 rounded-xl mr-2 ${
                filterIsPaid === true ? "bg-button" : "bg-slate-50 border border-slate-200"
              }`}
              onPress={() => onIsPaidChange(filterIsPaid === true ? null : true)}
            >
              <Text
                className={`text-xs font-body-medium ${filterIsPaid === true ? "text-white" : "text-textColor/70"}`}
              >
                Paid
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Sorting */}
        <View>
          <Text className="font-heading text-body-200 mb-2">Sort By</Text>
          <View className="flex-row flex-wrap mb-3">
            {HANGOUT_SORT_OPTIONS.map((sort) => (
              <TouchableOpacity
                key={sort.id}
                className={`px-3 py-1.5 rounded-xl mr-2 mb-2 ${
                  filterSortBy === sort.id ? "bg-button" : "bg-slate-50 border border-slate-200"
                }`}
                onPress={() => onSortByChange(sort.id)}
              >
                <Text
                  className={`text-xs font-body-medium ${
                    filterSortBy === sort.id ? "text-white" : "text-textColor/70"
                  }`}
                >
                  {sort.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          <View className="flex-row mb-2">
            <TouchableOpacity
              className={`flex-1 px-3 py-1.5 rounded-xl mr-2 ${
                filterSortOrder === "asc" ? "bg-button" : "bg-slate-50 border border-slate-200"
              }`}
              onPress={() => onSortOrderChange("asc")}
            >
              <Text
                className={`text-xs font-body-medium text-center ${
                  filterSortOrder === "asc" ? "text-white" : "text-textColor/70"
                }`}
              >
                Ascending
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              className={`flex-1 px-3 py-1.5 rounded-xl ${
                filterSortOrder === "desc" ? "bg-button" : "bg-slate-50 border border-slate-200"
              }`}
              onPress={() => onSortOrderChange("desc")}
            >
              <Text
                className={`text-xs font-body-medium text-center ${
                  filterSortOrder === "desc" ? "text-white" : "text-textColor/70"
                }`}
              >
                Descending
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>

      <TouchableOpacity className="bg-button rounded-xl py-3 w-full" onPress={onApplyFilters}>
        <Text className="text-white font-body-medium text-sm text-center">Apply Filters</Text>
      </TouchableOpacity>
    </Animated.View>
  );
};

export default SearchFilters;
