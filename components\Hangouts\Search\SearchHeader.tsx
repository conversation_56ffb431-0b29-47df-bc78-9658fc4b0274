import React from "react";
import { View, Text, TextInput, TouchableOpacity, ScrollView } from "react-native";
import { Ionicons, MaterialCommunityIcons } from "@expo/vector-icons";
import FilterButton from "../../Common/FilterButton";

interface SearchHeaderProps {
  searchText: string;
  onSearchChange: (text: string) => void;
  onSearchSubmit: () => void;
  onFilterPress: () => void;
  onBackPress: () => void;
  onClearSearch: () => void;
  activeFilter: string | null;
  onFilterSelect: (filterId: string) => void;
  filterOptions: Array<{ id: string; label: string }>;
  activeFilterCount: number;
}

const SearchHeader: React.FC<SearchHeaderProps> = ({
  searchText,
  onSearchChange,
  onSearchSubmit,
  onFilterPress,
  onBackPress,
  onClearSearch,
  activeFilter,
  onFilterSelect,
  filterOptions,
  activeFilterCount,
}) => {
  return (
    <View style={{ backgroundColor: "white", paddingHorizontal: 16, paddingBottom: 8 }}>
      <View style={{ flexDirection: "row", alignItems: "center", marginBottom: 12 }}>
        <TouchableOpacity style={{ marginRight: 12, padding: 8 }} onPress={onBackPress}>
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>

        <View
          style={{
            flex: 1,
            flexDirection: "row",
            alignItems: "center",
            backgroundColor: "#F3F4F6",
            borderRadius: 12,
            paddingHorizontal: 16,
            paddingVertical: 4,
          }}
        >
          <Ionicons name="search" size={20} color="#666" />
          <TextInput
            style={{
              flex: 1,
              marginLeft: 8,
              fontFamily: "Roboto-Regular",
              fontSize: 14,
              color: "#2E2E2E",
              paddingVertical: 8,
            }}
            placeholder="Search hangouts..."
            value={searchText}
            onChangeText={onSearchChange}
            onSubmitEditing={onSearchSubmit}
            returnKeyType="search"
            autoFocus
          />
          {searchText.length > 0 && (
            <TouchableOpacity onPress={onClearSearch}>
              <Ionicons name="close-circle" size={20} color="#666" />
            </TouchableOpacity>
          )}
        </View>

        <TouchableOpacity
          style={{
            marginLeft: 12,
            padding: 8,
            backgroundColor: activeFilterCount > 0 ? "#F3F4F6" : "transparent",
            borderRadius: 8,
            position: "relative",
          }}
          onPress={onFilterPress}
        >
          <Ionicons name="options" size={24} color={activeFilterCount > 0 ? "#346AFF" : "#333"} />
          {activeFilterCount > 0 && (
            <View
              style={{
                position: "absolute",
                top: 0,
                right: 0,
                backgroundColor: "#346AFF",
                borderRadius: 10,
                width: 20,
                height: 20,
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <Text
                style={{
                  color: "white",
                  fontFamily: "Roboto-Medium",
                  fontSize: 10,
                }}
              >
                {activeFilterCount}
              </Text>
            </View>
          )}
        </TouchableOpacity>
      </View>

      {/* Quick Filters */}
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={{ marginBottom: 12 }}>
        <View style={{ flexDirection: "row" }}>
          {filterOptions.map((filter) => (
            <FilterButton
              key={filter.id}
              title={filter.label}
              isActive={activeFilter === filter.id}
              onPress={() => onFilterSelect(filter.id)}
            />
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

export default SearchHeader;
