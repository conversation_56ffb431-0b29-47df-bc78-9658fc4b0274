import React from "react";
import { View, Text, TouchableOpacity, ScrollView } from "react-native";
import { Ionicons } from "@expo/vector-icons";

interface SearchSuggestionsProps {
  recentSearches: string[];
  trendingSearches: string[];
  onSearchSelect: (search: string) => void;
  onClearHistory: () => void;
}

const SearchSuggestions: React.FC<SearchSuggestionsProps> = ({
  recentSearches,
  trendingSearches,
  onSearchSelect,
  onClearHistory,
}) => {
  return (
    <ScrollView style={{ flex: 1, backgroundColor: "white" }} contentContainerStyle={{ padding: 16 }}>
      {/* Recent Searches */}
      {recentSearches.length > 0 && (
        <View style={{ marginBottom: 24 }}>
          <View
            style={{ flexDirection: "row", justifyContent: "space-between", alignItems: "center", marginBottom: 8 }}
          >
            <Text style={{ fontFamily: "Poppins-SemiBold", fontSize: 14 }}>Recent Searches</Text>
            <TouchableOpacity onPress={onClearHistory}>
              <Text style={{ color: "#346AFF", fontFamily: "Roboto-Medium", fontSize: 14 }}>Clear</Text>
            </TouchableOpacity>
          </View>

          {recentSearches.map((item, index) => (
            <TouchableOpacity
              key={`history-${index}`}
              style={{
                flexDirection: "row",
                alignItems: "center",
                paddingVertical: 12,
                borderBottomWidth: 1,
                borderBottomColor: "#F3F4F6",
              }}
              onPress={() => onSearchSelect(item)}
            >
              <Ionicons name="time-outline" size={18} color="#666" />
              <Text style={{ marginLeft: 12, fontFamily: "Roboto-Regular", fontSize: 14, color: "#2E2E2E" }}>
                {item}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      )}

      {/* Trending Searches */}
      <View>
        <Text style={{ fontFamily: "Poppins-SemiBold", fontSize: 14, marginBottom: 8 }}>Trending Searches</Text>
        <View style={{ flexDirection: "row", flexWrap: "wrap" }}>
          {trendingSearches.map((item, index) => (
            <TouchableOpacity
              key={`trending-${index}`}
              style={{
                backgroundColor: "#F3F4F6",
                paddingHorizontal: 12,
                paddingVertical: 8,
                borderRadius: 999,
                marginRight: 8,
                marginBottom: 8,
              }}
              onPress={() => onSearchSelect(item)}
            >
              <Text style={{ fontFamily: "Roboto-Regular", fontSize: 14, color: "#2E2E2E" }}>{item}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </ScrollView>
  );
};

export default SearchSuggestions;
