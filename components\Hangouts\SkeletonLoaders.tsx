import React from "react";
import { View, StyleSheet } from "react-native";
import SkeletonLoader from "../Common/SkeletonLoader";

export const FeaturedHangoutSkeleton = () => {
  return (
    <View style={styles.featuredContainer}>
      {[1, 2, 3].map((item) => (
        <View key={item} style={styles.featuredCard}>
          <SkeletonLoader width={320} height={224} borderRadius={16} />
        </View>
      ))}
    </View>
  );
};

export const UpcomingHangoutsSkeleton = () => {
  return (
    <View style={styles.upcomingContainer}>
      {[1, 2, 3, 4].map((item) => (
        <View key={item} style={styles.upcomingCard}>
          <View style={styles.upcomingLeft}>
            <SkeletonLoader width={96} height={128} borderRadius={12} />
          </View>
          <View style={styles.upcomingRight}>
            <SkeletonLoader width={200} height={20} />
            <SkeletonLoader width={150} height={16} />
            <SkeletonLoader width={100} height={24} />
            <View style={styles.participantsRow}>
              <SkeletonLoader width={120} height={30} borderRadius={15} />
              <SkeletonLoader width={80} height={20} />
            </View>
          </View>
        </View>
      ))}
    </View>
  );
};

export const LoadMoreSkeleton = () => {
  return (
    <View style={styles.loadMoreContainer}>
      <View style={styles.upcomingCard}>
        <View style={styles.upcomingLeft}>
          <SkeletonLoader width={96} height={128} borderRadius={12} />
        </View>
        <View style={styles.upcomingRight}>
          <SkeletonLoader width={200} height={20} />
          <SkeletonLoader width={150} height={16} />
          <SkeletonLoader width={100} height={24} />
          <View style={styles.participantsRow}>
            <SkeletonLoader width={120} height={30} borderRadius={15} />
            <SkeletonLoader width={80} height={20} />
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  featuredContainer: {
    flexDirection: "row",
    paddingVertical: 16,
  },
  featuredCard: {
    marginRight: 16,
  },
  upcomingContainer: {
    marginTop: 16,
  },
  upcomingCard: {
    flexDirection: "row",
    marginBottom: 16,
    padding: 8,
  },
  upcomingLeft: {
    marginRight: 8,
  },
  upcomingRight: {
    flex: 1,
    justifyContent: "space-between",
    paddingVertical: 4,
    gap: 8,
  },
  participantsRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    marginTop: 8,
  },
  loadMoreContainer: {
    marginTop: 8,
  },
});
