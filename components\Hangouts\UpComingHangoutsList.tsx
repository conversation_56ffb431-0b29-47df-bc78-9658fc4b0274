import React from "react";
import { View } from "react-native";
import ContentCard from "../Common/ContentCard";
import { UpcomingHangoutsSkeleton } from "./SkeletonLoaders";
import { useRouter } from "expo-router";
import HeadingWithCTA from "../HomePage/HeadingWithCTA";
import { SectionTitle } from "../Common/SectionTitle";

interface UpComingHangoutsListProps {
  hangouts: any[];
  isLoading?: boolean;
}

const UpComingHangoutsList: React.FC<UpComingHangoutsListProps> = ({ hangouts, isLoading }) => {
  const router = useRouter();
  if (isLoading) {
    return (
      <View className="mb-8">
        <SectionTitle title="UpComing" />
        <UpcomingHangoutsSkeleton />
      </View>
    );
  }

  if (!hangouts || hangouts.length === 0) {
    return null;
  }

  return (
    <View className="mb-8">
      <SectionTitle title="UpComing" />

      <View className="mt-2">
        {hangouts.map((hangout, index) => (
          <ContentCard
            key={`upcoming-hangout-${hangout._id}`}
            item={hangout}
            featured={false}
            contentType="hangout"
            onPress={() => {
              router.push({
                pathname: `/hangouts/${hangout._id}`,
              });
            }}
          />
        ))}
      </View>
    </View>
  );
};

export default UpComingHangoutsList;
