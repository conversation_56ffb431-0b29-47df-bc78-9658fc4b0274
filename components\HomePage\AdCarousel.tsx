import React, { useEffect, useRef, useState, useCallback } from "react";
import { View, FlatList, Image, ListRenderItemInfo } from "react-native";
import { AdCarouselProps, AdItem, CarouselItemProps } from "../../lib/types/Home/homePageTypes";

// AdCarousel component
export const AdCarousel: React.FC<AdCarouselProps> = ({ data }) => {
  const flatListRef = useRef<FlatList<AdItem>>(null); // Reference to FlatList
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      if (flatListRef.current) {
        flatListRef.current.scrollToIndex({
          index: (currentIndex + 1) % data.length,
          animated: true,
        });
      }
    }, 3000);

    return () => clearInterval(interval);
  }, [currentIndex, data.length]);

  const handleViewableItemsChanged = useCallback(({ viewableItems }: { viewableItems: any[] }) => {
    if (viewableItems.length > 0) {
      setCurrentIndex(viewableItems[0].index);
    }
  }, []);

  return (
    <View>
      <FlatList
        data={data}
        renderItem={({ item }: ListRenderItemInfo<AdItem>) => <CarouselItem item={item} />}
        keyExtractor={(item, index) => index.toString()}
        showsHorizontalScrollIndicator={false}
        ref={flatListRef}
        viewabilityConfig={{
          itemVisiblePercentThreshold: 50,
        }}
        onViewableItemsChanged={handleViewableItemsChanged}
        horizontal
        contentContainerStyle={{ gap: 15 }}
      />
    </View>
  );
};

// CarouselItem component
const CarouselItem: React.FC<CarouselItemProps> = ({ item }) => {
  return (
    <View>
      <Image source={item.src} style={{ width: 250, height: 180, borderRadius: 10 }} resizeMode="cover" />
    </View>
  );
};
