// import { View, Text } from "react-native";
// import React from "react";
// import { Image } from "react-native";
// import { ExploreChoices } from "../../constants/HomePageConstants";

// // DEPRECATED: This component is not used in the current codebase.
// // This component is used to display the Explore options on the Home Page.

// const ExploreOptions = () => {
//   return (
//     <View className="flex-row gap-x-4">
//       {ExploreChoices?.map((option, indx) => {
//         return (
//           <View
//             key={`${indx}-${option.name}`}
//             className="flex-1 bg-white rounded-lg overflow-hidden border-[1px] border-slate-200"
//           >
//             <Image source={option.image} resizeMode="cover" className="w-full h-[75px]" />
//             <View className="bg-black-200 opacity-[0.4] w-full h-[75px] absolute top-0"></View>
//             <View className="w-full h-[75px] absolute top-0 items-center justify-center">
//               <Text className="text-white font-bold text-xl">{option.name}</Text>
//             </View>
//             <Text className="text-xs text-gray-700 py-2 px-6 text-center">{option.description}</Text>
//           </View>
//         );
//       })}
//     </View>
//   );
// };

// export default ExploreOptions;
