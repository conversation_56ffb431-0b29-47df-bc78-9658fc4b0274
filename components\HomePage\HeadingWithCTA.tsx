import React from "react";
import { Text, View, TouchableOpacity } from "react-native";
import { Link } from "expo-router";
import { HeadingWithCTAProps } from "../../lib/types/Home/homePageTypes";

const HeadingWithCTA: React.FC<HeadingWithCTAProps> = ({ heading, cta_text, cta_link }) => {
  return (
    <View className="flex-row items-center justify-between">
      <Text className="text-lg font-heading text-textColor">{heading}</Text>
      <Link href={cta_link} asChild>
        <TouchableOpacity>
          <Text className="text-sm text-[#4A5568]">{cta_text}</Text>
        </TouchableOpacity>
      </Link>
    </View>
  );
};

export default HeadingWithCTA;
