import { View, Text, Image, TouchableOpacity } from "react-native";
import React, { useState, useEffect } from "react";
import NotificationIcon from "../Notification/NotificationIcon";
import { useAppSelector } from "../../reduxStore/hooks";
import { RootState } from "../../reduxStore/store";
import Ionicons from "react-native-vector-icons/Ionicons";
import { toTitleCase } from "../../lib/utils/commonUtils";
import * as Location from "expo-location";
import { LocationUpdateModal } from "../Common/LocationUpdateModal";
import { useQueryClient } from "@tanstack/react-query";
import { useFocusEffect, usePathname } from "expo-router";

// import { useNotificationPermission } from "../../hooks/useNotificationPermission";

interface HomePageHeaderProps {
  darkBackground?: boolean;
}

const HomePageHeader: React.FC<HomePageHeaderProps> = ({ darkBackground = false }) => {
  const user = useAppSelector((state: RootState) => state.user.user);
  const [showLocationSheet, setShowLocationSheet] = useState(false);
  const queryClient = useQueryClient();

  // const { permissionStatus, requestPermission } = useNotificationPermission();
  const pathname = usePathname();

  // Refresh notification count only when home tab is focused
  useFocusEffect(
    React.useCallback(() => {
      // Only invalidate queries when on the home tab
      if (pathname === "/home") {
        // Invalidate and refetch notification count
        queryClient.invalidateQueries({ queryKey: ["notification-count"] });
      }

      // if (permissionStatus !== "granted") {
      //   requestPermission();
      // }

      return () => {
        // Cleanup if needed
      };
    }, [pathname, queryClient])
  );

  // Check location permission status when component mounts
  useEffect(() => {
    const checkPermission = async () => {
      try {
        // Just check if we have permission, but don't store the status
        await Location.getForegroundPermissionsAsync();
      } catch (error) {
        console.error("Error checking location permission:", error);
      }
    };

    checkPermission();
  }, []);

  const handleLocationUpdated = (success: boolean) => {
    console.log("Location updated successfully");
  };

  return (
    <>
      <View className="profile-row flex-row items-center justify-between">
        <View className="flex-row gap-x-2 items-center">
          <View className="w-[50px] h-[50px] bg-slate-500 rounded-full overflow-hidden">
            <Image
              className="w-[50px] h-[50px]"
              source={{ uri: user?.profilePic || "https://randomuser.me/api/portraits/lego/3.jpg" }}
              resizeMode="cover"
            />
          </View>
          <View>
            <TouchableOpacity
              className="flex-row items-center"
              onPress={() => setShowLocationSheet(true)}
              activeOpacity={0.7}
            >
              <View className="flex-row items-center gap-x-[1px]">
                <Ionicons
                  name={user?.location?.address ? "location" : "location-outline"}
                  size={12}
                  color={user?.location?.address ? "#3b82f6" : "#6b7280"}
                />
                <Text
                  className={`text-[12px] font-bold ${user?.location?.address ? "text-blue-500" : "text-gray-500"}`}
                >
                  {user?.location?.city && user?.location?.state
                    ? `${user?.location?.city}, ${user?.location?.state}`
                    : "Set your location"}
                </Text>
                <Ionicons name="chevron-down" size={12} color="#6b7280" className="ml-1" />
              </View>
            </TouchableOpacity>
            <Text className={`text-[16px] font-heading ${darkBackground ? "text-white" : "text-black"}`}>
              {toTitleCase(user?.firstName + " " + user?.lastName)}
            </Text>
          </View>
        </View>
        <View className="flex-row gap-x-4" id="notification-icon">
          {/* <View>
            <NotificationIcon
              iconName="chatbubbles-outline"
              notificationCount={3}
              notificationColor="primary"
              darkBackground={darkBackground}
            />
          </View> */}
          <View>
            <NotificationIcon
              iconName="notifications-outline"
              notificationColor="secondary"
              darkBackground={darkBackground}
            />
          </View>
        </View>
      </View>

      {/* Location Update Modal */}
      <LocationUpdateModal
        visible={showLocationSheet}
        onClose={() => setShowLocationSheet(false)}
        onLocationUpdated={handleLocationUpdated}
      />
    </>
  );
};

export default HomePageHeader;
