import { View, Text, TextInput, StyleSheet, Platform } from "react-native";
import React from "react";
import Feather from "@expo/vector-icons/Feather";

const SearchBar = ({
  title,
  showTitle = true,
  className,
  backgroundColor = "bg-white",
  border = false,
  placeholder,
  disabled = false,
}: {
  title?: string;
  showTitle?: boolean;
  className?: string;
  backgroundColor?: string;
  border?: boolean;
  placeholder: string;
  disabled?: boolean;
}) => {
  return (
    <View className={`gap-y-2 ${className}`}>
      {showTitle && <Text className="text-xl font-heading">{title || "Where's Your Next Stop?"}</Text>}
      <View className="relative">
        <TextInput
          placeholder={placeholder}
          selectionColor="#000000"
          className={`py-4 px-3 rounded-xl text-xs font-body ${backgroundColor} ${
            border ? "border-[1px] border-slate-200" : ""
          }`}
          editable={!disabled}
          pointerEvents={disabled ? "none" : "auto"}
        />
        <Feather name="search" size={20} color="black" style={styles.searchIcon} />
      </View>
    </View>
  );
};

export default SearchBar;

const styles = StyleSheet.create({
  searchIcon: {
    position: "absolute",
    right: 12,
    top: "50%",
    marginTop: Platform.OS === "ios" ? -10 : 0,
    transform: Platform.OS === "ios" ? [] : [{ translateY: -10 }],
  },
});
