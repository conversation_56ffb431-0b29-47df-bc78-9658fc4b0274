import React from "react";
import { View, Text, TouchableOpacity, Image, Dimensions, ScrollView } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { Journey } from "../../api/journeysService";
import { LinearGradient } from "expo-linear-gradient";
import HeadingWithCTA from "../HomePage/HeadingWithCTA";
import { SectionTitle } from "../Common/SectionTitle";

interface Props {
  journeys: Journey[];
  isLoading: boolean;
}

const { width } = Dimensions.get("window");
const cardWidth = width * 0.85; // Slightly smaller for featured cards

const FeaturedJourneysList = ({ journeys, isLoading }: Props) => {
  if (isLoading) {
    return (
      <View className="mb-4">
        <HeadingWithCTA heading="Featured Journeys" cta_text="View all" cta_link="/journeys/search" />
        <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={{ paddingVertical: 16 }}>
          {[1, 2, 3].map((_, index) => (
            <View
              key={index}
              style={{ width: cardWidth, height: 250 }}
              className="mr-4 rounded-2xl bg-gray-100 animate-pulse overflow-hidden"
            >
              <View className="h-6 w-36 bg-gray-200 absolute top-5 left-5 rounded-full animate-pulse" />
              <View className="flex-row absolute bottom-5 left-5 right-5 justify-between">
                <View className="h-5 w-28 bg-gray-200 rounded-full animate-pulse" />
                <View className="h-5 w-20 bg-gray-200 rounded-full animate-pulse" />
              </View>
            </View>
          ))}
        </ScrollView>
      </View>
    );
  }

  if (!journeys?.length) {
    return (
      <View className="mb-4">
        <SectionTitle title="Featured" />
        <View className="bg-white rounded-2xl p-5 mt-4 shadow-sm border border-gray-100">
          <View className="flex-row items-center">
            <View className="bg-primary-50 p-3 rounded-full mr-4">
              <Ionicons name="notifications-outline" size={24} color="#4A6FFF" />
            </View>
            <View className="flex-1">
              <Text className="font-heading text-headline-500">Coming Soon</Text>
              <Text className="font-body text-body-200 text-gray-600 mt-1">
                We'll notify you when featured journeys are available
              </Text>
            </View>
          </View>
        </View>
      </View>
    );
  }

  const getDurationInDays = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  return (
    <View className="mb-4">
      <SectionTitle title="Featured" />
      <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={{ paddingVertical: 16 }}>
        {journeys.map((journey) => (
          <TouchableOpacity
            key={journey._id}
            style={{ width: cardWidth }}
            className="mr-4"
            onPress={() => router.push(`/journeys/${journey._id}`)}
          >
            <View className="rounded-2xl overflow-hidden shadow-md" style={{ height: 250 }}>
              <Image
                source={{ uri: journey.coverImage }}
                style={{ width: cardWidth, height: 250 }}
                resizeMode="cover"
              />

              {/* Date badge */}
              <View className="absolute top-3 right-3 bg-primary-200 rounded-lg p-2 items-center">
                <Text className="font-bold text-textColor">{new Date(journey.startDate).getDate()}</Text>
                <Text className="text-textColor text-xs">
                  {new Date(journey.startDate).toLocaleString("default", { month: "short" })}
                </Text>
              </View>

              {/* Price badge */}
              <View className="absolute top-3 left-3 bg-black/50 px-3 py-1 rounded-full">
                <Text className="text-white font-body-medium">₹{journey.basePrice.toLocaleString()}</Text>
              </View>

              {/* Bottom info panel */}
              <LinearGradient
                colors={["transparent", "rgba(0,0,0,0.8)"]}
                className="absolute bottom-0 left-0 right-0 p-4"
              >
                <Text className="font-heading text-headline-500 text-white mb-2" numberOfLines={1}>
                  {journey.title}
                </Text>

                <View className="flex-row justify-between">
                  <View className="flex-row items-center">
                    <Ionicons name="location-outline" size={14} color="#FFFFFF" />
                    <Text className="font-body text-body-200 text-gray-100 ml-1" numberOfLines={1}>
                      {journey.destinationCity}, {journey.destinationCountry}
                    </Text>
                  </View>
                </View>

                <View className="flex-row items-center mt-1">
                  <Ionicons name="time-outline" size={12} color="white" />
                  <Text className="text-white text-xs ml-1 font-body">
                    {getDurationInDays(journey.startDate, journey.endDate)} Days
                  </Text>

                  {journey.travelMode && journey.travelMode.length > 0 && (
                    <View className="flex-row ml-3">
                      {journey.travelMode.map((mode, index) => (
                        <View key={index} className="bg-white/20 rounded-full w-5 h-5 items-center justify-center ml-1">
                          <Ionicons
                            name={
                              mode.toLowerCase() === "flight"
                                ? "airplane"
                                : mode.toLowerCase() === "car"
                                ? "car"
                                : mode.toLowerCase() === "boat"
                                ? "boat"
                                : mode.toLowerCase() === "train"
                                ? "train"
                                : mode.toLowerCase() === "bus"
                                ? "bus"
                                : mode.toLowerCase() === "ship"
                                ? "boat"
                                : mode.toLowerCase() === "jeep"
                                ? "car"
                                : "walk"
                            }
                            size={12}
                            color="#FFFFFF"
                          />
                        </View>
                      ))}
                    </View>
                  )}
                </View>
              </LinearGradient>
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

export default FeaturedJourneysList;
