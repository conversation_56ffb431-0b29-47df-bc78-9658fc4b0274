import React, { useState, useEffect } from "react";
import { View, Text, Modal, TouchableOpacity, ScrollView, TextInput, ActivityIndicator, Alert } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useJourneyPayment } from "../../hooks/useJourneyPayment";
import { formatPrice } from "../../lib/utils/formatters";

interface PricingTier {
  _id: string;
  name: string;
  price: number;
  benefits: string[];
  availableUntil: string;
}

interface JourneyBookingModalProps {
  isVisible: boolean;
  onClose: () => void;
  onProceed: (selectedTier: string, promoCode?: string) => void;
  journey: {
    _id: string;
    title: string;
    pricingTiers: PricingTier[];
    promoCode?: string;
    promoDiscount?: number;
  };
}

const JourneyBookingModal: React.FC<JourneyBookingModalProps> = ({ isVisible, onClose, onProceed, journey }) => {
  const [selectedTier, setSelectedTier] = useState<string>("");
  const [promoCode, setPromoCode] = useState<string>("");
  const [finalPrice, setFinalPrice] = useState<number>(0);
  const [originalPrice, setOriginalPrice] = useState<number>(0);
  const { validatePromoCode, promoCodeValidation, loading, clearPromoValidation } = useJourneyPayment();

  // Reset state when modal opens
  useEffect(() => {
    if (isVisible) {
      setSelectedTier("");
      setPromoCode("");
      setFinalPrice(0);
      setOriginalPrice(0);
      clearPromoValidation();
    }
  }, [isVisible]);

  // Update price when tier is selected
  useEffect(() => {
    if (selectedTier) {
      const tier = journey.pricingTiers.find((t) => t._id === selectedTier);
      if (tier) {
        setOriginalPrice(tier.price);

        // Apply promo code discount if validated
        if (promoCodeValidation?.valid) {
          const discountAmount = tier.price * (promoCodeValidation.discountPercentage / 100);
          setFinalPrice(tier.price - discountAmount);
        } else {
          setFinalPrice(tier.price);
        }
      }
    }
  }, [selectedTier, promoCodeValidation]);

  const handleValidatePromoCode = async () => {
    if (!promoCode.trim()) {
      Alert.alert("Error", "Please enter a promo code");
      return;
    }

    if (!selectedTier) {
      Alert.alert("Error", "Please select a pricing tier first");
      return;
    }

    try {
      await validatePromoCode(journey._id, promoCode);
    } catch (error) {
      console.error("Failed to validate promo code:", error);
    }
  };

  const handleProceed = () => {
    if (!selectedTier) {
      Alert.alert("Error", "Please select a pricing tier");
      return;
    }

    onProceed(selectedTier, promoCodeValidation?.valid ? promoCode : undefined);
  };

  const isCurrentPricingAvailable = (tier: PricingTier) => {
    const now = new Date();
    const availableUntil = new Date(tier.availableUntil);
    return availableUntil > now;
  };

  return (
    <Modal visible={isVisible} transparent animationType="slide">
      <View className="flex-1 bg-black/50 justify-end">
        <View className="bg-white rounded-t-3xl p-5 max-h-[80%]">
          <View className="flex-row justify-between items-center mb-4">
            <Text className="font-heading text-xl text-textColor">Book Your Journey</Text>
            <TouchableOpacity onPress={onClose} className="p-2">
              <Ionicons name="close" size={24} color="#2b2b2b" />
            </TouchableOpacity>
          </View>

          <ScrollView showsVerticalScrollIndicator={false}>
            <Text className="font-heading text-lg text-textColor mb-2">Select Pricing Tier</Text>

            {journey.pricingTiers.map((tier) => (
              <TouchableOpacity
                key={tier._id}
                className={`border rounded-xl p-4 mb-3 ${
                  selectedTier === tier._id
                    ? "border-primary bg-primary/10"
                    : isCurrentPricingAvailable(tier)
                    ? "border-gray-200"
                    : "border-gray-200 opacity-50"
                }`}
                onPress={() => isCurrentPricingAvailable(tier) && setSelectedTier(tier._id)}
                disabled={!isCurrentPricingAvailable(tier)}
              >
                <View className="flex-row justify-between items-center mb-2">
                  <View>
                    <Text className="font-heading text-lg text-textColor">{tier.name}</Text>
                    <Text className="text-primary font-heading text-2xl">₹{tier.price.toLocaleString()}</Text>
                  </View>
                  {selectedTier === tier._id && (
                    <View className="bg-primary p-2 rounded-full">
                      <Ionicons name="checkmark" size={20} color="#2e2e2e" />
                    </View>
                  )}
                </View>

                <View className="mt-2">
                  {tier.benefits.map((benefit, index) => (
                    <View key={index} className="flex-row items-center mb-1">
                      <View className="w-5 h-5 bg-primary/20 rounded-full items-center justify-center mr-2">
                        <Ionicons name="checkmark" size={12} color="#4A6FFF" />
                      </View>
                      <Text className="font-body text-textColor">{benefit}</Text>
                    </View>
                  ))}
                </View>

                {!isCurrentPricingAvailable(tier) && (
                  <Text className="font-body text-red-500 mt-2">No longer available</Text>
                )}
              </TouchableOpacity>
            ))}

            {/* Promo Code Section */}
            <View className="mt-4 mb-6">
              <Text className="font-heading text-lg text-textColor mb-2">Promo Code</Text>
              <View className="flex-row">
                <TextInput
                  className="border border-gray-300 rounded-l-lg p-3 flex-1 border-r-0"
                  value={promoCode}
                  onChangeText={setPromoCode}
                  placeholder="Enter promo code"
                  autoCapitalize="characters"
                />
                <TouchableOpacity
                  className="bg-primary rounded-r-lg px-4 justify-center border border-gray-300 border-l-0"
                  onPress={handleValidatePromoCode}
                  disabled={loading || !selectedTier}
                >
                  {loading ? (
                    <ActivityIndicator size="small" color="#2e2e2e" />
                  ) : (
                    <Text className="font-body-medium text-textColor">Apply</Text>
                  )}
                </TouchableOpacity>
              </View>

              {promoCodeValidation && (
                <Text className={`mt-2 font-body ${promoCodeValidation.valid ? "text-green-600" : "text-red-500"}`}>
                  {promoCodeValidation.message}
                </Text>
              )}
            </View>

            {/* Price Summary */}
            {selectedTier && (
              <View className="bg-gray-50 p-4 rounded-xl mb-6">
                <Text className="font-heading text-lg text-textColor mb-2">Price Summary</Text>

                <View className="flex-row justify-between mb-2">
                  <Text className="font-body text-textColor">Base Price</Text>
                  <Text className="font-body-medium text-textColor">₹{originalPrice.toLocaleString()}</Text>
                </View>

                {promoCodeValidation?.valid && (
                  <View className="flex-row justify-between mb-2">
                    <Text className="font-body text-textColor">
                      Discount ({promoCodeValidation.discountPercentage}%)
                    </Text>
                    <Text className="font-body-medium text-green-600">
                      -₹{(originalPrice * (promoCodeValidation.discountPercentage / 100)).toLocaleString()}
                    </Text>
                  </View>
                )}

                <View className="border-t border-gray-200 my-2" />

                <View className="flex-row justify-between">
                  <Text className="font-heading text-textColor">Final Price</Text>
                  <Text className="font-heading text-xl text-primary">₹{finalPrice.toLocaleString()}</Text>
                </View>
              </View>
            )}
          </ScrollView>

          <TouchableOpacity
            className={`rounded-xl py-3 px-8 ${selectedTier ? "bg-primary" : "bg-gray-300"}`}
            onPress={handleProceed}
            disabled={!selectedTier || loading}
          >
            {loading ? (
              <ActivityIndicator color="#2e2e2e" size="small" />
            ) : (
              <Text className="font-heading text-md text-textColor">
                {selectedTier ? `Proceed with ₹${finalPrice.toLocaleString()}` : "Select a Tier"}
              </Text>
            )}
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

export default JourneyBookingModal;
