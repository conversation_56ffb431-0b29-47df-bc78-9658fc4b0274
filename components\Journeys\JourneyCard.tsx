import React from "react";
import { View, Text, TouchableOpacity, Image } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { Journey } from "../../api/journeysService";

interface JourneyCardProps {
  journey: Journey;
  onPress: () => void;
}

const JourneyCard: React.FC<JourneyCardProps> = ({ journey, onPress }) => {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });
  };

  const getDurationInDays = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  return (
    <TouchableOpacity
      className="flex-row p-3 mb-3 rounded-2xl overflow-hidden border-[1px] border-slate-200 active:bg-gray-50 relative"
      onPress={onPress}
    >
      {/* Bookmark icon */}
      {/* <TouchableOpacity className="absolute top-2 right-2 z-10">
        <Ionicons name="bookmark-outline" size={20} color="#6B7280" />
      </TouchableOpacity> */}

      <View className="rounded-xl w-[90px] h-[115px] relative">
        <Image source={{ uri: journey.coverImage }} className="w-full h-full rounded-xl" resizeMode="cover" />
        <LinearGradient
          colors={["transparent", "rgba(0,0,0,0.5)"]}
          className="absolute bottom-0 left-0 right-0 p-2 rounded-xl"
        />
        <View className="absolute bottom-2 left-2 bg-black/60 rounded-lg px-2 py-1">
          <Text className="text-white font-body text-[10px]">
            {getDurationInDays(journey.startDate, journey.endDate)}d
          </Text>
        </View>
      </View>

      <View className="ml-3 flex-1 justify-between">
        <View>
          <Text className="font-heading text-textColor w-[80%]" numberOfLines={1}>
            {journey.title}
          </Text>

          <View className="flex-row items-center mt-1">
            <Ionicons name="location-outline" size={12} color="#D72638" />
            <Text className="text-gray-600 text-xs ml-1 font-body" numberOfLines={1}>
              {journey.destinationCity}, {journey.destinationCountry}
            </Text>
          </View>

          <View className="flex-row items-center mt-2">
            <Ionicons name="calendar-outline" size={12} color="#6B7280" />
            <Text className="text-gray-500 font-body text-xs ml-1">{formatDate(journey.startDate)}</Text>
          </View>
        </View>

        <View className="flex-row justify-between items-center mt-3">
          <View className="flex-row flex-wrap">
            {journey.travelMode?.slice(0, 3).map((mode, index) => (
              <View key={index} className="flex-row items-center bg-gray-100 rounded-full mr-1 px-2 py-0.5">
                <Ionicons
                  name={
                    mode.toLowerCase() === "flight"
                      ? "airplane"
                      : mode.toLowerCase() === "car"
                      ? "car"
                      : mode.toLowerCase() === "boat"
                      ? "boat"
                      : mode.toLowerCase() === "train"
                      ? "train"
                      : mode.toLowerCase() === "bus"
                      ? "bus"
                      : mode.toLowerCase() === "ship"
                      ? "boat"
                      : mode.toLowerCase() === "jeep"
                      ? "car"
                      : "walk"
                  }
                  size={10}
                  color="#6B7280"
                />
                <Text className="text-gray-600 text-[9px] ml-1 font-body">{mode}</Text>
              </View>
            ))}
          </View>

          <Text className="text-primary-600 font-heading text-headline-300">₹{journey.basePrice.toLocaleString()}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default JourneyCard;
