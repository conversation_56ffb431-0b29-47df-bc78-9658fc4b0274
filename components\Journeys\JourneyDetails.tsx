import React, { useState, useRef } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Dimensions,
  Animated,
  Image,
  ActivityIndicator,
  Alert,
  Platform,
  Share,
} from "react-native";
import { Ionicons, FontAwesome5 } from "@expo/vector-icons";
import { router } from "expo-router";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { BlurView } from "expo-blur";
import { useJourneyDetails } from "../../hooks/useJourneyDetails";
import { LinearGradient } from "expo-linear-gradient";
import { SectionTitle } from "../Common/SectionTitle";
import TabButton from "../Common/TabButton";
import ImageGallery from "../Common/ImageGallery";
import { formatDate } from "../../lib/utils/formatters";
import JourneyBookingModal from "./JourneyBookingModal";
import JourneyPaymentStatus from "./JourneyPaymentStatus";
import ProfileValidator from "../Profile/ProfileValidator";
import JourneyTermsAgreementModal from "./JourneyTermsAgreementModal";
import { useJourneyPayment } from "../../hooks/useJourneyPayment";
import { showToast } from "../../lib/utils/showToast";
import { validateUserProfile } from "../../lib/utils/profileValidation";
import { useAppSelector } from "../../reduxStore/hooks";
import { RootState } from "../../reduxStore/store";
import InstagramProfile from "../Profile/InstagramProfile";
import { journeysService } from "../../api/journeysService";
import JourneyUpiPaymentModal from "./JourneyUpiPaymentModal";
import { JOURNEY_PARTICIPANT_STATUS } from "../../constants/journeys";
import { getJourneyLink } from "../../constants/common";

interface JourneyDetailsProps {
  journeyId: string;
}

// Helper Components
const BenefitItem = ({ text }: { text: string }) => (
  <View className="flex-row items-center mb-2">
    <View className="w-6 h-6 bg-green-100 rounded-full items-center justify-center">
      <Ionicons name="checkmark" size={16} color="#10b981" />
    </View>
    <Text className="ml-2 font-body text-textColor">{text}</Text>
  </View>
);

const PackingItem = ({ text }: { text: string }) => (
  <View className="flex-row items-center mb-2">
    <View className="w-6 h-6 bg-blue-100 rounded-full items-center justify-center">
      <Ionicons name="bag-handle" size={14} color="#4A6FFF" />
    </View>
    <Text className="ml-2 font-body text-textColor">{text}</Text>
  </View>
);

const Tag = ({ text }: { text: string }) => (
  <View className="bg-slate-100 rounded-full px-3 py-1 mr-2 mb-2">
    <Text className="font-body text-xs text-gray-700">#{text}</Text>
  </View>
);

const DifficultyBadge = ({ level }: { level: string }) => {
  let bgColor = "bg-green-100";
  let textColor = "text-green-800";
  let icon = "leaf";

  switch (level.toLowerCase()) {
    case "easy":
      bgColor = "bg-green-100";
      textColor = "text-green-800";
      icon = "leaf";
      break;
    case "moderate":
      bgColor = "bg-yellow-100";
      textColor = "text-yellow-800";
      icon = "hiking";
      break;
    case "difficult":
      bgColor = "bg-red-100";
      textColor = "text-red-800";
      icon = "mountain";
      break;
    default:
      break;
  }

  return (
    <View className={`${bgColor} rounded-lg px-3 py-2 flex-row items-center`}>
      <FontAwesome5 name={icon} size={14} color={textColor.replace("text-", "")} />
      <Text className={`ml-1 font-body-medium text-sm ${textColor} capitalize`}>{level}</Text>
    </View>
  );
};

const PriceCard = ({
  name,
  price,
  benefits,
  available,
}: {
  name: string;
  price: number;
  benefits: string[];
  available: boolean;
}) => (
  <View className={`border rounded-xl p-4 mb-3 ${available ? "border-gray-100" : "border-gray-100 opacity-40"}`}>
    <View className="flex-row justify-between items-center mb-2">
      <View>
        <Text className="font-heading text-lg text-textColor">{name}</Text>
        <Text className="text-primary-600 font-heading text-2xl">₹{price.toLocaleString()}</Text>
      </View>
      {available && (
        <View className="bg-primary-50 p-2 rounded-full">
          <Ionicons name="checkmark-circle" size={24} color="#4A6FFF" />
        </View>
      )}
    </View>
    <View className="mt-2">
      {benefits.map((benefit, index) => (
        <BenefitItem key={index} text={benefit} />
      ))}
    </View>
    {!available && <Text className="text-gray-500 text-xs mt-2 italic">This pricing tier is no longer available</Text>}
  </View>
);

const ItineraryDay = ({ day, isLast }: { day: any; isLast: boolean }) => {
  const [expanded, setExpanded] = useState(false);

  return (
    <View className="mb-4">
      <TouchableOpacity
        className="flex-row items-center bg-white border border-slate-200 rounded-xl p-4"
        onPress={() => setExpanded(!expanded)}
      >
        <View className="w-10 h-10 bg-primary-50 rounded-full items-center justify-center mr-3">
          <Text className="font-heading text-primary-600">D{day.dayNumber}</Text>
        </View>
        <View className="flex-1">
          <Text className="font-heading text-body-100" numberOfLines={expanded ? undefined : 1}>
            {day.title}
          </Text>
          <Text className="text-gray-500 text-xs">{formatDate(day.date)}</Text>
        </View>
        <Ionicons name={expanded ? "chevron-up" : "chevron-down"} size={20} color="gray" />
      </TouchableOpacity>

      {expanded && (
        <View className="bg-slate-50 p-4 rounded-xl mt-2 mb-4">
          <Text className="font-body text-textColor mb-3">{day.description}</Text>

          {day.activities.length > 0 && (
            <View className="mb-3">
              <Text className="font-heading text-sm text-textColor mb-2">Activities:</Text>
              {day.activities.map((activity: any, index: number) => (
                <View key={index} className="flex-row mb-2">
                  <View className="w-1 h-full bg-primary-100 mr-2 rounded-full" />
                  <View className="flex-1">
                    <Text className="font-body-medium text-textColor">{activity.name}</Text>
                    <Text className="text-xs text-gray-500">
                      {activity.startTime} - {activity.endTime}
                    </Text>
                    <Text className="text-xs text-gray-500">{activity.location?.name}</Text>
                  </View>
                </View>
              ))}
            </View>
          )}

          {day.accommodation && (
            <View className="mb-3">
              <Text className="font-heading text-sm text-textColor mb-2">Accommodation:</Text>
              <View className="bg-white p-3 rounded-lg border border-slate-200">
                <Text className="font-body-medium text-textColor">{day.accommodation.name}</Text>
                <Text className="text-xs text-gray-500">{day.accommodation.address}</Text>
                <Text className="text-xs text-gray-500">
                  Check-in: {day.accommodation.checkInTime} | Check-out: {day.accommodation.checkOutTime}
                </Text>

                {day.accommodation.amenities.length > 0 && (
                  <View className="flex-row flex-wrap mt-2">
                    {day.accommodation.amenities.map((amenity: string, idx: number) => (
                      <View key={idx} className="bg-slate-100 rounded-full px-2 py-1 mr-1 mb-1">
                        <Text className="text-[10px] text-gray-700">{amenity}</Text>
                      </View>
                    ))}
                  </View>
                )}
              </View>
            </View>
          )}

          <View className="flex-row items-center mt-2">
            <View className="flex-row items-center mr-4">
              <Ionicons name="restaurant" size={14} color={day.meals.breakfast ? "#4A6FFF" : "gray"} />
              <Text className={`ml-1 text-xs ${day.meals.breakfast ? "text-textColor" : "text-gray-400"}`}>
                Breakfast
              </Text>
            </View>
            <View className="flex-row items-center mr-4">
              <Ionicons name="restaurant" size={14} color={day.meals.lunch ? "#4A6FFF" : "gray"} />
              <Text className={`ml-1 text-xs ${day.meals.lunch ? "text-textColor" : "text-gray-400"}`}>Lunch</Text>
            </View>
            <View className="flex-row items-center">
              <Ionicons name="restaurant" size={14} color={day.meals.dinner ? "#4A6FFF" : "gray"} />
              <Text className={`ml-1 text-xs ${day.meals.dinner ? "text-textColor" : "text-gray-400"}`}>Dinner</Text>
            </View>
          </View>
        </View>
      )}

      {!isLast && (
        <View className="flex-row justify-center items-center">
          <View className="h-6 w-[1px] bg-gray-200" />
        </View>
      )}
    </View>
  );
};

// Main Component
const JourneyDetails: React.FC<JourneyDetailsProps> = ({ journeyId }) => {
  const { data: journey, userBookingStatus, isLoading, error, refetch } = useJourneyDetails(journeyId);
  const insets = useSafeAreaInsets();
  const [activeTab, setActiveTab] = useState<"overview" | "itinerary" | "reviews" | "pricing">("overview");
  const scrollY = useRef(new Animated.Value(0)).current;
  const windowHeight = Dimensions.get("window").height;

  const headerHeight = windowHeight * 0.45;
  const headerImageOpacity = scrollY.interpolate({
    inputRange: [0, headerHeight - 100],
    outputRange: [1, 0.3],
    extrapolate: "clamp",
  });

  const [showBookingModal, setShowBookingModal] = useState(false);
  const [showPaymentStatus, setShowPaymentStatus] = useState(false);
  const [showProfileValidator, setShowProfileValidator] = useState(false);
  const [showTermsAgreement, setShowTermsAgreement] = useState(false);
  const [selectedTierId, setSelectedTierId] = useState<string>("");
  const [promoCode, setPromoCode] = useState<string | undefined>(undefined);
  const { initiatePayment, loading: paymentLoading } = useJourneyPayment();
  const [missingFields, setMissingFields] = useState<string[]>([]);
  const [showUpiPaymentModal, setShowUpiPaymentModal] = useState(false);
  const [pendingBooking, setPendingBooking] = useState<{ journeyId: string; participantId: string } | null>(null);

  const { user } = useAppSelector((state: RootState) => state.user);

  const handleBookNow = () => {
    // Check if profile is complete
    const { isComplete, missingFields } = validateUserProfile(user);
    setMissingFields(missingFields);

    if (isComplete) {
      // Profile is complete, show agreement modal first
      setShowTermsAgreement(true);
    } else {
      // Profile needs completion, show validator
      setShowProfileValidator(true);
    }
  };

  const handleProfileValidatorClose = () => {
    setShowProfileValidator(false);
  };

  const handleProfileValidationComplete = () => {
    setShowProfileValidator(false);
    // Now show agreement modal
    setShowTermsAgreement(true);
  };

  const handleBookingModalClose = () => {
    setShowBookingModal(false);
  };

  const handleProceedWithBooking = async (selectedTier: string, promoCodeValue?: string) => {
    try {
      // Book journey (creates pending participant)
      const bookingResponse = await journeysService.bookJourney(journeyId, selectedTier);
      if (bookingResponse.requiresPayment) {
        setPendingBooking({ journeyId, participantId: bookingResponse.participantId });
        setShowUpiPaymentModal(true);
      } else {
        // Booking confirmed (free journey)
        showToast("success", "Booking confirmed!");
        setShowBookingModal(false);
      }
    } catch (error) {
      showToast("error", "Booking failed. Please try again.");
    }
  };

  const processJourneyBooking = async () => {
    try {
      if (!journey) {
        throw new Error("Journey details not available");
      }

      // For paid journeys, initiate payment flow
      await initiatePayment(journeyId, journey.title, selectedTierId, promoCode);

      // Show payment status modal
      setShowPaymentStatus(true);
    } catch (error) {
      console.log("Error booking journey:", error);

      // Show error message based on platform
      const errorMessage = error instanceof Error ? error.message : "Failed to book journey. Please try again.";
      showToast("error", errorMessage);
    }
  };

  const handlePaymentSuccess = () => {
    setShowPaymentStatus(false);
    // Navigate to the dedicated ticket page
    router.push(`/journeys/${journeyId}/ticket`);
    refetch();
  };

  const handleViewTicket = () => {
    // Navigate to the dedicated ticket page
    router.push(`/journeys/${journeyId}/ticket`);
  };

  if (isLoading) {
    return (
      <View className="flex-1 bg-background">
        <View style={{ height: headerHeight }} className="bg-gray-200" />
        <View className="bg-white rounded-t-[32px] -mt-8 p-4 flex-1 items-center justify-center">
          <ActivityIndicator size="large" color="#4A6FFF" />
          <Text className="mt-4 text-gray-600 font-body">Loading journey details...</Text>
        </View>
      </View>
    );
  }

  if (error || !journey) {
    return (
      <View className="flex-1 bg-background">
        <View style={{ height: headerHeight }} className="bg-gray-200" />
        <View className="bg-white rounded-t-[32px] -mt-8 p-4 flex-1 items-center justify-center">
          <Ionicons name="alert-circle-outline" size={48} color="#f43f5e" />
          <Text className="mt-4 text-gray-600 font-body text-center">
            Failed to load journey details. Please try again later.
          </Text>
          <TouchableOpacity className="mt-4 bg-primary p-3 rounded-full px-6" onPress={() => router.back()}>
            <Text className="text-white font-body-medium">Go Back</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  const isCurrentPricingAvailable = (tier: any) => {
    const now = new Date();
    const availableUntil = new Date(tier.availableUntil);
    return availableUntil > now;
  };

  const handleShareJourney = async () => {
    try {
      const journeyLink = getJourneyLink(journeyId);

      await Share.share({
        message: `Check out this journey on Logoutloud: ${journey.title} from ${formatDate(
          journey.startDate
        )} to ${formatDate(journey.endDate)}. Join me - ${journeyLink}`,
        title: "Join me on this Logoutloud Journey!",
        url: journeyLink, // This will be the clickable link
      });
    } catch (error) {
      console.error("Error sharing journey:", error);
      showToast("error", "Failed to share journey");
    }
  };

  return (
    <View className="flex-1 bg-background">
      {/* Header */}
      <Animated.View
        style={{
          height: headerHeight,
          opacity: headerImageOpacity,
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          zIndex: 0,
        }}
      >
        <Image source={{ uri: journey.coverImage }} style={{ width: "100%", height: "100%" }} resizeMode="cover" />
        <LinearGradient
          colors={["rgba(0,0,0,0.5)", "transparent", "rgba(0,0,0,0.7)"]}
          style={{
            position: "absolute",
            left: 0,
            right: 0,
            top: 0,
            height: headerHeight,
          }}
        />

        {/* Travel Mode Badges */}
        <View className="absolute bottom-20 left-5 flex-row">
          {journey.travelMode.map((mode: string, index: number) => (
            <View
              key={index}
              className="bg-white/25 backdrop-blur-md rounded-full w-10 h-10 items-center justify-center mr-2"
            >
              <Ionicons
                name={
                  mode.toLowerCase() === "flight"
                    ? "airplane"
                    : mode.toLowerCase() === "car"
                    ? "car"
                    : mode.toLowerCase() === "boat"
                    ? "boat"
                    : mode.toLowerCase() === "train"
                    ? "train"
                    : mode.toLowerCase() === "bus"
                    ? "bus"
                    : mode.toLowerCase() === "ship"
                    ? "boat"
                    : mode.toLowerCase() === "jeep"
                    ? "car"
                    : "walk"
                }
                size={18}
                color="#FFFFFF"
              />
            </View>
          ))}
        </View>

        {/* Date Badge */}
        <View className="absolute top-20 right-5 bg-white/20 backdrop-blur-md rounded-xl p-3">
          <Text className="text-white font-heading">{formatDate(journey.startDate)}</Text>
          <Text className="text-white text-xs font-body">{journey.duration} days</Text>
        </View>
      </Animated.View>

      {/* Back Button */}
      <View style={{ top: insets.top + 10 }} className="absolute left-4 z-20">
        <TouchableOpacity
          className="w-10 h-10 bg-black/30 rounded-full items-center justify-center"
          onPress={() => router.back()}
        >
          <Ionicons name="chevron-back" size={24} color="white" />
        </TouchableOpacity>
      </View>

      {/* Content */}
      <Animated.ScrollView
        showsVerticalScrollIndicator={false}
        onScroll={Animated.event([{ nativeEvent: { contentOffset: { y: scrollY } } }], { useNativeDriver: true })}
        scrollEventThrottle={16}
        style={{ zIndex: 5 }}
      >
        {/* Spacer to push content below header */}
        <View style={{ height: headerHeight - 32 }} />

        {/* Content Container */}
        <View className="bg-white rounded-t-[32px] px-5 pt-6 pb-40">
          {/* Title and Price Row */}
          <View>
            <Text className="font-heading text-xl text-textColor mb-1">{journey.title}</Text>
            <View className="flex-row items-center mb-4">
              <Ionicons name="location-sharp" size={18} color="#D72638" />
              <Text className="font-body text-body-100 text-gray-600 ml-1">
                {journey.destinationCity}, {journey.destinationCountry}
              </Text>
            </View>
            <View className="flex-row items-center justify-between mb-6">
              <View className="flex-row">
                <DifficultyBadge level={journey.difficultyLevel} />

                <View className="flex-row items-center ml-3 bg-slate-100 rounded-lg px-3 py-2">
                  <Ionicons name="people" size={14} color="#4A6FFF" />
                  <Text className="font-body text-sm text-textColor ml-1">
                    {journey.minParticipants}-{journey.maxParticipants} people
                  </Text>
                </View>
              </View>
              <TouchableOpacity
                className="bg-slate-100 rounded-lg px-3 py-2 ml-2 flex-row items-center"
                onPress={handleShareJourney}
              >
                <Ionicons name="share-social-outline" size={14} color="#4A6FFF" />
                <Text className="font-body text-sm text-textColor ml-1">Share</Text>
              </TouchableOpacity>
            </View>

            <View className="h-1 w-full bg-slate-100 mb-6" />
          </View>

          {/* Tabs */}
          <View className="flex-row bg-slate-100 mb-6 rounded-xl overflow-hidden justify-around">
            <TabButton
              title="Overview"
              isActive={activeTab === "overview"}
              onPress={() => setActiveTab("overview")}
              smallScreen={true}
            />
            <TabButton
              title="Itinerary"
              isActive={activeTab === "itinerary"}
              onPress={() => setActiveTab("itinerary")}
              smallScreen={true}
            />
            <TabButton
              title="Pricing"
              isActive={activeTab === "pricing"}
              onPress={() => setActiveTab("pricing")}
              smallScreen={true}
            />
          </View>

          {/* Tab Content */}
          {activeTab === "overview" && (
            <>
              {/* Description */}
              <View className="mb-6">
                <SectionTitle title="About this Journey" />
                <Text className="font-body text-body-100 text-textColor leading-6">{journey.description}</Text>
              </View>

              {/* Organizer */}
              <View className="mb-6">
                <SectionTitle title="Organizer" />
                <View className="flex-row items-center bg-slate-50 p-4 rounded-xl">
                  <Image source={{ uri: journey.organizer.profilePic }} className="w-14 h-14 rounded-full" />
                  <View className="ml-3">
                    <Text className="font-heading text-body-100 text-textColor">
                      {journey.organizer.firstName} {journey.organizer.lastName}
                    </Text>
                    {journey.organizerInstagramProfile && <InstagramProfile url={journey.organizerInstagramProfile} />}
                    {/* <Text className="text-gray-500 text-sm">Journey Organizer</Text> */}
                  </View>
                </View>
              </View>

              {/* Highlights */}
              <View className="mb-6">
                <SectionTitle title="Journey Highlights" />

                <View className="flex-row flex-wrap -mx-1 mb-4">
                  {journey.locations.map((location: any, index: number) => (
                    <View key={index} className="w-1/2 px-1 mb-2">
                      <View className="bg-slate-50 p-3 rounded-xl flex-row items-center">
                        <View className="w-8 h-8 bg-primary-50 rounded-full items-center justify-center">
                          <Ionicons name="location" size={16} color="#4A6FFF" />
                        </View>
                        <Text className="ml-2 font-body text-sm text-textColor flex-1" numberOfLines={2}>
                          {location.name}
                        </Text>
                      </View>
                    </View>
                  ))}
                </View>
              </View>

              {/* Included/Excluded */}
              <View className="flex-row mb-6">
                <View className="flex-1 pr-2">
                  <SectionTitle title="What's Included" />
                  <View className="bg-slate-50 p-4 rounded-xl">
                    {journey.included.slice(0, 4).map((item: string, index: number) => (
                      <BenefitItem key={index} text={item} />
                    ))}
                    {journey.included.length > 5 && (
                      <Text className="text-primary-600 text-xs mt-2">+{journey.included.length - 5} more</Text>
                    )}
                  </View>
                </View>
                <View className="flex-1 pl-2">
                  <SectionTitle title="What's Excluded" />
                  <View className="bg-slate-50 p-4 rounded-xl">
                    {journey.excluded.slice(0, 4).map((item: string, index: number) => (
                      <View key={index} className="flex-row items-center mb-2">
                        <View className="w-6 h-6 bg-red-100 rounded-full items-center justify-center">
                          <Ionicons name="close" size={16} color="#f43f5e" />
                        </View>
                        <Text className="ml-2 font-body text-textColor">{item}</Text>
                      </View>
                    ))}
                    {journey.excluded.length > 5 && (
                      <Text className="text-primary-600 text-xs mt-2">+{journey.excluded.length - 5} more</Text>
                    )}
                  </View>
                </View>
              </View>

              {/* Packing List */}
              <View className="mb-6">
                <SectionTitle title="What to Pack" />
                <View className="bg-slate-50 p-4 rounded-xl">
                  <View className="flex-row flex-wrap">
                    {journey.packingList.map((item: string, index: number) => (
                      <View key={index} className="w-1/2 mb-2">
                        <PackingItem text={item} />
                      </View>
                    ))}
                  </View>
                </View>
              </View>

              {/* Weather Info */}
              {journey.weatherInfo && (
                <View className="mb-6">
                  <SectionTitle title="Weather Information" />
                  <View className="bg-slate-50 p-4 rounded-xl flex-row items-center">
                    <View className="w-10 h-10 bg-blue-100 rounded-full items-center justify-center mr-3">
                      <Ionicons name="partly-sunny" size={20} color="#4A6FFF" />
                    </View>
                    <Text className="font-body text-textColor flex-1">{journey.weatherInfo}</Text>
                  </View>
                </View>
              )}

              {/* Tags */}
              {journey.tags.length > 0 && (
                <View className="mb-6">
                  <SectionTitle title="Tags" />
                  <View className="flex-row flex-wrap">
                    {journey.tags.map((tag: string, index: number) => (
                      <Tag key={index} text={tag} />
                    ))}
                  </View>
                </View>
              )}

              {/* Photos */}
              {journey.images.length > 0 && (
                <View className="mb-6">
                  <SectionTitle title="Photos" />
                  <ImageGallery images={[journey.coverImage, ...journey.images]} dynamicSize={true} />
                </View>
              )}
            </>
          )}

          {activeTab === "itinerary" && (
            <View>
              <View className="mb-6">
                <SectionTitle title="Journey Itinerary" />
                <Text className="font-body text-gray-600 mb-4">
                  Your {journey.duration}-day journey from {formatDate(journey.startDate)} to{" "}
                  {formatDate(journey.endDate)}
                </Text>

                {journey.itinerary.map((day: any, index: number) => (
                  <ItineraryDay key={day._id} day={day} isLast={index === journey.itinerary.length - 1} />
                ))}
              </View>
            </View>
          )}

          {activeTab === "pricing" && (
            <View>
              <View className="mb-6">
                <SectionTitle title="Pricing Options" />
                <Text className="font-body text-gray-600 mb-4">
                  Choose from our different pricing tiers for this journey
                </Text>

                {journey.pricingTiers.map((tier: any) => (
                  <PriceCard
                    key={tier._id}
                    name={tier.name}
                    price={tier.price}
                    benefits={tier.benefits}
                    available={isCurrentPricingAvailable(tier)}
                  />
                ))}

                {/* <View className="bg-primary-50 p-4 rounded-xl mt-2">
                  <View className="flex-row items-center mb-2">
                    <Ionicons name="information-circle" size={20} color="#4A6FFF" />
                    <Text className="font-heading text-sm text-textColor ml-2">Deposit Information</Text>
                  </View>
                  <Text className="font-body text-sm text-gray-600">
                    A deposit of ₹{journey.depositAmount} is required to secure your booking. Final payment is due by{" "}
                    {formatDate(journey.finalPaymentDueDate)}.
                  </Text>
                </View> */}

                {journey.groupDiscounts.length > 0 && (
                  <View className="bg-green-50 p-4 rounded-xl mt-4">
                    <View className="flex-row items-center mb-2">
                      <Ionicons name="people" size={20} color="#10b981" />
                      <Text className="font-heading text-sm text-textColor ml-2">Group Discounts Available</Text>
                    </View>

                    {journey.groupDiscounts.map((discount: any, index: number) => (
                      <Text key={index} className="font-body text-sm text-gray-600">
                        • {discount.discountPercentage}% off for groups of {discount.minPeople}+ people
                      </Text>
                    ))}
                  </View>
                )}
                {journey.promoCode && (
                  <View className="bg-white/70 p-4 rounded-xl mt-4">
                    <View className="flex-row items-center mb-2">
                      <Ionicons name="pricetag" size={20} color="#4A6FFF" />
                      <Text className="font-heading text-sm text-textColor ml-2">Promo Code Available</Text>
                    </View>
                    <Text className="font-body text-sm text-gray-600">
                      Use promo code <Text className="text-primary-600 font-heading">{journey.promoCode}</Text> for an
                      additional {journey.promoDiscount}% off!
                    </Text>
                  </View>
                )}
              </View>
            </View>
          )}
        </View>
      </Animated.ScrollView>

      {/* Join/Book Now CTA */}
      <View
        style={{
          position: "absolute",
          bottom: 0,
          left: 0,
          right: 0,
          paddingBottom: insets.bottom + 10,
          paddingTop: 15,
          paddingHorizontal: 20,
          backgroundColor: "white",
          borderTopWidth: 1,
          borderTopColor: "#f1f5f9",
          zIndex: 20,
        }}
      >
        <View className="flex-row items-center justify-between">
          <View>
            <Text className="text-gray-600 text-xs font-body">Starting from</Text>
            <Text className="font-heading text-2xl text-primary-600">₹{journey.basePrice.toLocaleString()}</Text>
          </View>
          <TouchableOpacity
            className={`bg-primary rounded-xl py-3 px-8 ${
              userBookingStatus?.isBooked
                ? "bg-green-600"
                : userBookingStatus?.status === JOURNEY_PARTICIPANT_STATUS.APPROVAL_PENDING
                ? "bg-green-100"
                : "bg-primary"
            } ${paymentLoading ? "opacity-70" : ""}`}
            onPress={userBookingStatus?.isBooked ? handleViewTicket : handleBookNow}
            disabled={paymentLoading || userBookingStatus?.status === JOURNEY_PARTICIPANT_STATUS.APPROVAL_PENDING}
          >
            {paymentLoading ? (
              <ActivityIndicator color="white" size="small" />
            ) : (
              <Text
                className={` ${userBookingStatus?.isBooked ? "text-white" : "text-textColor"} font-heading text-md`}
              >
                {userBookingStatus?.isBooked
                  ? "View Ticket"
                  : userBookingStatus?.status === JOURNEY_PARTICIPANT_STATUS.APPROVAL_PENDING
                  ? "Approval Pending"
                  : "Book Now"}
              </Text>
            )}
          </TouchableOpacity>
        </View>
      </View>

      {/* Profile Validator Modal */}
      <ProfileValidator
        isVisible={showProfileValidator}
        onClose={handleProfileValidatorClose}
        onComplete={handleProfileValidationComplete}
        missingFields={missingFields}
      />

      {/* Terms Agreement Modal */}
      <JourneyTermsAgreementModal
        isVisible={showTermsAgreement}
        onClose={() => setShowTermsAgreement(false)}
        onAccept={() => {
          setShowTermsAgreement(false);
          setShowUpiPaymentModal(true);
        }}
        journeyId={journeyId}
        isPaid={true}
      />

      {/* Booking Modal */}
      {/* {journey && (
        <JourneyBookingModal
          isVisible={showBookingModal}
          onClose={handleBookingModalClose}
          onProceed={handleProceedWithBooking}
          journey={journey}
        />
      )} */}

      {/* Payment Status Modal */}
      {/* {showPaymentStatus && (
        <JourneyPaymentStatus
          journeyId={journeyId}
          onClose={() => {
            refetch();
            setShowPaymentStatus(false);
          }}
          onSuccess={handlePaymentSuccess}
        />
      )} */}

      {/* UPI Payment Modal */}
      <JourneyUpiPaymentModal
        isVisible={showUpiPaymentModal}
        onClose={() => setShowUpiPaymentModal(false)}
        journey={journey}
        onPaymentSuccess={() => {
          setShowUpiPaymentModal(false);
          setShowPaymentStatus(true);
          refetch();
        }}
      />
    </View>
  );
};

export default JourneyDetails;
