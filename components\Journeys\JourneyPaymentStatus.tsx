import React, { useState, useEffect, useRef } from "react";
import { View, Text, Modal, ActivityIndicator, TouchableOpacity } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useJourneyPayment } from "../../hooks/useJourneyPayment";

interface JourneyPaymentStatusProps {
  journeyId: string;
  onClose: () => void;
  onSuccess: () => void;
}

const JourneyPaymentStatus: React.FC<JourneyPaymentStatusProps> = ({ journeyId, onClose, onSuccess }) => {
  const [status, setStatus] = useState<"processing" | "success" | "failed">("processing");
  const [message, setMessage] = useState<string>("Processing your payment...");
  const [timeoutReached, setTimeoutReached] = useState(false);
  const { checkPaymentStatus } = useJourneyPayment();
  const isMounted = useRef(true);
  let attempts = 0;
  const maxAttempts = 10;
  const checkInterval = 2000; // 2 seconds

  useEffect(() => {
    isMounted.current = true;

    const checkPayment = () => {
      const intervalId = setInterval(async () => {
        try {
          if (attempts >= maxAttempts) {
            if (isMounted.current) {
              setTimeoutReached(true);
              setMessage(
                "Payment verification is taking longer than expected. You can close this window and check your tickets later."
              );
            }
            clearInterval(intervalId);
            return;
          }

          attempts++;
          const response = await checkPaymentStatus(journeyId);
          console.log("Payment status response:", response);

          if (isMounted.current) {
            if (response && response.participantStatus === "confirmed" && response.payment.status === "completed") {
              setStatus("success");
              setMessage("Payment successful!");
              clearInterval(intervalId);
              setTimeout(() => {
                if (isMounted.current) {
                  onSuccess();
                }
              }, 2000);
            }
          }
        } catch (error) {
          console.error("Error checking payment status:", error);
          if (isMounted.current && attempts >= maxAttempts) {
            setStatus("failed");
            setMessage(
              "We couldn't verify your payment. If your payment was successful, your ticket will appear in your profile."
            );
            clearInterval(intervalId);
          }
        }
      }, checkInterval);

      return intervalId;
    };

    const intervalId = checkPayment();

    return () => {
      isMounted.current = false;
      clearInterval(intervalId);
    };
  }, [journeyId]);

  return (
    <Modal visible={true} transparent animationType="fade">
      <View className="flex-1 bg-black/50 justify-center items-center p-5">
        <View className="bg-white rounded-3xl p-6 w-full max-w-md">
          <View className="items-center mb-6">
            {status === "processing" && (
              <View className="bg-blue-100 rounded-full p-4 mb-4">
                <ActivityIndicator size="large" color="#4A6FFF" />
              </View>
            )}

            {status === "success" && (
              <View className="bg-green-100 rounded-full p-4 mb-4">
                <Ionicons name="checkmark-circle" size={48} color="#10B981" />
              </View>
            )}

            {status === "failed" && (
              <View className="bg-red-100 rounded-full p-4 mb-4">
                <Ionicons name="alert-circle" size={48} color="#EF4444" />
              </View>
            )}

            <Text className="font-heading text-xl text-center text-textColor mb-2">
              {status === "processing"
                ? "Processing Payment"
                : status === "success"
                ? "Payment Successful"
                : "Payment Verification Issue"}
            </Text>

            <Text className="font-body text-center text-gray-600">{message}</Text>
          </View>

          {(status !== "processing" || timeoutReached) && (
            <TouchableOpacity className="bg-primary rounded-xl py-3 w-full items-center" onPress={onClose}>
              <Text className="font-heading text-textColor">Close</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </Modal>
  );
};

export default JourneyPaymentStatus;
