import React, { useState } from "react";
import { View, Text, Modal, ScrollView, TouchableOpacity, ActivityIndicator, Platform } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import PrimaryButton from "../Buttons/PrimaryButton";
import SecondaryButton from "../Buttons/SecondaryButton";
import { uploadTextToFirebase } from "../../services/firebaseStorageService";
import { getAuth } from "firebase/auth";
import { router } from "expo-router";

interface JourneyTermsAgreementModalProps {
  isVisible: boolean;
  onClose: () => void;
  onAccept: () => void;
  journeyId: string;
  isPaid?: boolean;
}

const JourneyTermsAgreementModal: React.FC<JourneyTermsAgreementModalProps> = ({
  isVisible,
  onClose,
  onAccept,
  journeyId,
  isPaid = false,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const auth = getAuth();

  const handleAccept = async () => {
    try {
      setIsLoading(true);

      // Create agreement record
      const agreementData = {
        userId: auth.currentUser?.uid,
        journeyId: journeyId,
        agreementType: "journey-terms",
        timestamp: new Date().toISOString(),
        ipAddress: "captured-on-client",
        userAgent: Platform.OS + " " + Platform.Version,
        termsVersion: "1.0",
      };

      try {
        // Use the new function to directly upload the JSON object
        const agreementPath = `legal-agreements/${auth.currentUser?.uid}`;
        const filename = `${agreementPath}/${journeyId}.json`;
        await uploadTextToFirebase(agreementData, agreementPath, filename, "json");
      } catch (uploadError) {
        // Log the error but continue with the flow
        console.error("Error uploading agreement to Firebase:", uploadError);
      }

      // Proceed with acceptance regardless of storage success
      onAccept();
    } catch (error) {
      console.error("Error in agreement process:", error);
      // Still proceed with payment even if recording fails
      onAccept();
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewFullTerms = () => {
    // Close the modal first
    onClose();
    // Navigate to terms of service page
    router.push("/settings/terms-of-service");
  };

  return (
    <Modal visible={isVisible} transparent animationType="slide" onRequestClose={onClose}>
      <View className="flex-1 justify-center items-center bg-black/50 p-4">
        <View className="bg-white w-full rounded-xl p-5 max-h-[80%]">
          <View className="flex-row justify-between items-center mb-4">
            <Text className="text-xl font-heading text-primary-600">Before You Book</Text>
            <TouchableOpacity onPress={onClose}>
              <Ionicons name="close" size={24} color="#4A6FFF" />
            </TouchableOpacity>
          </View>

          <Text className="text-base mb-4 text-gray-700">
            We want you to have a great journey experience! Please review our journey guidelines before booking.
          </Text>

          <ScrollView className="mb-4">
            <View className="bg-slate-50 p-4 rounded-xl mb-4">
              <Text className="text-base font-body-medium mb-2 text-primary-600">Safety First</Text>
              <Text className="text-base mb-2">
                You're responsible for your safety and belongings during journeys. We connect people but don't supervise
                all aspects of the journey.
              </Text>
            </View>

            <View className="bg-slate-50 p-4 rounded-xl mb-4">
              <Text className="text-base font-body-medium mb-2 text-primary-600">Respect Others</Text>
              <Text className="text-base mb-2">
                Be respectful to everyone you meet during the journey. Report any inappropriate behavior through our
                app.
              </Text>
            </View>

            <View className="bg-slate-50 p-4 rounded-xl mb-4">
              <Text className="text-base font-body-medium mb-2 text-primary-600">Travel Responsibly</Text>
              <Text className="text-base mb-2">
                Follow all local laws and customs at your destination. Respect the environment and local communities.
              </Text>
            </View>

            {/* Add payment disclaimer section */}
            {isPaid && (
              <View className="bg-slate-50 p-4 rounded-xl mb-4">
                <Text className="text-base font-body-medium mb-2 text-primary-600">Payment Information</Text>
                <Text className="text-base mb-2">
                  Your payment is exclusively for an in-person, travel experience. No digital content, virtual goods, or
                  online services are provided. This transaction is for a real-world journey and is processed securely
                  through our payment partner.
                </Text>
              </View>
            )}

            <Text className="text-sm text-gray-500 mb-4">
              By continuing, you agree to our full Terms of Service, including assumption of risk and release of
              liability provisions.
            </Text>

            <TouchableOpacity className="mb-6" onPress={handleViewFullTerms}>
              <Text className="text-primary-600 text-sm font-body-medium">View Full Terms of Service</Text>
            </TouchableOpacity>
          </ScrollView>

          {isLoading ? (
            <ActivityIndicator size="large" color="#4A6FFF" />
          ) : (
            <View>
              <PrimaryButton buttonText="I Understand & Agree" onPressHandler={handleAccept} />
              <View className="h-2" />
              <SecondaryButton buttonText="Not Now" onPressHandler={onClose} />
            </View>
          )}
        </View>
      </View>
    </Modal>
  );
};

export default JourneyTermsAgreementModal;
