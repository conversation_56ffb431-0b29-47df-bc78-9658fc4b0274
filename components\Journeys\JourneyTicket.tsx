import React, { useRef } from "react";
import { View, Text, Image, TouchableOpacity, Share, Platform, Alert, ScrollView } from "react-native";
import { Ionicons, MaterialCommunityIcons, MaterialIcons } from "@expo/vector-icons";
import { formatDate } from "../../lib/utils/formatters";
import * as FileSystem from "expo-file-system";
import * as MediaLibrary from "expo-media-library";
import * as Sharing from "expo-sharing";
import ViewShot from "react-native-view-shot";
import { getJourneyLink } from "../../constants/common";

interface JourneyTicketProps {
  journey: {
    _id: string;
    title: string;
    description: string;
    destination: string;
    startDate: string;
    endDate: string;
    coverImage: string;
  };
  participant: {
    _id: string;
    status: string;
    bookingDate: string;
    pricingTier: string;
    bookingPrice: number;
  };
  ticketInfo: {
    bookingReference: string;
    departureDate: string;
    returnDate: string;
    departureLocation: string;
    emergencyContact: string;
    importantNotes: string;
  };
  payment: {
    transactionId: string;
    amount: number;
    paymentDate: string;
    paymentMethod: string;
    status: string;
  } | null;
  qrCode: string;
  onClose: () => void;
  fullPage?: boolean;
}

const JourneyTicket: React.FC<JourneyTicketProps> = ({
  journey,
  participant,
  ticketInfo,
  payment,
  qrCode,
  onClose,
  fullPage = false,
}) => {
  const ticketRef = useRef<ViewShot>(null);

  const captureTicket = async (): Promise<string | null> => {
    try {
      if (!ticketRef.current) {
        Alert.alert("Error", "Could not capture ticket image");
        return null;
      }

      const uri = await ticketRef.current.capture?.();
      return uri || null;
    } catch (error) {
      console.error("Error capturing ticket:", error);
      Alert.alert("Error", "Failed to capture ticket image");
      return null;
    }
  };

  const downloadTicket = async () => {
    try {
      // Request permissions
      const { status } = await MediaLibrary.requestPermissionsAsync();
      if (status !== "granted") {
        Alert.alert("Sorry, we need media library permissions to save the ticket!");
        return;
      }

      // Capture the ticket view
      const uri = await captureTicket();
      if (!uri) return;

      const filename = `logoutloud-journey-ticket-${participant._id}.png`;
      const fileUri = FileSystem.documentDirectory + filename;

      // Copy the captured image to a permanent location
      await FileSystem.copyAsync({
        from: uri,
        to: fileUri,
      });

      if (Platform.OS === "android") {
        // Save to media library on Android
        const asset = await MediaLibrary.createAssetAsync(fileUri);
        await MediaLibrary.createAlbumAsync("Logoutloud", asset, false);
        Alert.alert("Ticket saved to gallery!");
      } else {
        // Share on iOS
        await Sharing.shareAsync(fileUri);
      }
    } catch (error) {
      console.error("Error saving ticket:", error);
      Alert.alert("Failed to save ticket. Please try again.");
    }
  };

  const shareTicket = async () => {
    try {
      // Capture the ticket view
      const uri = await captureTicket();
      if (!uri) return;

      // Share the image and text
      await Share.share({
        message: `I've booked a journey on Logoutloud: ${journey.title} from ${formatDate(
          journey.startDate
        )} to ${formatDate(journey.endDate)}. \nJoin me - ${getJourneyLink(journey._id)}`,
        title: "Logoutloud Journey",
        url: uri, // This will attach the image on supported platforms
      });
    } catch (error) {
      console.error("Error sharing ticket:", error);
      Alert.alert("Failed to share ticket. Please try again.");
    }
  };

  return (
    <View className={`${fullPage ? "flex-1 p-5" : "flex-1 justify-center items-center bg-black/80 p-5"}`}>
      <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{ paddingBottom: fullPage ? 20 : 0 }}>
        <ViewShot ref={ticketRef} options={{ format: "png", quality: 0.9 }} style={{ width: "100%" }}>
          <View className="w-full bg-white rounded-2xl overflow-hidden shadow-lg">
            {/* Ticket Header */}
            {!fullPage && (
              <TouchableOpacity onPress={onClose} className="absolute top-2 right-2 p-1 bg-gray-100 rounded-full z-10">
                <Ionicons name="close" size={16} color="#2b2b2b" />
              </TouchableOpacity>
            )}

            {/* Ticket ID */}
            <View className="flex-row mt-3 justify-center">
              <Text className="font-body text-xs text-textColor/60 mr-1">Booking Reference:</Text>
              <Text className="font-body-medium text-xs text-textColor">{ticketInfo.bookingReference}</Text>
            </View>

            {/* Joined Date */}
            <Text className="font-body text-xs text-textColor/60 mt-1 text-center">
              Booked on {formatDate(participant.bookingDate)}
            </Text>

            {/* QR Code */}
            <View className="items-center my-3">
              <Image source={{ uri: qrCode }} className="w-[150px] h-[150px] mb-1" />
              <Text className="font-body text-xs text-textColor/60 text-center">
                Scan this QR code for verification
              </Text>
            </View>

            {/* dashed line with rounded black edges like a ticket */}
            <View className="flex-row justify-center items-center">
              <View className="w-[8px] h-[8px] bg-black rounded-full" />
              <View className="w-full h-[1px] bg-slate-200" />
              <View className="w-[8px] h-[8px] bg-black rounded-full" />
            </View>

            {/* Ticket Content */}
            <View className="p-4">
              <View className="mb-2">
                <Text className="text-base font-heading text-midnightBlue">{journey.title}</Text>
                <Text className="font-body text-sm text-textColor">{journey.destination}</Text>
              </View>

              <View className="flex-row justify-between mb-2">
                <View className="flex-1">
                  <Text className="font-body text-xs text-textColor">Start Date:</Text>
                  <Text className="font-body-medium text-sm text-textColor">{formatDate(journey.startDate)}</Text>
                </View>
                <View className="flex-1 items-end">
                  <Text className="font-body text-xs text-textColor">End Date:</Text>
                  <Text className="font-body-medium text-sm text-textColor">{formatDate(journey.endDate)}</Text>
                </View>
              </View>

              <View className="flex-row justify-between mb-2">
                <View className="flex-1">
                  <Text className="font-body text-xs text-textColor">Departure:</Text>
                  <Text className="font-body-medium text-sm text-textColor">{ticketInfo.departureLocation}</Text>
                </View>
                <View className="flex-1 items-end">
                  <Text className="font-body text-xs text-textColor">Status:</Text>
                  <Text
                    className={`font-body-medium text-sm ${
                      participant.status === "confirmed" ? "text-[#0a8043]" : "text-[#856404]"
                    }`}
                  >
                    {participant.status.charAt(0).toUpperCase() + participant.status.slice(1)}
                  </Text>
                </View>
              </View>

              <View className="flex-row justify-between mb-2">
                <View className="flex-1">
                  <Text className="font-body text-xs text-textColor">Pricing Tier:</Text>
                  <Text className="font-body-medium text-sm text-textColor">{participant.pricingTier}</Text>
                </View>
                <View className="flex-1 items-end">
                  <Text className="font-body text-xs text-textColor">Price:</Text>
                  <Text className="font-body-medium text-sm text-primary-600">
                    ₹{participant.bookingPrice.toLocaleString()}
                  </Text>
                </View>
              </View>

              {/* Payment Information */}
              {payment && (
                <View className="mt-2 p-2 bg-gray-50 rounded-lg">
                  <Text className="font-heading text-sm text-textColor mb-1">Payment Information</Text>
                  <View className="flex-row justify-between">
                    <Text className="font-body text-xs text-textColor/60">Transaction ID:</Text>
                    <Text className="font-body-medium text-xs text-textColor">{payment.transactionId}</Text>
                  </View>
                  <View className="flex-row justify-between">
                    <Text className="font-body text-xs text-textColor/60">Payment Date:</Text>
                    <Text className="font-body-medium text-xs text-textColor">{formatDate(payment.paymentDate)}</Text>
                  </View>
                  <View className="flex-row justify-between">
                    <Text className="font-body text-xs text-textColor/60">Payment Method:</Text>
                    <Text className="font-body-medium text-xs text-textColor">{payment.paymentMethod}</Text>
                  </View>
                </View>
              )}

              {/* Emergency Contact */}
              <View className="mt-2 p-2 bg-primary-50 rounded-lg">
                <Text className="font-heading text-sm text-textColor mb-1">Important Information</Text>
                <View className="flex-row items-center mb-1">
                  <Ionicons name="call" size={14} color="#4A6FFF" />
                  <Text className="font-body-medium text-xs text-textColor ml-1">
                    Emergency: {ticketInfo.emergencyContact}
                  </Text>
                </View>
                <Text className="font-body text-xs text-textColor/80">{ticketInfo.importantNotes}</Text>
              </View>
            </View>

            {/* Ticket Actions */}
            <View className="flex-row gap-2 justify-center p-3 border-t border-slate-200">
              <TouchableOpacity
                className="flex-row items-center bg-midnightBlue py-2 px-4 rounded-lg"
                onPress={downloadTicket}
              >
                <MaterialIcons name="downloading" size={16} color="#fff" />
                <Text className="text-white font-body-medium text-xs ml-1">Save</Text>
              </TouchableOpacity>

              <TouchableOpacity
                className="flex-row items-center bg-midnightBlue py-2 px-4 rounded-lg"
                onPress={shareTicket}
              >
                <MaterialCommunityIcons name="share" size={16} color="#fff" />
                <Text className="text-white font-body-medium text-xs ml-1">Share</Text>
              </TouchableOpacity>
            </View>

            {/* Ticket Footer */}
            <View className="items-center py-2 bg-primary">
              <Text className="font-body text-[10px] text-textColor/60">Logoutloud</Text>
            </View>
          </View>
        </ViewShot>
      </ScrollView>
    </View>
  );
};

export default JourneyTicket;
