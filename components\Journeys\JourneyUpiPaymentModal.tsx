import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  Image,
  TextInput,
  ActivityIndicator,
  Alert,
  ScrollView,
  Dimensions,
  Linking,
} from "react-native";
import * as ImagePicker from "expo-image-picker";
import { uploadFileToFirebase } from "../../services/firebaseStorageService";
import { useJourneyPayment } from "../../hooks/useJourneyPayment";
import PrimaryButton from "../Buttons/PrimaryButton";
import SecondaryButton from "../Buttons/SecondaryButton";
import { showToast } from "../../lib/utils/showToast";
import Ionicons from "@expo/vector-icons/Ionicons";

interface JourneyUpiPaymentModalProps {
  isVisible: boolean;
  onClose: () => void;
  journey: any;
  participantId?: string;
  onPaymentSuccess: () => void;
}

const JourneyUpiPaymentModal: React.FC<JourneyUpiPaymentModalProps> = ({
  isVisible,
  onClose,
  journey,
  participantId,
  onPaymentSuccess,
}) => {
  const [screenshotUri, setScreenshotUri] = useState<string | null>(null);
  const [screenshotUrl, setScreenshotUrl] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedTier, setSelectedTier] = useState<string>("");
  const [promoCode, setPromoCode] = useState<string>("");
  const [finalPrice, setFinalPrice] = useState<number>(0);
  const [originalPrice, setOriginalPrice] = useState<number>(0);
  const {
    validatePromoCode,
    promoCodeValidation,
    loading,
    clearPromoValidation,
    submitUpiPayment,
    upiPaymentLoading,
    upiPaymentError,
  } = useJourneyPayment();
  const [step, setStep] = useState(1);

  useEffect(() => {
    if (isVisible) {
      setStep(1);
      setSelectedTier("");
      setPromoCode("");
      setFinalPrice(0);
      setOriginalPrice(0);
      clearPromoValidation();
      setScreenshotUri(null);
      setScreenshotUrl(null);
      setError(null);
    }
  }, [isVisible]);

  useEffect(() => {
    if (selectedTier) {
      const tier = journey.pricingTiers.find((t: any) => t._id === selectedTier);
      if (tier) {
        setOriginalPrice(tier.price);
        if (promoCodeValidation?.valid) {
          const discountAmount = tier.price * (promoCodeValidation.discountPercentage / 100);
          setFinalPrice(tier.price - discountAmount);
        } else {
          setFinalPrice(tier.price);
        }
      }
    }
  }, [selectedTier, promoCodeValidation]);

  const handlePickImage = async () => {
    setError(null);
    setUploading(true);
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        quality: 0.8,
      });
      if (!result.canceled && result.assets && result.assets.length > 0) {
        const uri = result.assets[0].uri;
        const url = await uploadFileToFirebase(uri, `journey-upi-payments/${journey._id}`);
        if (!url) throw new Error("Failed to upload screenshot");
        setScreenshotUri(uri);
        setScreenshotUrl(url);
      }
    } catch (err: any) {
      setError(err.message || "Failed to upload screenshot");
      setScreenshotUri(null);
      setScreenshotUrl(null);
    } finally {
      setUploading(false);
    }
  };

  const handleRemoveScreenshot = () => {
    setScreenshotUri(null);
    setScreenshotUrl(null);
  };

  const handleValidatePromoCode = async () => {
    if (!promoCode.trim()) {
      Alert.alert("Error", "Please enter a promo code");
      return;
    }
    if (!selectedTier) {
      Alert.alert("Error", "Please select a pricing tier first");
      return;
    }
    try {
      await validatePromoCode(journey._id, promoCode);
    } catch (error) {
      setError("Failed to validate promo code");
    }
  };

  const handleSubmit = async () => {
    setError(null);
    if (!selectedTier) {
      setError("Please select a pricing tier.");
      return;
    }
    if (!screenshotUrl) {
      setError("Please upload a payment screenshot.");
      return;
    }
    try {
      await submitUpiPayment(
        journey._id,
        screenshotUrl,
        selectedTier,
        promoCodeValidation?.valid ? promoCode : undefined
      );
      showToast("success", "Payment submitted! Awaiting organizer approval.");
      onPaymentSuccess();
    } catch (err: any) {
      setError(upiPaymentError || err.message || "Failed to submit payment");
    }
  };

  const isCurrentPricingAvailable = (tier: any) => {
    const now = new Date();
    const availableUntil = new Date(tier.availableUntil);
    return availableUntil > now;
  };

  return (
    <Modal visible={isVisible} transparent animationType="slide" onRequestClose={onClose}>
      <View className="flex-1 justify-end items-center bg-black/50">
        <View
          className="bg-white w-full rounded-t-3xl p-5"
          style={{
            maxHeight: Dimensions.get("window").height * 0.9,
            minHeight: Dimensions.get("window").height * 0.5,
            shadowColor: "#000",
            shadowOffset: { width: 0, height: -2 },
            shadowOpacity: 0.15,
            shadowRadius: 8,
            elevation: 8,
          }}
        >
          <View className="flex-row justify-between items-center mb-4">
            <Text className="text-xl font-heading text-primary-600">UPI Payment</Text>
            <TouchableOpacity onPress={onClose}>
              <Text style={{ fontSize: 18, color: "#4A6FFF" }}>✕</Text>
            </TouchableOpacity>
          </View>
          <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{ paddingBottom: 24 }}>
            {step === 1 && (
              <>
                <Text className="mb-2 text-base font-body text-textColor">
                  Select your pricing tier, apply promo code, and see the final price to pay.
                </Text>
                {/* Tier Selection */}
                <Text className="font-heading text-lg text-textColor mb-2">Select Pricing Tier</Text>
                {journey.pricingTiers.map((tier: any) => (
                  <TouchableOpacity
                    key={tier._id}
                    className={`border rounded-xl p-4 mb-3 ${
                      selectedTier === tier._id
                        ? "border-primary bg-primary/10"
                        : isCurrentPricingAvailable(tier)
                        ? "border-gray-200"
                        : "border-gray-200 opacity-50"
                    }`}
                    onPress={() => isCurrentPricingAvailable(tier) && setSelectedTier(tier._id)}
                    disabled={!isCurrentPricingAvailable(tier)}
                  >
                    <View className="flex-row justify-between items-center mb-2">
                      <View>
                        <Text className="font-heading text-lg text-textColor">{tier.name}</Text>
                        <Text className="text-primary font-heading text-2xl">₹{tier.price.toLocaleString()}</Text>
                      </View>
                      {selectedTier === tier._id && (
                        <View className="bg-primary p-2 rounded-full">
                          <Ionicons name="checkmark" size={20} color="#2e2e2e" />
                        </View>
                      )}
                    </View>
                    <View className="mt-2">
                      {tier.benefits.map((benefit: string, index: number) => (
                        <View key={index} className="flex-row items-center mb-1">
                          <View className="w-5 h-5 bg-primary/20 rounded-full items-center justify-center mr-2">
                            <Ionicons name="checkmark" size={12} color="#2e2e2e" />
                          </View>
                          <Text className="font-body text-textColor">{benefit}</Text>
                        </View>
                      ))}
                    </View>
                    {!isCurrentPricingAvailable(tier) && (
                      <Text className="font-body text-red-500 mt-2">No longer available</Text>
                    )}
                  </TouchableOpacity>
                ))}
                {/* Promo Code Section */}
                <View className="mt-4 mb-6">
                  <Text className="font-heading text-lg text-textColor mb-2">Promo Code</Text>
                  <View className="flex-row">
                    <TextInput
                      className="border border-gray-300 rounded-l-lg p-3 flex-1 border-r-0"
                      value={promoCode}
                      onChangeText={setPromoCode}
                      placeholder="Enter promo code"
                      autoCapitalize="characters"
                    />
                    <TouchableOpacity
                      className="bg-primary rounded-r-lg px-4 justify-center border border-gray-300 border-l-0"
                      onPress={handleValidatePromoCode}
                      disabled={loading || !selectedTier}
                    >
                      {loading ? (
                        <ActivityIndicator size="small" color="#2e2e2e" />
                      ) : (
                        <Text className="font-body-medium text-textColor">Apply</Text>
                      )}
                    </TouchableOpacity>
                  </View>
                  {promoCodeValidation && (
                    <Text className={`mt-2 font-body ${promoCodeValidation.valid ? "text-green-600" : "text-red-500"}`}>
                      {promoCodeValidation.message}
                    </Text>
                  )}
                </View>
                {/* Price Summary */}
                {selectedTier && (
                  <View className="bg-gray-50 p-4 rounded-xl mb-6">
                    <Text className="font-heading text-lg text-textColor mb-2">Price Summary</Text>
                    <View className="flex-row justify-between mb-2">
                      <Text className="font-body text-textColor">Base Price</Text>
                      <Text className="font-body-medium text-textColor">₹{originalPrice.toLocaleString()}</Text>
                    </View>
                    {promoCodeValidation?.valid && (
                      <View className="flex-row justify-between mb-2">
                        <Text className="font-body text-textColor">
                          Discount ({promoCodeValidation.discountPercentage}%)
                        </Text>
                        <Text className="font-body-medium text-green-600">
                          -₹{(originalPrice * (promoCodeValidation.discountPercentage / 100)).toLocaleString()}
                        </Text>
                      </View>
                    )}
                    <View className="border-t border-gray-200 my-2" />
                    <View className="flex-row justify-between">
                      <Text className="font-heading text-textColor">Final Price</Text>
                      <Text className="font-heading text-xl text-green-600">₹{finalPrice.toLocaleString()}</Text>
                    </View>
                  </View>
                )}
                <PrimaryButton buttonText="Next" onPressHandler={() => setStep(2)} disabled={!selectedTier} />
              </>
            )}
            {step === 2 && (
              <>
                <TouchableOpacity onPress={() => setStep(1)} className="mb-2">
                  <Text className="text-primary-600 font-body-medium">← Back</Text>
                </TouchableOpacity>
                {finalPrice > 0 && (
                  <View>
                    {/* Step 2: Summary and Payment Instructions */}
                    <View className="bg-gray-50 p-4 rounded-xl mb-4">
                      <Text className="font-heading text-lg text-textColor mb-2">Booking Summary</Text>
                      <Text className="font-body text-textColor mb-1">
                        <Text className="font-body-medium">Tier:</Text>{" "}
                        {journey.pricingTiers.find((t: any) => t._id === selectedTier)?.name}
                      </Text>
                      <View className="mb-2">
                        {journey.pricingTiers
                          .find((t: any) => t._id === selectedTier)
                          ?.benefits.map((benefit: string, idx: number) => (
                            <Text key={idx} className="text-xs text-gray-600 ml-2">
                              • {benefit}
                            </Text>
                          ))}
                      </View>
                      <Text className="font-body text-textColor mb-1">
                        <Text className="font-body-medium">Base Price:</Text> ₹{originalPrice.toLocaleString()}
                      </Text>
                      {promoCodeValidation?.valid && (
                        <Text className="font-body text-green-600 mb-1">
                          <Text className="font-body-medium">Promo Applied:</Text> {promoCode.toUpperCase()} (-
                          {promoCodeValidation.discountPercentage}%)
                        </Text>
                      )}
                      <Text className="font-heading text-lg text-primary-600 mt-2">
                        Amount to Pay: ₹{finalPrice.toLocaleString()}
                      </Text>
                    </View>
                    <Text className="font-body text-textColor mb-2">
                      Please pay{" "}
                      <Text className="font-heading text-primary-600">exactly ₹{finalPrice.toLocaleString()}</Text> for
                      the{" "}
                      <Text className="font-heading">
                        {journey.pricingTiers.find((t: any) => t._id === selectedTier)?.name}
                      </Text>{" "}
                      tier using the UPI QR below. Upload your payment screenshot to complete your booking.
                    </Text>
                    {journey.upiQrCodeUrl ? (
                      <Image
                        source={{ uri: journey.upiQrCodeUrl }}
                        style={{ width: 180, height: 180, alignSelf: "center", marginBottom: 16, borderRadius: 12 }}
                        resizeMode="contain"
                      />
                    ) : (
                      <Text className="text-red-500 mb-4">No UPI QR code available for this journey.</Text>
                    )}
                    {/* UPI App Link Option */}
                    {finalPrice > 0 && journey.upiId && (
                      <TouchableOpacity
                        onPress={() => {
                          const upiId = journey.upiId;
                          const name = journey.organizer?.firstName || "Organizer";
                          const amount = finalPrice;
                          const note = encodeURIComponent(`Payment for ${journey.title}`);
                          const url = `upi://pay?pa=${upiId}&pn=${encodeURIComponent(
                            name
                          )}&am=${amount}&cu=INR&tn=${note}`;
                          Linking.openURL(url);
                        }}
                        activeOpacity={0.85}
                        style={{ alignSelf: "center", marginBottom: 8 }}
                      >
                        <Text className="text-blue-600 underline font-body-medium">
                          Or click here to pay via UPI app
                        </Text>
                      </TouchableOpacity>
                    )}
                    <Text className="text-xs text-gray-500 mb-4 text-center">
                      After paying, please upload the payment screenshot below.
                    </Text>
                    <Text className="font-heading text-lg text-textColor mb-2">Upload Payment Screenshot</Text>
                    {uploading && (
                      <View className="items-center mb-2">
                        <ActivityIndicator size="small" color="#4A6FFF" />
                        <Text className="text-xs text-gray-500 mt-1">Uploading transaction screenshot...</Text>
                      </View>
                    )}
                    {error && <Text className="text-red-500 mb-2 text-center">{error}</Text>}
                    {!uploading && screenshotUrl ? (
                      <View className="items-center mb-2">
                        <Image
                          source={{ uri: screenshotUri || screenshotUrl }}
                          style={{ width: 120, height: 120, borderRadius: 8, alignSelf: "center", marginBottom: 8 }}
                        />
                        <TouchableOpacity onPress={handleRemoveScreenshot} className="mt-1">
                          <Text className="text-primary-600 underline text-xs">Remove and pick another</Text>
                        </TouchableOpacity>
                      </View>
                    ) : (
                      !uploading && (
                        <TouchableOpacity
                          onPress={handlePickImage}
                          className="bg-primary/10 rounded-xl p-4 items-center mb-2"
                        >
                          <Text className="text-primary-600 font-body-medium">Pick Screenshot</Text>
                        </TouchableOpacity>
                      )
                    )}
                    <PrimaryButton
                      buttonText={upiPaymentLoading ? "Submitting..." : "Submit Payment"}
                      onPressHandler={handleSubmit}
                      disabled={upiPaymentLoading || uploading}
                    />
                    <View className="h-2" />
                    <SecondaryButton buttonText="Cancel" onPressHandler={onClose} />
                  </View>
                )}
              </>
            )}
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

export default JourneyUpiPaymentModal;
