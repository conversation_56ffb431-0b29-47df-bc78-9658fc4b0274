import React from "react";
import { View, Text, TouchableOpacity, ScrollView, Animated, Dimensions } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { JourneySearchFilters } from "../../../hooks/useJourneySearch";
import {
  JOURNEY_CATEGORIES,
  JOURNEY_DIFFICULTY_LEVELS,
  JOURNEY_TRAVEL_MODES,
  JOURNEY_SORT_OPTIONS,
} from "../../../constants/journeys";

interface SearchFiltersProps {
  bottomSheetAnim: Animated.Value;
  filterCategory: string | null;
  filterDifficulty: string | null;
  filterTravelMode: string | null;
  priceRange: {
    min: number | null;
    max: number | null;
  };
  filterSortBy: string;
  filterSortOrder: "asc" | "desc";
  onApplyFilters: () => void;
  onResetFilters: () => void;
  onCategoryChange: (category: string | null) => void;
  onDifficultyChange: (difficulty: string | null) => void;
  onTravelModeChange: (travelMode: string | null) => void;
  onPriceRangeChange: (range: { min: number | null; max: number | null }) => void;
  onSortByChange: (sortBy: string) => void;
  onSortOrderChange: (sortOrder: "asc" | "desc") => void;
}

const { height } = Dimensions.get("window");

const SearchFilters: React.FC<SearchFiltersProps> = ({
  bottomSheetAnim,
  filterCategory,
  filterDifficulty,
  filterTravelMode,
  priceRange,
  filterSortBy,
  filterSortOrder,
  onApplyFilters,
  onResetFilters,
  onCategoryChange,
  onDifficultyChange,
  onTravelModeChange,
  onPriceRangeChange,
  onSortByChange,
  onSortOrderChange,
}) => {
  const hasActiveFilters =
    filterCategory || filterDifficulty || filterTravelMode || priceRange.min !== null || priceRange.max !== null;

  return (
    <Animated.View
      className="bg-white rounded-t-3xl p-5"
      style={{
        transform: [{ translateY: bottomSheetAnim }],
        maxHeight: height * 0.8,
      }}
    >
      <View className="items-center mb-3">
        <View className="w-10 h-1 bg-gray-300 rounded-full" />
      </View>

      <View className="flex-row justify-between items-center mb-4">
        <Text className="font-heading text-headline-500">Journey Filters</Text>
        {hasActiveFilters && (
          <TouchableOpacity onPress={onResetFilters}>
            <Text className="text-link font-body text-body-200">Reset All</Text>
          </TouchableOpacity>
        )}
      </View>

      <ScrollView showsVerticalScrollIndicator={false} className="mb-4">
        {/* Categories */}
        <View className="mb-5">
          <Text className="font-heading text-body-200 mb-2">Category</Text>
          <View className="flex-row flex-wrap">
            {JOURNEY_CATEGORIES.map((category) => (
              <TouchableOpacity
                key={category.id}
                className={`px-3 py-1.5 rounded-xl mr-2 mb-2 ${
                  filterCategory === category.id ? "bg-button" : "bg-slate-50 border border-slate-200"
                }`}
                onPress={() => onCategoryChange(filterCategory === category.id ? null : category.id)}
              >
                <Text
                  className={`text-xs font-body-medium ${
                    filterCategory === category.id ? "text-white" : "text-textColor/70"
                  }`}
                >
                  {category.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Difficulty */}
        <View className="mb-5">
          <Text className="font-heading text-body-200 mb-2">Difficulty Level</Text>
          <View className="flex-row flex-wrap">
            {JOURNEY_DIFFICULTY_LEVELS.map((difficulty) => (
              <TouchableOpacity
                key={difficulty.id}
                className={`px-3 py-1.5 rounded-xl mr-2 mb-2 ${
                  filterDifficulty === difficulty.id ? "bg-button" : "bg-slate-50 border border-slate-200"
                }`}
                onPress={() => onDifficultyChange(filterDifficulty === difficulty.id ? null : difficulty.id)}
              >
                <Text
                  className={`text-xs font-body-medium ${
                    filterDifficulty === difficulty.id ? "text-white" : "text-textColor/70"
                  }`}
                >
                  {difficulty.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Travel Modes */}
        <View className="mb-5">
          <Text className="font-heading text-body-200 mb-2">Travel Mode</Text>
          <View className="flex-row flex-wrap">
            {JOURNEY_TRAVEL_MODES.slice(0, 8).map((mode) => (
              <TouchableOpacity
                key={mode.id}
                className={`px-3 py-1.5 rounded-xl mr-2 mb-2 ${
                  filterTravelMode === mode.id ? "bg-button" : "bg-slate-50 border border-slate-200"
                }`}
                onPress={() => onTravelModeChange(filterTravelMode === mode.id ? null : mode.id)}
              >
                <Text
                  className={`text-xs font-body-medium ${
                    filterTravelMode === mode.id ? "text-white" : "text-textColor/70"
                  }`}
                >
                  {mode.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Price Range - We're keeping this simple without sliders for now */}
        <View className="mb-5">
          <Text className="font-heading text-body-200 mb-2">Price Range</Text>
          <View className="flex-row">
            <TouchableOpacity
              className={`px-3 py-1.5 rounded-xl mr-2 ${
                priceRange.min === 0 && priceRange.max === 5000 ? "bg-button" : "bg-slate-50 border border-slate-200"
              }`}
              onPress={() => onPriceRangeChange({ min: 0, max: 5000 })}
            >
              <Text
                className={`text-xs font-body-medium ${
                  priceRange.min === 0 && priceRange.max === 5000 ? "text-white" : "text-textColor/70"
                }`}
              >
                Budget (₹0-5K)
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              className={`px-3 py-1.5 rounded-xl mr-2 ${
                priceRange.min === 5000 && priceRange.max === 20000
                  ? "bg-button"
                  : "bg-slate-50 border border-slate-200"
              }`}
              onPress={() => onPriceRangeChange({ min: 5000, max: 20000 })}
            >
              <Text
                className={`text-xs font-body-medium ${
                  priceRange.min === 5000 && priceRange.max === 20000 ? "text-white" : "text-textColor/70"
                }`}
              >
                Mid-range (₹5K-20K)
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              className={`px-3 py-1.5 rounded-xl ${
                priceRange.min === 20000 && priceRange.max === null
                  ? "bg-button"
                  : "bg-slate-50 border border-slate-200"
              }`}
              onPress={() => onPriceRangeChange({ min: 20000, max: null })}
            >
              <Text
                className={`text-xs font-body-medium ${
                  priceRange.min === 20000 && priceRange.max === null ? "text-white" : "text-textColor/70"
                }`}
              >
                Luxury (₹20K+)
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Sorting */}
        <View>
          <Text className="font-heading text-body-200 mb-2">Sort By</Text>
          <View className="flex-row flex-wrap mb-3">
            {JOURNEY_SORT_OPTIONS.map((sort) => (
              <TouchableOpacity
                key={sort.id}
                className={`px-3 py-1.5 rounded-xl mr-2 mb-2 ${
                  filterSortBy === sort.id ? "bg-button" : "bg-slate-50 border border-slate-200"
                }`}
                onPress={() => onSortByChange(sort.id)}
              >
                <Text
                  className={`text-xs font-body-medium ${
                    filterSortBy === sort.id ? "text-white" : "text-textColor/70"
                  }`}
                >
                  {sort.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          <View className="flex-row mb-2">
            <TouchableOpacity
              className={`flex-1 px-3 py-1.5 rounded-xl mr-2 ${
                filterSortOrder === "asc" ? "bg-button" : "bg-slate-50 border border-slate-200"
              }`}
              onPress={() => onSortOrderChange("asc")}
            >
              <Text
                className={`text-xs font-body-medium text-center ${
                  filterSortOrder === "asc" ? "text-white" : "text-textColor/70"
                }`}
              >
                Ascending
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              className={`flex-1 px-3 py-1.5 rounded-xl ${
                filterSortOrder === "desc" ? "bg-button" : "bg-slate-50 border border-slate-200"
              }`}
              onPress={() => onSortOrderChange("desc")}
            >
              <Text
                className={`text-xs font-body-medium text-center ${
                  filterSortOrder === "desc" ? "text-white" : "text-textColor/70"
                }`}
              >
                Descending
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>

      <TouchableOpacity className="bg-button rounded-xl py-3 w-full" onPress={onApplyFilters}>
        <Text className="text-white font-body-medium text-sm text-center">Apply Filters</Text>
      </TouchableOpacity>
    </Animated.View>
  );
};

export default SearchFilters;
