import React from "react";
import { View, Text, TextInput, TouchableOpacity, ScrollView } from "react-native";
import { Ionicons, MaterialCommunityIcons } from "@expo/vector-icons";
import FilterButton from "../../Common/FilterButton";

interface SearchHeaderProps {
  searchText: string;
  onSearchChange: (text: string) => void;
  onSearchSubmit: () => void;
  onFilterPress: () => void;
  onBackPress: () => void;
  onClearSearch: () => void;
  activeFilter: string | null;
  onFilterSelect: (filterId: string) => void;
  filterOptions: Array<{ id: string; label: string }>;
  activeFilterCount: number;
}

const SearchHeader: React.FC<SearchHeaderProps> = ({
  searchText,
  onSearchChange,
  onSearchSubmit,
  onFilterPress,
  onBackPress,
  onClearSearch,
  activeFilter,
  onFilterSelect,
  filterOptions,
  activeFilterCount,
}) => {
  return (
    <View className="bg-white px-4 pb-2">
      <View className="flex-row items-center mb-3">
        <TouchableOpacity className="mr-3 p-2" onPress={onBackPress}>
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>

        <View className="flex-1 flex-row items-center bg-slate-100 rounded-xl px-4 py-1">
          <Ionicons name="search" size={20} color="#666" />
          <TextInput
            className="flex-1 ml-2 font-body text-sm text-textColor py-2"
            placeholder="Search journeys..."
            value={searchText}
            onChangeText={onSearchChange}
            onSubmitEditing={onSearchSubmit}
            returnKeyType="search"
            autoFocus
          />
          {searchText.length > 0 && (
            <TouchableOpacity onPress={onClearSearch}>
              <Ionicons name="close-circle" size={20} color="#666" />
            </TouchableOpacity>
          )}
        </View>

        <TouchableOpacity
          className={`ml-3 p-2 rounded-lg relative ${activeFilterCount > 0 ? "bg-slate-100" : ""}`}
          onPress={onFilterPress}
        >
          <Ionicons name="options" size={24} color={activeFilterCount > 0 ? "#346AFF" : "#333"} />
          {activeFilterCount > 0 && (
            <View className="absolute top-0 right-0 bg-button rounded-full w-5 h-5 items-center justify-center">
              <Text className="text-white font-body-medium text-[10px]">{activeFilterCount}</Text>
            </View>
          )}
        </TouchableOpacity>
      </View>

      {/* Quick Filters */}
      <ScrollView horizontal showsHorizontalScrollIndicator={false} className="mb-3">
        <View className="flex-row">
          {filterOptions.map((filter) => (
            <FilterButton
              key={filter.id}
              title={filter.label}
              isActive={activeFilter === filter.id}
              onPress={() => onFilterSelect(filter.id)}
            />
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

export default SearchHeader;
