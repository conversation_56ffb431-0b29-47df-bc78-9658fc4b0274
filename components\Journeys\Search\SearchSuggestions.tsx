import React from "react";
import { View, Text, TouchableOpacity, FlatList } from "react-native";
import { Ionicons } from "@expo/vector-icons";

interface SearchSuggestionsProps {
  recentSearches: string[];
  trendingSearches: string[];
  onSearchSelect: (search: string) => void;
  onClearHistory: () => void;
}

const SearchSuggestions: React.FC<SearchSuggestionsProps> = ({
  recentSearches,
  trendingSearches,
  onSearchSelect,
  onClearHistory,
}) => {
  const renderSearchItem = (item: string, type: "recent" | "trending") => (
    <TouchableOpacity
      key={item}
      className="flex-row items-center px-4 py-3 border-b border-gray-100"
      onPress={() => onSearchSelect(item)}
    >
      <Ionicons name={type === "recent" ? "time-outline" : "trending-up-outline"} size={20} color="#6B7280" />
      <Text className="flex-1 ml-3 font-body text-sm text-gray-800">{item}</Text>
      <Ionicons name="chevron-forward" size={18} color="#9CA3AF" />
    </TouchableOpacity>
  );

  return (
    <View className="flex-1 bg-white">
      {/* Recent Searches */}
      {recentSearches.length > 0 && (
        <View>
          <View className="flex-row justify-between items-center px-4 pt-4 pb-2">
            <Text className="font-heading text-base text-gray-800">Recent Searches</Text>
            <TouchableOpacity onPress={onClearHistory}>
              <Text className="text-button font-body text-sm">Clear</Text>
            </TouchableOpacity>
          </View>
          {recentSearches.map((item) => renderSearchItem(item, "recent"))}
        </View>
      )}

      {/* Trending Searches */}
      <View>
        <View className="flex-row justify-between items-center px-4 pt-4 pb-2">
          <Text className="font-heading text-base text-gray-800">Trending Searches</Text>
        </View>
        {trendingSearches.map((item) => renderSearchItem(item, "trending"))}
      </View>

      {/* Popular Destinations Section */}
      <View className="mt-4 px-4">
        <Text className="font-heading text-base text-gray-800 mb-3">Popular Destinations</Text>
        <View className="flex-row flex-wrap">
          {["Coorg", "Goa", "Rishikesh", "Manali", "Ladakh", "Kerala"].map((destination) => (
            <TouchableOpacity
              key={destination}
              className="bg-gray-100 rounded-xl px-3 py-1.5 mr-2 mb-2"
              onPress={() => onSearchSelect(destination)}
            >
              <Text className="font-body text-sm text-gray-800">{destination}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Travel Types Section */}
      <View className="mt-4 px-4 mb-6">
        <Text className="font-heading text-base text-gray-800 mb-3">Journey Types</Text>
        <View className="flex-row flex-wrap">
          {["Trekking", "Beach", "Road Trip", "Wildlife", "Weekend", "Heritage"].map((type) => (
            <TouchableOpacity
              key={type}
              className="bg-gray-100 rounded-xl px-3 py-1.5 mr-2 mb-2"
              onPress={() => onSearchSelect(type)}
            >
              <Text className="font-body text-sm text-gray-800">{type}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </View>
  );
};

export default SearchSuggestions;
