import React, { useState } from "react";
import { View, Text, Modal, ScrollView, TouchableOpacity, ActivityIndicator, Platform } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import PrimaryButton from "../Buttons/PrimaryButton";
import SecondaryButton from "../Buttons/SecondaryButton";
import { uploadTextToFirebase } from "../../services/firebaseStorageService";
import { getAuth } from "firebase/auth";
import apiClient from "../../api/apiClient";
import { router } from "expo-router";

interface TermsAgreementModalProps {
  isVisible: boolean;
  onClose: () => void;
  onAccept: () => void;
  hangoutId: string;
  isPaid?: boolean; // New prop
}

const TermsAgreementModal: React.FC<TermsAgreementModalProps> = ({
  isVisible,
  onClose,
  onAccept,
  hangoutId,
  isPaid = false, // Default to false
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const auth = getAuth();

  const handleAccept = async () => {
    try {
      setIsLoading(true);

      // Create agreement record
      const agreementData = {
        userId: auth.currentUser?.uid,
        hangoutId: hangoutId,
        agreementType: "hangout-terms",
        timestamp: new Date().toISOString(),
        ipAddress: "captured-on-client", // You might want to get this from the server
        userAgent: Platform.OS + " " + Platform.Version,
        termsVersion: "1.0",
      };

      try {
        // Use the new function to directly upload the JSON object
        const agreementPath = `legal-agreements/${auth.currentUser?.uid}`;
        const filename = `${agreementPath}/${hangoutId}.json`;
        await uploadTextToFirebase(agreementData, agreementPath, filename, "json");
      } catch (uploadError) {
        // Log the error but continue with the flow
        console.error("Error uploading agreement to Firebase:", uploadError);
      }

      // Optionally, notify backend about the agreement
      // try {
      //   await apiClient.post("/legal/record-agreement", agreementData);
      // } catch (apiError) {
      //   console.error("Error recording agreement via API:", apiError);
      // }

      // Proceed with acceptance regardless of storage success
      onAccept();
    } catch (error) {
      console.error("Error in agreement process:", error);
      // Still proceed with payment even if recording fails
      onAccept();
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewFullTerms = () => {
    // Close the modal first
    onClose();
    // Navigate to terms of service page
    router.push("/settings/terms-of-service");
  };

  return (
    <Modal visible={isVisible} transparent animationType="slide" onRequestClose={onClose}>
      <View className="flex-1 justify-center items-center bg-black/50 p-4">
        <View className="bg-white w-full rounded-xl p-5 max-h-[80%]">
          <View className="flex-row justify-between items-center mb-4">
            <Text className="text-xl font-heading text-primary-600">Before You Join</Text>
            <TouchableOpacity onPress={onClose}>
              <Ionicons name="close" size={24} color="#4A6FFF" />
            </TouchableOpacity>
          </View>

          <Text className="text-base mb-4 text-gray-700">
            We want you to have a great experience! Please review our community guidelines before joining this hangout.
          </Text>

          <ScrollView className="mb-4">
            <View className="bg-slate-50 p-4 rounded-xl mb-4">
              <Text className="text-base font-body-medium mb-2 text-primary-600">Safety First</Text>
              <Text className="text-base mb-2">
                You're responsible for your safety and belongings during hangouts. We connect people but don't supervise
                events.
              </Text>
            </View>

            <View className="bg-slate-50 p-4 rounded-xl mb-4">
              <Text className="text-base font-body-medium mb-2 text-primary-600">Respect Others</Text>
              <Text className="text-base mb-2">
                Be respectful to everyone you meet. Report any inappropriate behavior through our app.
              </Text>
            </View>

            <View className="bg-slate-50 p-4 rounded-xl mb-4">
              <Text className="text-base font-body-medium mb-2 text-primary-600">Have Fun Responsibly</Text>
              <Text className="text-base mb-2">
                If alcohol is served, drink responsibly and never drive under the influence. Illegal substances are not
                permitted.
              </Text>
            </View>

            {/* Add payment disclaimer section */}
            {isPaid && (
              <View className="bg-slate-50 p-4 rounded-xl mb-4">
                <Text className="text-base font-body-medium mb-2 text-primary-600">Payment Information</Text>
                <Text className="text-base mb-2">
                  Your payment is exclusively for an in-person, offline event. No digital content, virtual goods, or
                  online services are provided. This transaction is for a real-world experience and is processed
                  securely through our payment partner.
                </Text>
              </View>
            )}

            <Text className="text-sm text-gray-500 mb-4">
              By continuing, you agree to our full Terms of Service, including assumption of risk and release of
              liability provisions.
            </Text>

            <TouchableOpacity className="mb-6" onPress={handleViewFullTerms}>
              <Text className="text-primary-600 text-sm font-body-medium">View Full Terms of Service</Text>
            </TouchableOpacity>
          </ScrollView>

          {isLoading ? (
            <ActivityIndicator size="large" color="#4A6FFF" />
          ) : (
            <View>
              <PrimaryButton buttonText="I Understand & Agree" onPressHandler={handleAccept} />
              <View className="h-2" />
              <SecondaryButton buttonText="Not Now" onPressHandler={onClose} />
            </View>
          )}
        </View>
      </View>
    </Modal>
  );
};

export default TermsAgreementModal;
