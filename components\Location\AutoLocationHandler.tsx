import React, { useEffect } from "react";
import { useAutoLocation } from "../../hooks/useAutoLocation";

interface AutoLocationHandlerProps {
  onLocationSet?: (success: boolean) => void;
}

const AutoLocationHandler: React.FC<AutoLocationHandlerProps> = ({ onLocationSet }) => {
  try {
    const { hasLocation } = useAutoLocation();

    // Handle location set callback
    useEffect(() => {
      try {
        if (hasLocation && onLocationSet) {
          onLocationSet(true);
        }
      } catch (error) {
        console.error("Error in location callback:", error);
      }
    }, [hasLocation, onLocationSet]);

    // This component doesn't render anything - it just triggers the native permission dialog
    // The useAutoLocation hook handles all the permission logic automatically
    return null;
  } catch (error) {
    console.error("Error in AutoLocationHandler:", error);
    return null;
  }
};

export default AutoLocationHandler;
