import React, { useEffect } from "react";
import { useAutoLocation } from "../../hooks/useAutoLocation";

interface AutoLocationHandlerProps {
  onLocationSet?: (success: boolean) => void;
}

const AutoLocationHandler: React.FC<AutoLocationHandlerProps> = ({ onLocationSet }) => {
  const { hasLocation } = useAutoLocation();

  // Handle location set callback
  useEffect(() => {
    if (hasLocation && onLocationSet) {
      onLocationSet(true);
    }
  }, [hasLocation, onLocationSet]);

  // This component doesn't render anything - it just triggers the native permission dialog
  // The useAutoLocation hook handles all the permission logic automatically
  return null;
};

export default AutoLocationHandler;
