import React, { useEffect, useState } from "react";
import { View, Text, TouchableOpacity, Modal, ActivityIndicator } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { LinearGradient } from "expo-linear-gradient";
import { Ionicons } from "@expo/vector-icons";
import { useAutoLocation, LocationPermissionState } from "../../hooks/useAutoLocation";

interface AutoLocationHandlerProps {
  onLocationSet?: (success: boolean) => void;
}

const AutoLocationHandler: React.FC<AutoLocationHandlerProps> = ({ onLocationSet }) => {
  const {
    permissionState,
    isUpdatingLocation,
    error,
    hasLocation,
    requestLocationPermission,
    showLocationSettings,
    retryLocationRequest,
  } = useAutoLocation();

  const [showModal, setShowModal] = useState(false);

  // Show modal based on permission state
  useEffect(() => {
    const shouldShowModal =
      !hasLocation &&
      (permissionState === "not-requested" || permissionState === "denied" || permissionState === "requesting");

    setShowModal(shouldShowModal);
  }, [permissionState, hasLocation]);

  // Handle location set callback
  useEffect(() => {
    if (hasLocation && onLocationSet) {
      onLocationSet(true);
    }
  }, [hasLocation, onLocationSet]);

  const handleAllowLocation = async () => {
    const granted = await requestLocationPermission();
    if (!granted && onLocationSet) {
      onLocationSet(false);
    }
  };

  const handleSkipLocation = () => {
    setShowModal(false);
    if (onLocationSet) {
      onLocationSet(false);
    }
  };

  const handleOpenSettings = () => {
    showLocationSettings();
  };

  const handleRetry = () => {
    retryLocationRequest();
  };

  if (!showModal) {
    return null;
  }

  const renderContent = () => {
    switch (permissionState) {
      case "requesting":
        return (
          <View className="items-center py-8">
            <ActivityIndicator size="large" color="#007AFF" />
            <Text className="text-lg font-semibold mt-4 text-center">Requesting Location Permission...</Text>
            <Text className="text-gray-500 text-center mt-2">Please allow location access when prompted</Text>
          </View>
        );

      case "denied":
        return (
          <View className="items-center">
            <View className="w-20 h-20 bg-orange-100 rounded-full items-center justify-center mb-6">
              <Ionicons name="location-outline" size={40} color="#f97316" />
            </View>

            <Text className="text-xl font-bold text-center mb-2">Location Permission Denied</Text>

            <Text className="text-gray-600 text-center mb-6 leading-6">
              We need location access to show you nearby events and personalized content. You can enable this in your
              device settings.
            </Text>

            <View className="w-full space-y-3">
              <TouchableOpacity onPress={handleOpenSettings} className="bg-blue-500 py-4 rounded-xl items-center">
                <Text className="text-white font-semibold text-lg">Open Settings</Text>
              </TouchableOpacity>

              <TouchableOpacity onPress={handleRetry} className="bg-gray-100 py-4 rounded-xl items-center">
                <Text className="text-gray-700 font-semibold text-lg">Try Again</Text>
              </TouchableOpacity>

              <TouchableOpacity onPress={handleSkipLocation} className="py-3 items-center">
                <Text className="text-gray-500 font-medium">Skip for Now</Text>
              </TouchableOpacity>
            </View>
          </View>
        );

      case "denied-permanently":
        return (
          <View className="items-center">
            <View className="w-20 h-20 bg-red-100 rounded-full items-center justify-center mb-6">
              <Ionicons name="location-outline" size={40} color="#ef4444" />
            </View>

            <Text className="text-xl font-bold text-center mb-2">Location Access Blocked</Text>

            <Text className="text-gray-600 text-center mb-6 leading-6">
              Location access has been permanently denied. To enable location features, please go to your device
              settings and allow location access for this app.
            </Text>

            <View className="w-full space-y-3">
              <TouchableOpacity onPress={handleOpenSettings} className="bg-blue-500 py-4 rounded-xl items-center">
                <Text className="text-white font-semibold text-lg">Open Settings</Text>
              </TouchableOpacity>

              <TouchableOpacity onPress={handleSkipLocation} className="py-3 items-center">
                <Text className="text-gray-500 font-medium">Continue Without Location</Text>
              </TouchableOpacity>
            </View>
          </View>
        );

      default: // 'not-requested'
        return (
          <View className="items-center">
            <View className="w-20 h-20 bg-blue-100 rounded-full items-center justify-center mb-6">
              <Ionicons name="location" size={40} color="#007AFF" />
            </View>

            <Text className="text-2xl font-bold text-center mb-2">Enable Location</Text>

            <Text className="text-gray-600 text-center mb-6 leading-6">
              We'll use your location to show you nearby events, hangouts, and personalized recommendations.
            </Text>

            <View className="w-full space-y-3">
              <TouchableOpacity
                onPress={handleAllowLocation}
                className="bg-blue-500 py-4 rounded-xl items-center"
                disabled={isUpdatingLocation}
              >
                {isUpdatingLocation ? (
                  <ActivityIndicator color="white" />
                ) : (
                  <Text className="text-white font-semibold text-lg">Allow Location Access</Text>
                )}
              </TouchableOpacity>

              <TouchableOpacity onPress={handleSkipLocation} className="py-3 items-center">
                <Text className="text-gray-500 font-medium">Skip for Now</Text>
              </TouchableOpacity>
            </View>

            {error && (
              <View className="mt-4 p-3 bg-red-50 rounded-lg">
                <Text className="text-red-600 text-sm text-center">{error}</Text>
              </View>
            )}
          </View>
        );
    }
  };

  return (
    <Modal visible={showModal} transparent={true} animationType="fade" onRequestClose={handleSkipLocation}>
      <View className="flex-1 bg-black/50 justify-center items-center px-6">
        <View className="bg-white rounded-2xl p-6 w-full max-w-sm">{renderContent()}</View>
      </View>
    </Modal>
  );
};

export default AutoLocationHandler;
