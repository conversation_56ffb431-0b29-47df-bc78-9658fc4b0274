import { useEffect } from "react";
import { useRouter } from "expo-router";
import { useAuth } from "../../contexts/AuthContext";

/**
 * Global navigation handler that responds to auth state changes
 * This component should be rendered at the root level to handle navigation
 * regardless of which screen the user is currently on
 */
const AuthNavigationHandler = () => {
  const router = useRouter();
  const { authState, isLoading } = useAuth();

  useEffect(() => {
    if (!isLoading) {
      console.log("AuthNavigationHandler - Auth state changed:", authState);

      switch (authState) {
        case "authenticated":
          // User is fully authenticated and has completed interests
          console.log("Navigating to home");
          router.replace("/(tabs)/home");
          break;
        case "interests-incomplete":
          // User is authenticated but needs to complete interests
          console.log("Navigating to hangout interests");
          router.replace("/onboarding/hangout-interests");
          break;
        case "email-unverified":
          // User needs to verify their email
          console.log("Navigating to email verification");
          router.replace("/(auth)/email-verification");
          break;
        case "unauthenticated":
          // User needs to authenticate
          console.log("Navigating to auth");
          router.replace("/(auth)/google-signin");
          break;
        case "onboarding":
          // User needs to see onboarding screens
          console.log("Navigating to onboarding");
          router.replace("/");
          break;
        default:
          console.log("Unknown auth state:", authState);
          break;
      }
    }
  }, [authState, isLoading, router]);

  // This component doesn't render anything, it just handles navigation
  return null;
};

export default AuthNavigationHandler;
