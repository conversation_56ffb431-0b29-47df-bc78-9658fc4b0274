import React from "react";
import { View, Text, TouchableOpacity } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import Animated, { FadeInDown, ZoomIn } from "react-native-reanimated";
import { useRouter } from "expo-router";
import { LinearGradient } from "expo-linear-gradient";

interface EmptyNotificationsProps {
  message?: string;
  actionText?: string;
  actionRoute?: string;
}

const EmptyNotifications: React.FC<EmptyNotificationsProps> = ({
  message = "You don't have any notifications yet. We'll notify you about important updates, hangout invites, and more!",
  actionText = "Explore Hangouts",
  actionRoute = "/(tabs)/home",
}) => {
  const router = useRouter();

  return (
    <View className="flex-1 items-center justify-center px-6 py-10">
      {/* Modern gradient background */}
      <LinearGradient
        colors={["rgba(215, 38, 56, 0.03)", "rgba(255, 255, 255, 0)"]}
        style={{ position: "absolute", height: 400, left: 0, right: 0, top: 0 }}
      />

      {/* Illustration */}
      <Animated.View entering={ZoomIn.duration(800)} className="mb-8">
        <View className="bg-white p-5 rounded-full shadow-sm" style={{ elevation: 2 }}>
          <Ionicons name="notifications-off-outline" size={60} color="#D72638" />
        </View>
      </Animated.View>

      {/* Text content */}
      <Animated.Text
        className="text-xl font-heading text-center mb-3 text-gray-800"
        entering={FadeInDown.duration(600).delay(200)}
      >
        No Notifications
      </Animated.Text>

      <Animated.Text
        className="text-gray-500 text-center font-body text-base mb-8 max-w-xs"
        entering={FadeInDown.duration(600).delay(400)}
      >
        {message}
      </Animated.Text>

      {/* Action button */}
      <Animated.View entering={FadeInDown.duration(600).delay(600)}>
        <TouchableOpacity
          className="flex-row items-center py-3 px-6 rounded-xl bg-primary"
          onPress={() => router.push(actionRoute)}
          activeOpacity={0.7}
        >
          <Ionicons name="compass-outline" size={18} color="white" style={{ marginRight: 8 }} />
          <Text className="text-white font-heading text-md">{actionText}</Text>
        </TouchableOpacity>
      </Animated.View>
    </View>
  );
};

export default EmptyNotifications;
