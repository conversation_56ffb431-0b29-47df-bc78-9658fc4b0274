import React from "react";
import { View, Text, TouchableOpacity } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { formatNotificationTime } from "../../lib/utils/commonUtils";
import { NotificationProps } from "../../lib/types/Notification/notificationTypes";

// Get icon and color based on notification type
const getNotificationTypeDetails = (type?: string) => {
  switch (type) {
    case "booking_confirmation":
      return {
        icon: "checkmark-circle-outline",
        color: "#10B981", // green
      };
    case "payment":
      return {
        icon: "card-outline",
        color: "#6366F1", // indigo
      };
    case "hangout_invite":
      return {
        icon: "people-outline",
        color: "#F59E0B", // amber
      };
    case "hangout_viewed":
      return {
        icon: "eye-outline",
        color: "#3B82F6", // blue
      };
    case "journey_update":
      return {
        icon: "map-outline",
        color: "#EC4899", // pink
      };
    case "message":
      return {
        icon: "chatbubble-outline",
        color: "#3B82F6", // blue
      };
    case "friend_request":
      return {
        icon: "person-add-outline",
        color: "#8B5CF6", // violet
      };
    default:
      return {
        icon: "notifications-outline",
        color: "#D72638", // primary red
      };
  }
};

const Notification: React.FC<NotificationProps> = ({
  title,
  description,
  isRead = true,
  createdAt,
  onPress,
  apiData,
}) => {
  // Get notification type details
  const typeDetails = getNotificationTypeDetails(apiData?.type);

  // Format the time if available
  const formattedTime =
    formatNotificationTime(createdAt || "") || (apiData?.createdAt ? formatNotificationTime(apiData.createdAt) : "");

  return (
    <TouchableOpacity
      className={`flex-row p-3 rounded-lg mx-4 my-1 border border-slate-200 ${
        !isRead ? "bg-primary-100/10 border-primary" : ""
      }`}
      onPress={onPress}
      activeOpacity={0.7}
    >
      {/* Left icon/avatar */}
      <View
        className={`w-10 h-10 rounded-full items-center justify-center mr-3`}
        style={{ backgroundColor: `${typeDetails.color}15` }}
      >
        <Ionicons name={typeDetails.icon as any} size={18} color={typeDetails.color} />
      </View>

      {/* Content */}
      <View className="flex-1">
        {/* Title and timestamp */}
        <View className="flex-row justify-between items-start">
          <Text
            className={`flex-1 text-[15px] ${!isRead ? "font-bold" : "font-medium"} text-gray-800`}
            numberOfLines={1}
          >
            {title}
          </Text>
          {formattedTime && <Text className="text-[12px] text-gray-500 ml-2">{formattedTime}</Text>}
        </View>

        {/* Description */}
        <Text className="text-[13px] text-gray-600 mt-1" numberOfLines={2}>
          {description}
        </Text>

        {/* Action button */}
        {apiData?.actionType === "link" && apiData.actionText && (
          <TouchableOpacity
            className="flex-row items-center mt-2 self-start rounded-lg px-3 py-1.5"
            style={{ backgroundColor: `${typeDetails.color}15` }}
            onPress={onPress}
          >
            <Text className="text-[12px] font-medium mr-1" style={{ color: typeDetails.color }}>
              {apiData.actionText}
            </Text>
            <Ionicons name="chevron-forward" size={12} style={{ color: typeDetails.color }} />
          </TouchableOpacity>
        )}
      </View>

      {/* Unread indicator */}
      {!isRead && <View className="w-2 h-2 rounded-full mt-2" style={{ backgroundColor: typeDetails.color }} />}
    </TouchableOpacity>
  );
};

export default Notification;
