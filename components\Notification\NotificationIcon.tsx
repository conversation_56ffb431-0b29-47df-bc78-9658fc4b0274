import { useNavigation, usePathname } from "expo-router";
import React, { useEffect, useRef } from "react";
import { Text, TouchableOpacity, View, Animated, Easing } from "react-native";
import Ionicons from "react-native-vector-icons/Ionicons";
import { NotificationIconProps } from "../../lib/types/Notification/notificationTypes";
import { NavigationProp } from "../../lib/types/commonTypes";
import { useNotificationCount } from "../../hooks/useNotifications";

const NotificationIcon: React.FC<NotificationIconProps> = ({
  iconName,
  notificationCount: propCount,
  iconColor = "black",
  notificationColor,
  size = 24,
  showDot = false,
  darkBackground = false,
}) => {
  const navigation = useNavigation<NavigationProp>();
  const { data: countData } = useNotificationCount();
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;

  // Use the count from props if provided, otherwise use the count from the API
  const notificationCount = propCount !== undefined ? propCount : countData?.data?.unreadCount || 0;

  // Determine if we should show the notification indicator
  const hasNotifications = notificationCount > 0;

  // Create bell ringing animation when there are unread notifications
  useEffect(() => {
    if (hasNotifications) {
      // Create a more pronounced bell ringing animation
      Animated.loop(
        Animated.sequence([
          // Scale up slightly
          Animated.timing(scaleAnim, {
            toValue: 1.15,
            duration: 150,
            easing: Easing.out(Easing.ease),
            useNativeDriver: true,
          }),
          // First ring - stronger movement with scale
          Animated.parallel([
            Animated.timing(rotateAnim, {
              toValue: 0.25,
              duration: 120,
              easing: Easing.out(Easing.sin),
              useNativeDriver: true,
            }),
            Animated.timing(scaleAnim, {
              toValue: 1.1,
              duration: 120,
              easing: Easing.out(Easing.sin),
              useNativeDriver: true,
            }),
          ]),
          Animated.timing(rotateAnim, {
            toValue: -0.25,
            duration: 120,
            easing: Easing.out(Easing.sin),
            useNativeDriver: true,
          }),
          Animated.timing(rotateAnim, {
            toValue: 0.2,
            duration: 100,
            easing: Easing.out(Easing.sin),
            useNativeDriver: true,
          }),
          Animated.timing(rotateAnim, {
            toValue: -0.2,
            duration: 100,
            easing: Easing.out(Easing.sin),
            useNativeDriver: true,
          }),
          // Scale back to normal as ringing slows
          Animated.parallel([
            Animated.timing(rotateAnim, {
              toValue: 0,
              duration: 150,
              easing: Easing.out(Easing.sin),
              useNativeDriver: true,
            }),
            Animated.timing(scaleAnim, {
              toValue: 1,
              duration: 150,
              easing: Easing.out(Easing.sin),
              useNativeDriver: true,
            }),
          ]),
          // Pause between rings
          Animated.delay(3000),
        ]),
        { iterations: -1 }
      ).start();
    } else {
      // Reset animations when there are no notifications
      rotateAnim.setValue(0);
      scaleAnim.setValue(1);
    }

    return () => {
      rotateAnim.stopAnimation();
      scaleAnim.stopAnimation();
    };
  }, [hasNotifications]);

  // Convert rotation value to degrees for transform
  const spin = rotateAnim.interpolate({
    inputRange: [-1, 1],
    outputRange: ["-60deg", "60deg"],
  });

  return (
    <TouchableOpacity
      className="relative"
      onPress={() => {
        // Navigate to notifications screen
        navigation.navigate("(notifications)", { screen: "notifications" });
      }}
    >
      <Animated.View
        style={{
          transform: [{ rotate: hasNotifications ? spin : "0deg" }, { scale: hasNotifications ? scaleAnim : 1 }],
        }}
      >
        <Ionicons name={iconName} size={size} color={darkBackground ? "white" : iconColor} />
      </Animated.View>

      {hasNotifications && !showDot && (
        <View
          style={{
            backgroundColor: notificationColor === "primary" ? "#D72638" : "#FFDE59",
            position: "absolute",
            top: -4,
            right: -4,
            width: 16,
            height: 16,
            borderRadius: 8,
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Text
            style={{
              fontWeight: "bold",
              fontSize: 8,
              color: notificationColor === "primary" ? "white" : "black",
              textAlign: "center",
            }}
          >
            {notificationCount > 9 ? "9+" : notificationCount}
          </Text>
        </View>
      )}

      {showDot && hasNotifications && (
        <View
          style={{
            backgroundColor: notificationColor === "primary" ? "#D72638" : "#FFDE59",
            position: "absolute",
            top: -1,
            right: 0,
            width: 8,
            height: 8,
            borderRadius: 4,
          }}
        />
      )}
    </TouchableOpacity>
  );
};

export default NotificationIcon;
