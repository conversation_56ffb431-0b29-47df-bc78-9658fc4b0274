import React from "react";
import { View, FlatList, RefreshControl, ActivityIndicator, Text, TouchableOpacity } from "react-native";
import { Notification as NotificationType } from "../../api/notificationService";
import Notification from "./Notification";
import EmptyNotifications from "./EmptyNotifications";
import NotificationSkeleton from "./NotificationSkeleton";

import Animated, { FadeIn } from "react-native-reanimated";
import { Ionicons } from "@expo/vector-icons";

interface NotificationListProps {
  notifications: NotificationType[];
  isLoading: boolean;
  isFetchingNextPage: boolean;
  refreshing: boolean;
  onRefresh: () => void;
  onEndReached: () => void;
  onMarkAsRead: (notification: NotificationType) => void;
  onMarkAllAsRead: () => void;
  isMarkingAllAsRead: boolean;
}

const NotificationList: React.FC<NotificationListProps> = ({
  notifications,
  isLoading,
  isFetchingNextPage,
  refreshing,
  onRefresh,
  onEndReached,
  onMarkAsRead,
  onMarkAllAsRead,
  isMarkingAllAsRead,
}) => {
  // Render a notification item
  const renderNotification = ({ item }: { item: NotificationType }) => (
    <Notification
      title={item.title}
      description={item.message}
      isRead={item.isRead}
      createdAt={item.createdAt}
      apiData={item}
      onPress={() => onMarkAsRead(item)}
    />
  );

  // Render the list footer (loading indicator or empty)
  const renderFooter = () => {
    if (isFetchingNextPage) {
      return (
        <View className="py-4 items-center">
          <ActivityIndicator size="small" color="#D72638" />
        </View>
      );
    }
    return null;
  };

  // Render empty state
  const renderEmpty = () => {
    if (isLoading) {
      return <NotificationSkeleton />;
    }
    return <EmptyNotifications />;
  };

  return (
    <View className="flex-1">
      {notifications.length > 0 && (
        <Animated.View className="flex-row justify-between items-center px-4 py-3" entering={FadeIn.duration(300)}>
          {notifications.filter((n) => !n.isRead).length > 0 && (
            <View className="flex-row items-center">
              <View className="px-2 py-0.5 bg-primary-200 rounded-full">
                <Text className="text-[10px] font-bold text-textColor">
                  {notifications.filter((n) => !n.isRead).length} new
                </Text>
              </View>
            </View>
          )}
          <TouchableOpacity
            onPress={onMarkAllAsRead}
            disabled={isMarkingAllAsRead}
            className="ml-auto flex-row items-center"
            activeOpacity={0.7}
          >
            {/* ICON */}
            <Ionicons name="checkmark-done-outline" size={18} color="#346aff" style={{ marginRight: 8 }} />
            <Text className="text-button font-medium text-[14px]">
              {isMarkingAllAsRead ? "Marking..." : "Mark all as read"}
            </Text>
          </TouchableOpacity>
        </Animated.View>
      )}

      <FlatList
        data={notifications}
        renderItem={renderNotification}
        keyExtractor={(item) => item._id}
        contentContainerStyle={{
          flexGrow: 1,
          ...(notifications.length === 0 && { flex: 1 }),
        }}
        // No separator needed as each notification has its own separator
        ListEmptyComponent={renderEmpty}
        ListFooterComponent={renderFooter}
        onEndReached={onEndReached}
        onEndReachedThreshold={0.5}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} colors={["#D72638"]} tintColor="#D72638" />
        }
        showsVerticalScrollIndicator={false}
        // Modern styling
        bounces={true}
        overScrollMode="always"
        initialNumToRender={10}
        maxToRenderPerBatch={10}
        windowSize={10}
      />
    </View>
  );
};

export default NotificationList;
