import { useEffect } from "react";
import { useNotificationPermission } from "../../hooks/useNotificationPermission";

const NotificationPermissionHandler = () => {
  const { permissionStatus, shouldPrompt, requestPermission } = useNotificationPermission();

  useEffect(() => {
    // Request permission if we should prompt and it's not already granted
    if (shouldPrompt && permissionStatus !== "granted") {
      requestPermission();
    }
  }, [shouldPrompt, permissionStatus, requestPermission]);

  // This component doesn't render anything
  return null;
};

export default NotificationPermissionHandler;
