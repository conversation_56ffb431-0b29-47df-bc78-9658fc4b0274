import React from "react";
import { View } from "react-native";
import Animated, { FadeInDown, LinearTransition } from "react-native-reanimated";

interface NotificationSkeletonProps {
  count?: number;
}

const NotificationSkeleton: React.FC<NotificationSkeletonProps> = ({ count = 5 }) => {
  return (
    <View className="flex-1">
      {/* Header skeleton */}
      <Animated.View
        className="flex-row justify-between items-center px-5 py-3 bg-white border-b border-gray-100 mb-2"
        entering={FadeInDown.duration(300)}
      >
        <View className="h-6 bg-gray-200 rounded-full w-1/3" style={{ opacity: 0.7 }} />
        <View className="h-4 bg-gray-200 rounded-full w-1/4" style={{ opacity: 0.7 }} />
      </Animated.View>

      {/* Notification items */}
      {Array.from({ length: count }).map((_, index) => (
        <Animated.View
          key={index}
          className="flex-row py-4 px-4 border-b border-gray-100"
          entering={FadeInDown.duration(400).delay(index * 100)}
          layout={LinearTransition.duration(300)}
        >
          {/* Icon placeholder */}
          <View className="mr-3">
            <View
              className="w-10 h-10 rounded-full items-center justify-center"
              style={{ backgroundColor: "rgba(209, 213, 219, 0.3)" }}
            />
          </View>

          {/* Content placeholders */}
          <View className="flex-1 justify-center">
            <View className="flex-row justify-between items-start mb-2">
              <View className="h-4 bg-gray-200 rounded w-2/5 mb-2" style={{ opacity: 0.7 }} />
              <View className="h-3 bg-gray-200 rounded w-12" style={{ opacity: 0.7 }} />
            </View>
            <View className="h-3 bg-gray-200 rounded w-5/6 mb-2" style={{ opacity: 0.7 }} />
            <View className="h-3 bg-gray-200 rounded w-3/4" style={{ opacity: 0.7 }} />

            {/* Action button placeholder (for some items) */}
            {index % 3 === 0 && <View className="h-6 bg-gray-200 rounded-lg w-1/3 mt-2" style={{ opacity: 0.5 }} />}
          </View>

          {/* Unread indicator for some items */}
          {index % 2 === 0 && <View className="w-2 h-2 rounded-full bg-gray-300 mt-2" style={{ opacity: 0.7 }} />}
        </Animated.View>
      ))}
    </View>
  );
};

export default NotificationSkeleton;
