import { View, Text } from "react-native";
import React from "react";
import StepIndicator from "./StepIndicator";
import Animated, { BounceInRight } from "react-native-reanimated";
import { OnBoardingStepProps } from "../../lib/types/OnBoarding/onBoardingTypes";

const OnBoardingStep: React.FC<OnBoardingStepProps> = ({ step, title, description }) => {
  return (
    <View className="flex justify-between gap-y-4">
      <Animated.View key={step} className="flex gap-y-4" entering={BounceInRight.duration(500)}>
        <Text className="text-white text-4xl font-bold w-80">{title}</Text>
        <Text className="text-white">{description}</Text>
      </Animated.View>
      <View>
        <StepIndicator total_steps={3} current_step={step} />
      </View>
    </View>
  );
};

export default OnBoardingStep;
