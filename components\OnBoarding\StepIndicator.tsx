import { View } from "react-native";
import React from "react";
import Animated, { BounceInLeft } from "react-native-reanimated";
import { StepIndicatorProps } from "../../lib/types/OnBoarding/onBoardingTypes";

const StepIndicator: React.FC<StepIndicatorProps> = ({ total_steps, current_step }) => {
  return (
    <Animated.View className="h-4 flex-row w-full gap-x-1" entering={BounceInLeft.duration(500)} key={current_step}>
      {[...Array(total_steps)].map((_, index) => (
        <View key={index} className={`h-1 flex-1 overflow-hidden ${current_step > index ? "bg-white" : "bg-gray-600"}`}>
          {index === current_step && <View className="bg-white h-full w-full" />}
        </View>
      ))}
    </Animated.View>
  );
};

export default StepIndicator;
