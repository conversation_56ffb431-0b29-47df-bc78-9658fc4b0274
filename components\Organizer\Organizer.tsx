import React, { useState, useEffect } from "react";
import { View, Text, ScrollView, RefreshControl, TouchableOpacity, Image } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { StatusBar } from "expo-status-bar";
import { router } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import HomePageHeader from "../HomePage/HomePageHeader";
import { useAppSelector } from "../../reduxStore/hooks";
import { RootState } from "../../reduxStore/store";
import { useOrganizerHangouts } from "../../hooks/useOrganizerHangouts";
import { useOrganizerJourneys } from "../../hooks/useOrganizerJourneys";
import { USER_ROLES } from "../../constants/AuthConstants";
import { statusbgColorMap } from "../../constants/common";
import { toTitleCase } from "../../lib/utils/commonUtils";

const OrganizerHomeScreen = () => {
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState("hangouts");
  const { user } = useAppSelector((state: RootState) => state.user);

  const isHangoutHost = user?.role?.includes(USER_ROLES.HANGOUT_HOST);
  const isJourneyCaptain = user?.role?.includes(USER_ROLES.JOURNEY_CAPTAIN);

  // Default to first available role
  useEffect(() => {
    if (!isHangoutHost && isJourneyCaptain) {
      setActiveTab("journeys");
    }
  }, [isHangoutHost, isJourneyCaptain]);

  // Fetch user's hangouts
  const {
    data: hangoutsData,
    isLoading: isHangoutsLoading,
    refetch: refetchHangouts,
  } = useOrganizerHangouts({
    page: 1,
    limit: 10,
  });

  // Fetch user's journeys
  const {
    data: journeysData,
    isLoading: isJourneysLoading,
    refetch: refetchJourneys,
  } = useOrganizerJourneys(user?._id);

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      if (isHangoutHost) await refetchHangouts();
      if (isJourneyCaptain) await refetchJourneys();
    } catch (error) {
      console.error("Error refreshing:", error);
    } finally {
      setRefreshing(false);
    }
  };

  const renderTabs = () => {
    const tabs = [];

    if (isHangoutHost) {
      tabs.push({ id: "hangouts", title: "My Hangouts" });
    }

    if (isJourneyCaptain) {
      tabs.push({ id: "journeys", title: "My Journeys" });
    }

    return (
      <View className="flex-row mb-4">
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab.id}
            className={`px-4 py-2 rounded-full mr-2 ${activeTab === tab.id ? "bg-midnightBlue" : "bg-gray-100"}`}
            onPress={() => setActiveTab(tab.id)}
          >
            <Text className={`font-body-medium ${activeTab === tab.id ? "text-white" : "text-gray-600"}`}>
              {tab.title}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const renderHangouts = () => {
    const hangouts = hangoutsData?.pages[0]?.data || [];

    if (isHangoutsLoading) {
      return (
        <View className="space-y-3">
          {[1, 2, 3].map((_, index) => (
            <View key={index} className="h-[100px] rounded-xl bg-gray-100 animate-pulse" />
          ))}
        </View>
      );
    }

    if (hangouts.length === 0) {
      return (
        <View className="items-center justify-center py-8">
          <Ionicons name="people-outline" size={48} color="#D72638" />
          <Text className="font-heading text-lg text-textColor mt-4">No hangouts yet</Text>
          <Text className="text-gray-500 text-center mb-6">Start creating memorable experiences</Text>
          <TouchableOpacity
            className="bg-secondary px-6 py-3 rounded-full"
            onPress={() => router.push("/hangouts/create")}
          >
            <Text className="text-white font-body-medium">Create Hangout</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <View className="space-y-3">
        {hangouts.map((hangout) => (
          <TouchableOpacity
            key={hangout._id}
            className="flex-row h-[100px] rounded-xl overflow-hidden border border-slate-100"
            onPress={() => router.push(`/organizer/hangouts/${hangout._id}`)}
          >
            <Image
              source={{ uri: hangout.featuredImage || hangout.images?.[0] }}
              style={{ width: 100, height: 100 }}
              resizeMode="cover"
            />
            <View className="p-3 flex-1 justify-between">
              <View>
                <Text className="font-heading text-[14px] text-textColor" numberOfLines={1}>
                  {hangout.title}
                </Text>
                <View className="flex-row items-center mt-1">
                  <Ionicons name="calendar-outline" size={12} color="#6B7280" />
                  <Text className="text-gray-500 text-xs ml-1">
                    {hangout.date
                      ? new Date(hangout.date).toLocaleDateString(undefined, { month: "short", day: "numeric" })
                      : "No date"}
                  </Text>
                </View>
              </View>
              <View className="flex-row items-center justify-between">
                <View className="flex-row items-center">
                  <Ionicons name="people-outline" size={12} color="#D72638" />
                  <Text className="text-gray-500 text-xs ml-1">{hangout.participants?.length || 0} participants</Text>
                </View>
                <View className={`bg-${statusbgColorMap[hangout.status.toLowerCase()]} px-2 py-1 rounded-full`}>
                  <Text className="text-[10px] font-body-medium text-textColor">{toTitleCase(hangout.status)}</Text>
                </View>
              </View>
            </View>
          </TouchableOpacity>
        ))}

        <TouchableOpacity
          className="mt-4 border border-secondary border-dashed rounded-xl p-4 items-center"
          onPress={() => router.push("/hangouts/create")}
        >
          <Ionicons name="add-circle-outline" size={24} color="#D72638" />
          <Text className="font-body-medium text-secondary mt-2">Create New Hangout</Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderJourneys = () => {
    const journeys = journeysData?.data || [];

    if (isJourneysLoading) {
      return (
        <View className="space-y-3">
          {[1, 2, 3].map((_, index) => (
            <View key={index} className="h-[100px] rounded-xl bg-gray-100 animate-pulse" />
          ))}
        </View>
      );
    }

    if (journeys.length === 0) {
      return (
        <View className="items-center justify-center py-8">
          <Ionicons name="compass-outline" size={48} color="#4A6FFF" />
          <Text className="font-heading text-lg text-textColor mt-4">No journeys yet</Text>
          <Text className="text-gray-500 text-center mb-6">Start creating amazing adventures</Text>
          <TouchableOpacity
            className="bg-button px-6 py-3 rounded-full"
            onPress={() => router.push("/journeys/create")}
          >
            <Text className="text-white font-body-medium">Create Journey</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <View className="space-y-3">
        {journeys.map((journey) => (
          <TouchableOpacity
            key={journey._id}
            className="flex-row h-[100px] rounded-xl overflow-hidden border border-slate-100"
            onPress={() => router.push(`/organizer/journeys/${journey._id}`)}
          >
            <Image source={{ uri: journey.coverImage }} style={{ width: 100, height: 100 }} resizeMode="cover" />
            <View className="p-3 flex-1 justify-between">
              <View>
                <Text className="font-heading text-[14px] text-textColor" numberOfLines={1}>
                  {journey.title}
                </Text>
                <View className="flex-row items-center mt-1">
                  <Ionicons name="location-outline" size={12} color="#6B7280" />
                  <Text className="text-gray-500 text-xs ml-1" numberOfLines={1}>
                    {journey.destinationCity || journey.destination?.split(",")[0] || "Location unavailable"}
                  </Text>
                </View>
              </View>
              <View className="flex-row items-center justify-between">
                <View className="flex-row items-center">
                  <Ionicons name="calendar-outline" size={12} color="#6B7280" />
                  <Text className="text-gray-500 text-xs ml-1">
                    {journey.startDate
                      ? new Date(journey.startDate).toLocaleDateString(undefined, { month: "short", day: "numeric" })
                      : "No date"}
                  </Text>
                </View>
                <View className={`bg-${statusbgColorMap[journey.status.toLowerCase()]} px-2 py-1 rounded-full`}>
                  <Text className="text-[10px] font-body-medium text-textColor">{toTitleCase(journey.status)}</Text>
                </View>
              </View>
            </View>
          </TouchableOpacity>
        ))}

        <TouchableOpacity
          className="mt-4 border border-button border-dashed rounded-xl p-4 items-center"
          onPress={() => router.push("/journeys/create")}
        >
          <Ionicons name="add-circle-outline" size={24} color="#4A6FFF" />
          <Text className="font-body-medium text-button mt-2">Create New Journey</Text>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: "white" }} edges={["top", "left", "right"]}>
      <StatusBar style="dark" />
      <View className="p-4 pb-0">
        <HomePageHeader />
        <View className="mt-4">
          <Text className="text-gray-500">Manage your hangouts and journeys</Text>
        </View>
      </View>
      <ScrollView
        contentContainerStyle={{ padding: 16, paddingTop: 8 }}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} tintColor="#D72638" colors={["#D72638"]} />
        }
      >
        {renderTabs()}
        {activeTab === "hangouts" && isHangoutHost && renderHangouts()}
        {activeTab === "journeys" && isJourneyCaptain && renderJourneys()}
      </ScrollView>
    </SafeAreaView>
  );
};

export default OrganizerHomeScreen;
