import React from "react";
import { View, Text, TouchableOpacity, Image, ScrollView } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { statusbgColorMap, statusColorMap } from "../../constants/common";
import { toTitleCase } from "../../lib/utils/commonUtils";

interface OrganizerItemDetailsProps {
  id: string;
  type: "hangout" | "journey";
  title: string;
  image: string;
  date?: string;
  location?: string;
  viewsCount?: number;
  participantsCount: number;
  status: string;
}

const OrganizerItemDetails: React.FC<OrganizerItemDetailsProps> = ({
  id,
  type,
  title,
  image,
  date,
  location,
  participantsCount,
  status,
  viewsCount,
}) => {
  const handleViewAsUser = () => {
    router.push(`/${type}s/${id}`);
  };

  const handleEdit = () => {
    router.push(`/hangouts/edit/${id}`);
  };

  const handleViewParticipants = () => {
    router.push(`/organizer/${type}s/${id}/participants`);
  };

  const handleScanQR = () => {
    router.push(`/(tabs)/scan`);
  };

  return (
    <ScrollView className="flex-1 bg-white">
      <Image source={{ uri: image || "https://placehold.co/400x200" }} className="w-full h-48" resizeMode="cover" />

      <View className="p-4">
        <Text className="font-heading text-xl text-textColor">{title}</Text>

        <View className="flex-row items-center mt-2">
          {date && (
            <View className="flex-row items-center mr-4">
              <Ionicons name="calendar-outline" size={16} color="#6B7280" />
              <Text className="text-gray-500 text-sm ml-1">
                {new Date(date).toLocaleDateString(undefined, { month: "short", day: "numeric" })}
              </Text>
            </View>
          )}

          {location && (
            <View className="flex-row items-center">
              <Ionicons name="location-outline" size={16} color="#6B7280" />
              <Text className="text-gray-500 text-sm ml-1" numberOfLines={1}>
                {location}
              </Text>
            </View>
          )}
        </View>

        {/* <View className="flex-row items-center mt-2">
          <View className="flex-row items-center">
            <Ionicons name="people-outline" size={16} color="#6B7280" />
            <Text className="text-gray-500 text-sm ml-1">{participantsCount} participants</Text>
          </View>

          <View className={`ml-4 bg-${statusbgColorMap[status.toLowerCase()]} px-2 py-1 rounded-full`}>
            <Text className="text-xs font-body-medium text-textColor">{toTitleCase(status)}</Text>
          </View>
        </View> */}

        <Text className="font-heading text-lg text-textColor mt-6 mb-3">Organizer Actions</Text>

        <View className="flex-row flex-wrap justify-between">
          <TouchableOpacity
            className="w-[48%] bg-white border border-gray-200 rounded-xl p-4 mb-4 items-center"
            onPress={handleViewAsUser}
          >
            <Ionicons name="eye-outline" size={24} color={type === "hangout" ? "#D72638" : "#4A6FFF"} />
            <Text className="font-body-medium text-textColor mt-2">View as User</Text>
          </TouchableOpacity>

          {type === "hangout" && (
            <TouchableOpacity
              className="w-[48%] bg-white border border-gray-200 rounded-xl p-4 mb-4 items-center"
              onPress={handleEdit}
            >
              <Ionicons name="create-outline" size={24} color={type === "hangout" ? "#D72638" : "#4A6FFF"} />
              <Text className="font-body-medium text-textColor mt-2">
                Edit {type === "hangout" ? "Hangout" : "Journey"}
              </Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            className="w-[48%] bg-white border border-gray-200 rounded-xl p-4 mb-4 items-center"
            onPress={handleViewParticipants}
          >
            <Ionicons name="people-outline" size={24} color={type === "hangout" ? "#D72638" : "#4A6FFF"} />
            <Text className="font-body-medium text-textColor mt-2">Participants</Text>
          </TouchableOpacity>

          <TouchableOpacity
            className="w-[48%] bg-white border border-gray-200 rounded-xl p-4 mb-4 items-center"
            onPress={handleScanQR}
          >
            <Ionicons name="qr-code-outline" size={24} color={type === "hangout" ? "#D72638" : "#4A6FFF"} />
            <Text className="font-body-medium text-textColor mt-2">Scan Tickets</Text>
          </TouchableOpacity>
        </View>

        {/* Additional stats section */}
        <View className="mt-4 bg-gray-50 p-4 rounded-xl">
          <Text className="font-heading text-textColor mb-3">Quick Stats</Text>

          <View className="flex-row justify-between">
            <View className="items-center">
              <Text className="font-heading text-lg text-textColor">{participantsCount}</Text>
              <Text className="text-gray-500 text-xs">Participants</Text>
            </View>

            <View className="items-center">
              <Text className={`font-heading text-${statusColorMap[status.toLowerCase()]}/100`}>
                {status.toUpperCase()}
              </Text>
              <Text className="text-gray-500 text-xs">Status</Text>
            </View>

            <View className="items-center">
              <Text className="font-heading text-lg text-textColor">{viewsCount}</Text>
              <Text className="text-gray-500 text-xs">Views</Text>
            </View>
          </View>
        </View>
      </View>
    </ScrollView>
  );
};

export default OrganizerItemDetails;
