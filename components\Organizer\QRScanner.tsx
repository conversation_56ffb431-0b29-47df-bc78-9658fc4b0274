import React, { useState } from "react";
import { View, Text, TouchableOpacity, Alert, ActivityIndicator, Image } from "react-native";
import { CameraView, useCameraPermissions } from "expo-camera";
import { Ionicons, MaterialCommunityIcons } from "@expo/vector-icons";
import { hangoutsService } from "../../api/hangoutsService";
import { showToast } from "../../lib/utils/showToast";
import { journeysService } from "../../api/journeysService";
import { router } from "expo-router";

interface CheckInResponse {
  success: boolean;
  message: string;
  data: {
    participantId: string;
    userId: string;
    hangoutId: string;
    checkedIn: boolean;
    checkInTime: string;
    user: {
      firstName: string;
      lastName: string;
      profilePic: string;
    };
  };
}

const QRScanner: React.FC = () => {
  const [permission, requestPermission] = useCameraPermissions();
  const [scanned, setScanned] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successData, setSuccessData] = useState<CheckInResponse["data"] | null>(null);

  const handleBarCodeScanned = async ({ type, data }: { type: string; data: string }) => {
    if (scanned) return;

    try {
      setScanned(true);
      setLoading(true);

      // Parse the QR code data as JSON
      let qrData;
      try {
        qrData = JSON.parse(data);
      } catch (parseError) {
        throw new Error("Invalid QR code format");
      }

      console.log("QR Data:", qrData);

      // Check if participantId exists in the QR data
      if (!qrData.participantId) {
        throw new Error("Invalid QR code: Participant ID not found");
      }

      let response;
      if (qrData.journeyId) {
        console.log("Journey ID found in QR code. Redirecting to journey check-in page...");
        response = await journeysService.checkInParticipant(qrData.participantId);
      } else {
        // Call the check-in API with the participantId from QR code
        console.log("Hangout ID found in QR code. Redirecting to hangout check-in page...");
        response = await hangoutsService.checkInParticipant(qrData.participantId);
      }

      if (response.success) {
        setSuccessData(response.data);
        showToast("success", "Participant checked in successfully");
        // redirect to participants page for both hangouts and journeys
        if (qrData.journeyId) {
          router.push(`/organizer/journeys/${qrData.journeyId}/participants`);
        } else {
          router.push(`/organizer/hangouts/${qrData.hangoutId}/participants`);
        }
      } else {
        throw new Error(response.message || "Failed to check in participant");
      }
    } catch (error: any) {
      console.error("Error checking in participant:", error.response?.data);
      let errorMessage = "Failed to check in participant";

      // Handle API error response format
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      showToast("error", errorMessage);
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleScanAgain = () => {
    setScanned(false);
    setSuccessData(null);
  };

  if (!permission) {
    // Camera permissions are still loading
    return (
      <View className="flex-1 justify-center items-center">
        <Text className="font-body text-textColor">Requesting camera permission...</Text>
      </View>
    );
  }

  if (!permission.granted) {
    // Camera permissions are not granted yet
    return (
      <View className="flex-1 justify-center items-center p-4">
        <MaterialCommunityIcons name="camera-off-outline" size={48} color="#D72638" />
        <Text className="font-heading text-lg text-textColor mt-4">Camera permission denied</Text>
        <Text className="text-gray-500 text-center mb-6">We need camera access to scan QR codes for check-ins</Text>
        <TouchableOpacity className="bg-secondary px-6 py-3 rounded-full" onPress={requestPermission}>
          <Text className="text-white font-body-medium">Grant Permission</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View className="flex-1">
      {!scanned ? (
        <>
          <CameraView
            className="flex-1"
            facing="back"
            barcodeScannerSettings={{
              barcodeTypes: ["qr"],
            }}
            onBarcodeScanned={scanned ? undefined : handleBarCodeScanned}
          >
            <View className="absolute top-0 left-0 right-0 bg-black/50 p-4">
              <Text className="text-white font-heading text-center text-lg">Scan Participant QR Code</Text>
              <Text className="text-white font-body text-center mt-2">
                Position the QR code within the frame to check in the participant
              </Text>
            </View>
          </CameraView>
        </>
      ) : (
        <View className="flex-1 justify-center items-center p-4 bg-white">
          {loading ? (
            <ActivityIndicator size="large" color="#D72638" />
          ) : successData ? (
            <>
              <View className="bg-green-100 p-6 rounded-xl items-center w-full max-w-sm">
                <Ionicons name="checkmark-circle" size={64} color="#0a8043" />
                <Text className="font-heading text-lg text-textColor mt-4 text-center">Check-in Successful!</Text>

                {/* User details section */}
                <View className="mt-4 items-center w-full">
                  {successData.user.profilePic ? (
                    <Image
                      source={{ uri: successData.user.profilePic }}
                      className="w-20 h-20 rounded-full border-2 border-green-500"
                    />
                  ) : (
                    <View className="w-20 h-20 rounded-full bg-gray-300 items-center justify-center">
                      <Ionicons name="person" size={36} color="#666" />
                    </View>
                  )}

                  <Text className="font-heading text-xl text-textColor mt-3 text-center">
                    {successData.user.firstName} {successData.user.lastName}
                  </Text>

                  <View className="flex-row items-center mt-2">
                    <Ionicons name="time-outline" size={16} color="#666" />
                    <Text className="text-gray-600 ml-1">
                      Checked in at {new Date(successData.checkInTime).toLocaleTimeString()}
                    </Text>
                  </View>
                </View>
              </View>
              <TouchableOpacity className="bg-secondary px-6 py-3 rounded-full mt-8" onPress={handleScanAgain}>
                <Text className="text-white font-body-medium">Scan Another Code</Text>
              </TouchableOpacity>
            </>
          ) : (
            <>
              <View className="bg-red-100 p-6 rounded-xl items-center w-full max-w-sm">
                <Ionicons name="close-circle" size={64} color="#D72638" />
                <Text className="font-heading text-lg text-textColor mt-4 text-center">Check-in Failed</Text>
                <Text className="text-gray-500 text-center mt-1">{error}</Text>
              </View>
              <TouchableOpacity className="bg-secondary px-6 py-3 rounded-full mt-8" onPress={handleScanAgain}>
                <Text className="text-white font-body-medium">Try Again</Text>
              </TouchableOpacity>
            </>
          )}
        </View>
      )}
    </View>
  );
};

export default QRScanner;
