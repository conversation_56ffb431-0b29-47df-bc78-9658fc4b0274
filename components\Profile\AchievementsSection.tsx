import { View, Text } from "react-native";
import React from "react";

interface Achievement {
  name: string;
  description: string;
  category: string;
  progress: number;
  target: number;
  isCompleted: boolean;
  _id: string;
}

interface AchievementsSectionProps {
  achievements: Achievement[];
}

const AchievementsSection: React.FC<AchievementsSectionProps> = ({ achievements }) => {
  if (!achievements || achievements.length === 0) return null;

  return (
    <View className="mt-6 bg-white rounded-xl p-4 border border-slate-200">
      <Text className="text-lg font-bold mb-3">Achievements</Text>
      {achievements.map((achievement) => (
        <View key={achievement._id} className="mb-4">
          <View className="flex-row justify-between mb-1">
            <Text className="font-medium">{achievement.name}</Text>
            <Text className="text-gray-500">
              {achievement.progress}/{achievement.target}
            </Text>
          </View>
          <Text className="text-gray-500 text-xs mb-2">{achievement.description}</Text>
          <View className="h-2 bg-slate-200 rounded-full overflow-hidden">
            <View
              className={`h-full ${achievement.isCompleted ? "bg-green-500" : "bg-primary"} rounded-full`}
              style={{ width: `${(achievement.progress / achievement.target) * 100}%` }}
            />
          </View>
        </View>
      ))}
    </View>
  );
};

export default AchievementsSection;
