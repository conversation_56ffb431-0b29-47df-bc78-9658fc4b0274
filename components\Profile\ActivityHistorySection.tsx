import { View, Text } from "react-native";
import React from "react";
import { Ionicons } from "@expo/vector-icons";

interface Activity {
  type: string;
  title: string;
  description: string;
  date: string;
  points?: number;
  _id: string;
}

interface ActivityHistorySectionProps {
  activities: Activity[];
}

const ActivityHistorySection: React.FC<ActivityHistorySectionProps> = ({ activities }) => {
  if (!activities || activities.length === 0) return null;

  const getIconName = (type: string): string => {
    switch (type) {
      case "hangout":
        return "people-outline";
      case "journey":
        return "airplane-outline";
      case "badge":
        return "ribbon-outline";
      default:
        return "star-outline";
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", { month: "short", day: "numeric", year: "numeric" });
  };

  return (
    <View className="mt-6 bg-white rounded-xl p-4 shadow-sm">
      <Text className="text-lg font-bold mb-3">Recent Activity</Text>
      {activities.slice(0, 3).map((activity) => (
        <View key={activity._id} className="flex-row mb-4 items-start">
          <View className="w-10 h-10 rounded-full bg-gray-100 items-center justify-center mr-3">
            <Ionicons name={getIconName(activity.type) as any} size={20} color="#666" />
          </View>
          <View className="flex-1">
            <Text className="font-medium">{activity.title}</Text>
            <Text className="text-gray-500 text-sm">{activity.description}</Text>
            <View className="flex-row justify-between mt-1">
              <Text className="text-gray-400 text-xs">{formatDate(activity.date)}</Text>
              {activity.points && <Text className="text-primary text-xs">+{activity.points} points</Text>}
            </View>
          </View>
        </View>
      ))}
    </View>
  );
};

export default ActivityHistorySection;
