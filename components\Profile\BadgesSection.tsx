import { View, Text, Image, ScrollView } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import React from "react";

interface Badge {
  name: string;
  description: string;
  icon: string;
  category: string;
  earnedAt: string;
  _id: string;
  locked: boolean;
}

interface BadgesSectionProps {
  badges: Badge[];
}

const BadgesSection: React.FC<BadgesSectionProps> = ({ badges }) => {
  if (!badges || badges.length === 0) return null;

  return (
    <ScrollView horizontal showsHorizontalScrollIndicator={false}>
      {badges.map((badge) => (
        <View key={badge._id} className="mr-4 items-center w-20">
          {badge.locked ? (
            // blurred image with lock icon
            <View className="w-16 h-16 rounded-full mb-2 overflow-hidden">
              <Image source={{ uri: badge.icon }} className="w-16 h-16 rounded-full" resizeMode="cover" />
              <View className="absolute top-0 left-0 right-0 bottom-0 bg-black/40 items-center justify-center">
                <Ionicons name="lock-closed" size={20} color="white" />
              </View>
            </View>
          ) : (
            <Image source={{ uri: badge.icon }} className="w-16 h-16 rounded-full mb-2" resizeMode="cover" />
          )}

          <Text className="text-center text-xs font-medium">{badge.name}</Text>
        </View>
      ))}
    </ScrollView>
  );
};

export default BadgesSection;
