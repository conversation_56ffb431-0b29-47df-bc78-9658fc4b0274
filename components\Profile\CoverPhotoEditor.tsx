import React from "react";
import { View, TouchableOpacity, Image, Text } from "react-native";
import { Ionicons } from "@expo/vector-icons";

interface CoverPhotoEditorProps {
  imageUrl?: string;
  onPress: () => void;
}

const CoverPhotoEditor: React.FC<CoverPhotoEditorProps> = ({ imageUrl, onPress }) => {
  return (
    <View className="w-full h-full bg-slate-200 items-center justify-center">
      {imageUrl ? (
        <Image source={{ uri: imageUrl }} className="w-full h-full" resizeMode="cover" />
      ) : (
        <View className="items-center">
          <Ionicons name="image-outline" size={40} color="#9ca3af" />
          <Text className="text-gray-500 mt-2">Add Cover Photo</Text>
        </View>
      )}

      <TouchableOpacity
        className="absolute bottom-4 right-4 bg-white/80 rounded-full p-2"
        style={{ zIndex: 100 }}
        onPress={onPress}
        activeOpacity={0.7}
      >
        <Ionicons name="camera" size={24} color="black" />
      </TouchableOpacity>
    </View>
  );
};

export default CoverPhotoEditor;
