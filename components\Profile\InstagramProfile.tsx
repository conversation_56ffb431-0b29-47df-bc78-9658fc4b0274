import { Linking, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import React from "react";
import { Ionicons } from "@expo/vector-icons";
import { extractInstagramUsername } from "../../lib/utils/commonUtils";

const InstagramProfile = ({ url }: { url: string }) => {
  return (
    <TouchableOpacity
      onPress={() => {
        Linking.openURL(url);
      }}
    >
      <View className="flex-row items-center mb-1">
        <Ionicons name="logo-instagram" size={14} color="#d72638" style={{ marginTop: 2 }} />
        <Text className="text-sm ml-1 text-secondary">{extractInstagramUsername(url)}</Text>
      </View>
    </TouchableOpacity>
  );
};

export default InstagramProfile;

const styles = StyleSheet.create({});
