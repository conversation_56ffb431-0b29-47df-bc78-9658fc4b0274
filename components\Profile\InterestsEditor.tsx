import React, { useState, useEffect } from "react";
import { View, Text, Modal, TouchableOpacity, ScrollView, Dimensions, Animated as RNAnimated } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import PrimaryButton from "../Buttons/PrimaryButton";
import InterestsSelector from "./InterestsSelector";
import { updateUserProfile } from "../../api/userService";
import { useAppDispatch, useAppSelector } from "../../reduxStore/hooks";
import { updateUser } from "../../reduxStore/userSlice";
import { showToast } from "../../lib/utils/showToast";
import { RootState } from "../../reduxStore/store";
import { BlurView } from "expo-blur";
import Animated, { FadeIn, SlideInDown } from "react-native-reanimated";

const { height, width } = Dimensions.get("window");

interface InterestsEditorProps {
  type: "hangout" | "journey";
  isVisible: boolean;
  onClose: () => void;
  initialInterests: Record<string, string[]>;
  interestsData: Record<string, { label: string; options: string[] }>;
}

const InterestsEditor: React.FC<InterestsEditorProps> = ({
  type,
  isVisible,
  onClose,
  initialInterests,
  interestsData,
}) => {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state: RootState) => state.user);
  const [selectedInterests, setSelectedInterests] = useState<Record<string, string[]>>(initialInterests || {});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [modalAnimation] = useState(new RNAnimated.Value(0));

  useEffect(() => {
    if (isVisible) {
      RNAnimated.timing(modalAnimation, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      RNAnimated.timing(modalAnimation, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }).start();
    }
  }, [isVisible]);

  const handleInterestsChange = (interests: Record<string, string[]>) => {
    setSelectedInterests(interests);
  };

  const handleSave = async () => {
    setIsSubmitting(true);
    try {
      const profileData = {
        profile: {
          [type === "hangout" ? "hangoutInterests" : "journeyInterests"]: selectedInterests,
        },
      };

      const updatedProfile = await updateUserProfile(profileData);

      if (user) {
        dispatch(
          updateUser({
            ...user,
            profile: {
              ...user.profile,
              [type === "hangout" ? "hangoutInterests" : "journeyInterests"]: selectedInterests,
            },
          })
        );
      }

      showToast("success", "Interests updated successfully");
      onClose();
    } catch (error) {
      console.error("Error updating interests:", error);
      showToast("error", "Failed to update interests. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const getTypeColor = () => {
    return type === "hangout" ? "#D72638" : "#FFDE59";
  };

  const getTypeTextColor = () => {
    return type === "hangout" ? "text-white" : "text-textColor";
  };

  const modalTranslateY = modalAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [height, 0],
  });

  const backdropOpacity = modalAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 0.5],
  });

  return (
    <Modal visible={isVisible} animationType="none" transparent={true} onRequestClose={onClose}>
      <RNAnimated.View
        style={{
          flex: 1,
          backgroundColor: "rgba(0,0,0,0.5)",
        }}
      >
        <BlurView intensity={15} className="absolute inset-0" />

        <RNAnimated.View
          style={{
            flex: 1,
            justifyContent: "flex-end",
            transform: [{ translateY: modalTranslateY }],
          }}
        >
          <View className="bg-white rounded-t-3xl overflow-hidden" style={{ height: height * 0.85 }}>
            {/* Header */}
            <View className="px-4 pt-6 pb-4 border-b border-gray-100 flex-row justify-between items-center">
              <View className="flex-row items-center">
                <View
                  className="w-10 h-10 rounded-full items-center justify-center mr-3"
                  style={{ backgroundColor: `${getTypeColor()}20` }}
                >
                  <Ionicons
                    name={type === "hangout" ? "people-outline" : "compass-outline"}
                    size={20}
                    color={getTypeColor()}
                  />
                </View>
                <Text className="font-heading text-headline-500">
                  {type === "hangout" ? "Hangout" : "Journey"} Interests
                </Text>
              </View>
              <TouchableOpacity
                onPress={onClose}
                className="w-9 h-9 rounded-full bg-gray-100 items-center justify-center"
              >
                <Ionicons name="close" size={20} color="#6B7280" />
              </TouchableOpacity>
            </View>

            {/* Content */}
            <ScrollView className="flex-1 px-4 py-4">
              <Animated.View entering={FadeIn.delay(200).duration(400)}>
                <Text className="font-body text-body-200 text-gray-700 mb-4">
                  Select your interests for {type === "hangout" ? "local hangouts" : "travel journeys"}. This helps us
                  recommend activities you'll love!
                </Text>

                <View className="mb-4 px-3 py-1.5 rounded-full self-start" style={{ backgroundColor: getTypeColor() }}>
                  <Text className={`text-xs font-body-medium ${getTypeTextColor()}`}>
                    {Object.values(selectedInterests).flat().length} interests selected
                  </Text>
                </View>

                <InterestsSelector
                  categories={interestsData}
                  selectedInterests={selectedInterests}
                  onInterestsChange={handleInterestsChange}
                />
                <View className="h-20" />
              </Animated.View>
            </ScrollView>

            {/* Footer */}
            <View className="px-4 py-4 border-t border-gray-100 flex-row">
              <TouchableOpacity
                className="flex-1 mr-2 py-3.5 rounded-xl border border-gray-200 items-center justify-center"
                onPress={onClose}
                disabled={isSubmitting}
              >
                <Text className="font-body-medium text-gray-700">Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                className="flex-1 ml-2 py-3.5 rounded-xl items-center justify-center"
                style={{ backgroundColor: getTypeColor() }}
                onPress={handleSave}
                disabled={isSubmitting}
              >
                <Text className={`font-body-medium ${getTypeTextColor()}`}>{isSubmitting ? "Saving..." : "Save"}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </RNAnimated.View>
      </RNAnimated.View>
    </Modal>
  );
};

export default InterestsEditor;
