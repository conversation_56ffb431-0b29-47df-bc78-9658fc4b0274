import React, { useState } from "react";
import { View, Text, TouchableOpacity, ScrollView } from "react-native";
import Ionicons from "@expo/vector-icons/Ionicons";
import InterestsEditor from "./InterestsEditor";
import { HANGOUT_INTERESTS, JOURNEY_INTERESTS } from "../../constants/InterestsConstants";
import Animated, { FadeIn, FadeInRight } from "react-native-reanimated";
import { Feather } from "@expo/vector-icons";

interface InterestsSectionProps {
  hangoutInterests: Record<string, string[]>;
  journeyInterests: Record<string, string[]>;
}

const InterestsSection: React.FC<InterestsSectionProps> = ({ hangoutInterests, journeyInterests }) => {
  const [editingType, setEditingType] = useState<"hangout" | "journey" | null>(null);

  const getTypeColor = (type: "hangout" | "journey") => {
    return type === "hangout" ? "#D72638" : "#FFDE59";
  };

  const getTypeBackgroundColor = (type: "hangout" | "journey") => {
    return type === "hangout" ? "rgba(215, 38, 56, 0.1)" : "rgba(255, 222, 89, 0.25)";
  };

  const getTypeTextColor = (type: "hangout" | "journey") => {
    return type === "hangout" ? "#D72638" : "#000000";
  };

  const renderInterestTags = (interests: Record<string, string[]>, type: "hangout" | "journey") => {
    if (!interests || Object.keys(interests).length === 0) {
      return (
        <View className="flex-row items-center py-3">
          <Text className="text-gray-500 text-body-100 italic mr-2">No interests selected</Text>
          <TouchableOpacity onPress={() => setEditingType(type)} className="bg-primary-100 px-3 py-1 rounded-full">
            <Text className="text-link text-body-100 font-body-medium">Add interests</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <View className="py-2">
        <ScrollView horizontal showsHorizontalScrollIndicator={false} className="py-1">
          <View className="flex-row flex-wrap">
            {Object.entries(interests).map(([category, items]) =>
              items.map((interest, index) => (
                <Animated.View
                  key={`${category}-${index}`}
                  entering={FadeInRight.delay(index * 50).springify()}
                  className="mr-2 mb-2"
                >
                  <View
                    className="px-3 py-1.5 rounded-full flex-row items-center"
                    style={{
                      backgroundColor: getTypeBackgroundColor(type),
                      borderWidth: 1,
                      borderColor: getTypeColor(type) + (type === "hangout" ? "30" : "90"),
                    }}
                  >
                    <Text className="text-body-100 font-body-medium" style={{ color: getTypeTextColor(type) }}>
                      {interest}
                    </Text>
                  </View>
                </Animated.View>
              ))
            )}
          </View>
        </ScrollView>
      </View>
    );
  };
  return (
    <Animated.View className="mt-6" entering={FadeIn.duration(400)}>
      <View className="mb-6 bg-white rounded-xl p-4 border border-slate-200">
        <View className="flex-row justify-between items-center mb-3">
          <View className="flex-row items-center">
            {/* <View
              className="w-6 h-6 rounded-full items-center justify-center mr-2 border border-secondary"
              style={{ backgroundColor: "rgba(215, 38, 56, 0.1)" }}
            >
              <Ionicons name="people-outline" size={12} color="#D72638" />
            </View> */}
            <Text className="font-heading text-headline-400">Hangout Interests</Text>
          </View>
          <TouchableOpacity
            onPress={() => setEditingType("hangout")}
            className="w-8 h-8 items-center justify-center rounded-full bg-secondary/10"
          >
            <Feather name="edit-2" size={16} color="#D72638" />
          </TouchableOpacity>
        </View>
        {renderInterestTags(hangoutInterests, "hangout")}
      </View>

      <View className="mb-6 bg-white rounded-xl p-4 border border-slate-200">
        <View className="flex-row justify-between items-center mb-3">
          <View className="flex-row items-center">
            {/* <View
              className="w-6 h-6 rounded-full items-center justify-center mr-2 border border-primary"
              style={{ backgroundColor: "rgba(255, 222, 89, 0.1)" }}
            >
              <Ionicons name="compass-outline" size={12} color="#d8ae2e" />
            </View> */}
            <Text className="font-heading text-headline-400">Journey Interests</Text>
          </View>
          <TouchableOpacity
            onPress={() => setEditingType("journey")}
            className="w-8 h-8 items-center justify-center rounded-full"
            style={{ backgroundColor: "rgba(255, 222, 89, 0.25)" }}
          >
            <Feather name="edit-2" size={16} color="#d8ae2e" />
          </TouchableOpacity>
        </View>
        {renderInterestTags(journeyInterests, "journey")}
      </View>

      {editingType && (
        <InterestsEditor
          type={editingType}
          isVisible={!!editingType}
          onClose={() => setEditingType(null)}
          initialInterests={editingType === "hangout" ? hangoutInterests : journeyInterests}
          interestsData={editingType === "hangout" ? HANGOUT_INTERESTS : JOURNEY_INTERESTS}
        />
      )}
    </Animated.View>
  );
};

export default InterestsSection;
