import React from "react";
import { View, Text, TouchableOpacity, ScrollView } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import Animated, { FadeInDown } from "react-native-reanimated";

interface InterestsSelectorProps {
  categories: Record<string, { label: string; options: string[] }>;
  selectedInterests: Record<string, string[]>;
  onInterestsChange: (interests: Record<string, string[]>) => void;
}

const InterestsSelector: React.FC<InterestsSelectorProps> = ({ categories, selectedInterests, onInterestsChange }) => {
  const toggleInterest = (categoryKey: string, interest: string) => {
    const newInterests = { ...selectedInterests };

    if (!newInterests[categoryKey]) {
      newInterests[categoryKey] = [];
    }

    const index = newInterests[categoryKey].indexOf(interest);

    if (index > -1) {
      newInterests[categoryKey] = newInterests[categoryKey].filter((item) => item !== interest);
      if (newInterests[categoryKey].length === 0) {
        delete newInterests[categoryKey];
      }
    } else {
      newInterests[categoryKey] = [...newInterests[categoryKey], interest];
    }

    onInterestsChange(newInterests);
  };

  const isInterestSelected = (categoryKey: string, interest: string) => {
    return selectedInterests[categoryKey]?.includes(interest) || false;
  };

  const getCategoryCount = (categoryKey: string) => {
    return selectedInterests[categoryKey]?.length || 0;
  };

  return (
    <View className="flex-1">
      {Object.entries(categories).map(([categoryKey, category], categoryIndex) => (
        <Animated.View key={categoryKey} className="mb-8" entering={FadeInDown.delay(categoryIndex * 100).springify()}>
          <View className="flex-row items-center justify-between mb-4">
            <Text className="font-subheading font-bold text-headline-400 text-black-100">{category.label}</Text>
            {getCategoryCount(categoryKey) > 0 && (
              <View className="bg-slate-100 px-2 py-1 rounded-full">
                <Text className="text-xs text-gray-700">{getCategoryCount(categoryKey)} selected</Text>
              </View>
            )}
          </View>

          <View className="flex-row flex-wrap">
            {category.options.map((interest, index) => {
              const isSelected = isInterestSelected(categoryKey, interest);
              return (
                <Animated.View key={interest} entering={FadeInDown.delay(index * 50 + 100).springify()}>
                  <TouchableOpacity
                    className={`mr-3 mb-3 px-4 py-2 rounded-xl flex-row items-center ${
                      isSelected ? "bg-primary-200 border border-primary-500 shadow-sm" : "border border-slate-200"
                    }`}
                    onPress={() => toggleInterest(categoryKey, interest)}
                    style={{
                      shadowColor: isSelected ? "#FFDE59" : "transparent",
                      shadowOffset: { width: 0, height: 2 },
                      shadowOpacity: isSelected ? 0.3 : 0,
                      shadowRadius: 3,
                      elevation: isSelected ? 2 : 0,
                    }}
                    activeOpacity={0.7}
                  >
                    {isSelected ? (
                      <Ionicons name="checkmark-circle" size={16} color="#000" style={{ marginRight: 6 }} />
                    ) : (
                      <Ionicons name="add-circle-outline" size={16} color="#666" style={{ marginRight: 6 }} />
                    )}
                    <Text className={`text-body-200 ${isSelected ? "font-body-medium text-black" : "text-gray-700"}`}>
                      {interest}
                    </Text>
                  </TouchableOpacity>
                </Animated.View>
              );
            })}
          </View>
        </Animated.View>
      ))}
    </View>
  );
};

export default InterestsSelector;
