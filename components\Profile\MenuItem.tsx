import { View, Text, TouchableOpacity } from "react-native";
import React from "react";
import { Ionicons } from "@expo/vector-icons";
import { signOutUser } from "../../services/auth/AuthService";
import { useAppDispatch } from "../../reduxStore/hooks";

export interface MenuItemType {
  id: number;
  title: string;
  icon: string;
  route: string;
  version?: string;
}

interface MenuItemProps {
  item: MenuItemType;
  onPress: (route: string) => void;
}

const MenuItem: React.FC<MenuItemProps> = ({ item, onPress }) => {
  const dispatch = useAppDispatch();

  const handleMenuItemPress = () => {
    if (item.title === "Logout") {
      signOutUser(dispatch);
    } else {
      onPress(item.route);
    }
  };

  return (
    <TouchableOpacity className="flex-row items-center p-4 mt-4" onPress={handleMenuItemPress}>
      <View className="w-8">
        <Ionicons name={item.icon as any} size={20} color={item.title === "Logout" ? "crimson" : "#666"} />
      </View>
      <Text className={`flex-1 text-base ${item.title === "Logout" ? "text-red-500" : "text-gray-700"}`}>
        {item.title}
      </Text>
      {item.version ? (
        <Text className="text-gray-400">{item.version}</Text>
      ) : (
        <Ionicons name="chevron-forward" size={20} color={item.title === "Logout" ? "crimson" : "#666"} />
      )}
    </TouchableOpacity>
  );
};

export default MenuItem;
