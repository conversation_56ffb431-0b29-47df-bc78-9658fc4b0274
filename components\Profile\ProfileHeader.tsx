import { View, Text, TouchableOpacity } from "react-native";
import React from "react";
import { Ionicons } from "@expo/vector-icons";

interface ProfileHeaderProps {
  title: string;
  onEditPress?: () => void;
}

const ProfileHeader: React.FC<ProfileHeaderProps> = ({ title, onEditPress }) => {
  return (
    <View className="flex-row justify-between items-center py-4 ">
      <Text className="text-xl font-semibold">{title}</Text>
      <TouchableOpacity onPress={onEditPress}>
        <Ionicons name="create-outline" size={24} color="black" />
      </TouchableOpacity>
    </View>
  );
};

export default ProfileHeader;
