import { View, Text, Image, TouchableOpacity } from "react-native";
import React from "react";
import { Ionicons } from "@expo/vector-icons";
import { User } from "../../lib/types/Auth/userTypes";
import InstagramProfile from "./InstagramProfile";

interface ProfileInfoProps {
  user: User | null;
}

const ProfileInfo: React.FC<ProfileInfoProps> = ({ user }) => {
  const displayName = `${user?.firstName} ${user?.lastName}`;
  const bio = user?.profile?.bio || "";
  const profilePic = user?.profilePic;
  const location = user?.location?.city ? `${user?.location?.city}, ${user?.location?.country}` : "";
  const languages = user?.profile?.languages || [];
  const verification = user?.profile?.verification?.isVerified;

  return (
    <View className="bg-white rounded-xl p-4 border border-slate-200">
      <View className="flex-row">
        {profilePic ? (
          <Image source={{ uri: profilePic }} className="w-24 h-24 rounded-full border-4 border-white" />
        ) : (
          <View className="w-24 h-24 rounded-full bg-gray-50 items-center justify-center">
            <Ionicons name="person" size={40} color="#999" />
          </View>
        )}
        <View className="ml-4 flex-1 justify-center">
          <View className="flex-row items-center">
            <Text className="text-xl font-bold">{displayName}</Text>
            {verification && <Ionicons name="checkmark-circle" size={18} color="#4CAF50" className="ml-1" />}
          </View>
          {location && (
            <View className="flex-row items-center mt-1">
              <Ionicons name="location-outline" size={14} color="#666" />
              <Text className="text-gray-500 text-sm ml-1">{location}</Text>
            </View>
          )}
          {user?.profile?.instagramProfile && <InstagramProfile url={user?.profile?.instagramProfile} />}
        </View>
      </View>

      {bio && (
        <View className="mt-4">
          <Text className="text-gray-700">{bio}</Text>
        </View>
      )}

      {languages.length > 0 && (
        <View className="mt-3 flex-row flex-wrap items-center">
          <Text className="text-gray-600 mr-2">Speaks:</Text>
          {languages.map((lang: string, index: number) => (
            <View key={index} className="bg-slate-100 border border-slate-200 rounded-full px-2 py-1 mr-2 mb-1">
              <Text className="text-[10px] text-gray-700">{lang}</Text>
            </View>
          ))}
        </View>
      )}
    </View>
  );
};

export default ProfileInfo;
