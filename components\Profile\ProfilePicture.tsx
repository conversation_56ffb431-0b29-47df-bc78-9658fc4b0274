import React from "react";
import { View, Image, TouchableOpacity, Text } from "react-native";
import { Ionicons } from "@expo/vector-icons";

interface ProfilePictureProps {
  imageUrl?: string | null;
  onPress?: () => void;
  size?: number;
}

const ProfilePicture: React.FC<ProfilePictureProps> = ({ imageUrl, onPress, size = 24 }) => {
  return (
    <View className="items-center mb-8">
      <View className="relative">
        <View style={{ width: size * 4, height: size * 4 }} className="rounded-full bg-gray-50 overflow-hidden">
          {!imageUrl ? (
            <View className="absolute top-0 left-0 right-0 bottom-0 items-center justify-center">
              <Ionicons name="person" size={40} color="#999" />
            </View>
          ) : (
            <Image source={{ uri: imageUrl }} className="w-full h-full" resizeMode="cover" />
          )}
        </View>
        {onPress && (
          <TouchableOpacity
            className="absolute bottom-0 right-0 bg-secondary w-8 h-8 rounded-full items-center justify-center"
            onPress={onPress}
          >
            <Ionicons name="camera-outline" size={20} color="white" />
          </TouchableOpacity>
        )}
      </View>
      {onPress && (
        <Text className="text-gray-400 text-xs font-body mt-2">Tap the camera icon to change profile picture</Text>
      )}
    </View>
  );
};

export default ProfilePicture;
