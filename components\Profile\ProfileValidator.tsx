import React, { useState, useEffect } from "react";
import { View, Text, Modal, ScrollView } from "react-native";
import { useNavigation } from "expo-router";
import { useAppSelector } from "../../reduxStore/hooks";
import { RootState } from "../../reduxStore/store";
import PrimaryButton from "../Buttons/PrimaryButton";
import SecondaryButton from "../Buttons/SecondaryButton";

interface ProfileValidatorProps {
  isVisible: boolean;
  onClose: () => void;
  onComplete: () => void;
  missingFields: string[]; // Add this prop to receive missing fields from parent
}

const ProfileValidator: React.FC<ProfileValidatorProps> = ({ isVisible, onClose, onComplete, missingFields }) => {
  const navigation = useNavigation();
  const { user } = useAppSelector((state: RootState) => state.user);
  const [loading, setLoading] = useState(false);

  const handleEditProfile = () => {
    setLoading(true);
    navigation.navigate("settings/profile-edit" as never);
    onClose();
    setLoading(false);
  };

  if (!isVisible) return null;

  return (
    <Modal visible={isVisible} transparent={true} animationType="slide" onRequestClose={onClose}>
      <View className="flex-1 justify-center items-center bg-black/50">
        <View className="bg-white w-11/12 rounded-xl p-5 max-h-[80%]">
          <Text className="text-xl font-bold text-center mb-4">Complete Your Profile</Text>

          <Text className="text-base mb-4 text-center">
            Please complete the following information before joining this hangout:
          </Text>

          <ScrollView className="mb-4 max-h-60">
            {missingFields.map((field, index) => (
              <View key={index} className="flex-row items-center mb-2">
                <View className="h-2 w-2 rounded-full bg-red-500 mr-2" />
                <Text className="text-base">{field}</Text>
              </View>
            ))}
          </ScrollView>

          <PrimaryButton
            buttonText={loading ? "Loading..." : "Edit Profile"}
            onPressHandler={handleEditProfile}
            disabled={loading}
          />
          <View className="h-2" />
          <SecondaryButton buttonText="Cancel" onPressHandler={onClose} disabled={loading} />
        </View>
      </View>
    </Modal>
  );
};

export default ProfileValidator;
