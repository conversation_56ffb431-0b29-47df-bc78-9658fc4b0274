import { View, Text } from "react-native";
import React from "react";

interface StatsProps {
  stats: any;
}

const StatsSection: React.FC<StatsProps> = ({ stats }) => {
  if (!stats) return null;

  const statItems = [
    { title: "Hangouts", value: stats.totalHangoutsAttended || 0 },
    { title: "Journeys", value: stats.totalJourneysAttended || 0 },
    { title: "Points", value: stats.totalPoints || 0 },
    { title: "Level", value: stats.level || 1 },
  ];

  return (
    <View className="mt-6 bg-white rounded-xl p-4 border border-slate-200">
      <Text className="text-lg font-bold mb-3">Offline Stats</Text>
      <View className="flex-row justify-between">
        {statItems.map((stat, index) => (
          <View key={index} className="items-center">
            <Text className="text-secondary text-2xl font-bold">{stat.value}</Text>
            <Text className="text-gray-500 text-sm">{stat.title}</Text>
          </View>
        ))}
      </View>

      {stats.level > 1 && (
        <View className="mt-4">
          <View className="h-2 bg-slate-200 rounded-full overflow-hidden">
            <View
              className="h-full bg-primary rounded-full"
              style={{
                width: `${(stats.experience / stats.experienceToNextLevel) * 100}%`,
              }}
            />
          </View>
          <View className="flex-row justify-between mb-1">
            <Text className="text-xs text-gray-500">Level {stats.level}</Text>
            <Text className="text-xs text-gray-500 mt-1 text-center">
              {stats.experience}/{stats.experienceToNextLevel} XP
            </Text>
            <Text className="text-xs text-gray-500">Level {stats.level + 1}</Text>
          </View>
        </View>
      )}
    </View>
  );
};

export default StatsSection;
