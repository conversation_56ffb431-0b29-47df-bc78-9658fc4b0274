import { StyleSheet, Text, View } from "react-native";
import React from "react";
import Ratings from "../Common/Ratings";
import Ionicons from "@expo/vector-icons/Ionicons";
import ExpandableText from "../Common/ExpandableText";
import PrimaryButton from "../Buttons/PrimaryButton";
import { PackageItem } from "../../lib/types/Reels/planOrPackageReelTypes";
import TravelParticipants from "../Common/TravelParticipants";

const PlanOrPackageDetails = ({ item }: { item: PackageItem }) => {
  const dummyAvatars = Array.from({ length: item?.totalSpots - item?.spotsLeft - 2 }, (_, index) => ({
    id: index,
    uri: `https://picsum.photos/id/${index + 78}/200/300`, // Example avatar URL
  }));
  const dummyDescription = `Discover the Serenity of the Swiss Alps
Embark on a 7-day journey through the breathtaking landscapes of Switzerland. From the picturesque towns of Interlaken and Zermatt to the majestic peaks of the Matterhorn, this trip offers the perfect blend of adventure and relaxation.

Highlights:

Ride the world-famous Glacier Express train.
Explore the charming streets of Lucerne and its iconic Chapel Bridge.
Hike through scenic trails with panoramic views of the Alps.
Indulge in authentic Swiss cuisine and chocolates.
What's Included:

Luxury accommodations at 4-star hotels.
Guided tours with local experts.
Daily breakfast and select meals.
All transportation, including airport transfers.
Whether you're a nature enthusiast, a photography lover, or just seeking tranquility, this tour package promises an unforgettable experience. Don't miss your chance to create memories that will last a lifetime!
`;
  return (
    <View className="gap-y-2 p-6 mb-10">
      <View className="flex-row items-center gap-x-[1px]">
        <Ionicons name="location-outline" size={16} color="#e5e7eb" />
        <Text className="text-gray-200 text-[14px]">{item.location}</Text>
      </View>
      <View>
        <Text className="text-white text-3xl font-semibold">{item.place}</Text>
      </View>
      <View>
        <TravelParticipants
          avatars={dummyAvatars}
          totalSpots={item?.totalSpots}
          spotsLeft={item?.spotsLeft}
          spotsTitle={"people have explored"}
          spotsTitleSize={14}
          imageSize={20}
          textColor={"text-white"}
        />
      </View>
      <View>
        <Ratings rating={Math.floor(item.rating)} ratingText={item?.rating} ratingTextColor="text-white" size={14} />
      </View>
      <View>
        <ExpandableText content={dummyDescription} />
      </View>
      <View className="flex-row items-center justify-between">
        <View className="flex-row items-center">
          <Text className="text-xl text-white">{item?.price} </Text>
          <Text className="text-gray-400">/person</Text>
        </View>
        <PrimaryButton buttonText={"Book Your Slot"} borderRadius={"xl"} width={140} onPressHandler={() => {}} />
      </View>
    </View>
  );
};

export default PlanOrPackageDetails;
