export const adImages = [
  {
    src: require("../assets/images/onboarding-1.jpg"),
  },
  {
    src: require("../assets/images/onboarding-2.jpg"),
  },
  {
    src: require("../assets/images/onboarding-3.jpg"),
  },
];

export const ExploreChoices = [
  {
    name: "NearBy Hangouts",
    image: require("../assets/images/travel-plan.jpeg"),
    description: "Join Hangouts Near You",
  },
  {
    name: "Journeys",
    image: require("../assets/images/tour-package.jpg"),
    description: "Plan a weekend journey",
  },
];
export const dummyData = {
  popularPackages: [
    {
      place: "Kuthu Beach",
      location: "Bali, Indonesia",
      image: "https://picsum.photos/id/13/400/600",
      rating: 3.3,
      spotsLeft: 3,
      totalSpots: 10,
      price: "$245.00",
    },
    {
      place: "Z Point",
      location: "Paris, Uk",
      image: "https://picsum.photos/id/29/400/600",
      rating: 4.8,
      spotsLeft: 7,
      totalSpots: 20,
      price: "$120.00",
    },
    {
      place: "Sky View",
      location: "Tokyo, USA",
      image: "https://picsum.photos/id/162/400/600",
      rating: 2.2,
      spotsLeft: 14,
      totalSpots: 25,
      price: "$400.00",
    },
    {
      place: "Old Village Palace",
      location: "Mumbai, India",
      image: "https://picsum.photos/id/142/400/600",
      rating: 4.5,
      spotsLeft: 1,
      totalSpots: 20,
      price: "$100.00",
    },
    {
      place: "Sunflower Trek",
      location: "New York, USA",
      image: "https://picsum.photos/id/177/400/600",
      rating: 3,
      spotsLeft: 50,
      totalSpots: 54,
      price: "$220.00",
    },
  ],
  trending_rooms: [
    {
      id: "1",
      tripName: "Beach Escape",
      destination: "Maldives, Asia",
      startDate: "2024-09-20",
      endDate: "2024-09-27",
      numberOfDays: 7,
      spotsLeft: 3,
      totalSpots: 10,
      budgetPerPerson: 500,
      organizer: {
        name: "John Doe",
        avatarUrl: "https://picsum.photos/id/12/200/300",
      },
      tripType: "Trekking",
      coverImageUrl: "https://picsum.photos/id/78/200/300",
      isTrending: true,
    },
    {
      id: "2",
      tripName: "Mountain Adventure",
      destination: "Swiss Alps, Switzerland",
      startDate: "2024-10-05",
      endDate: "2024-10-12",
      numberOfDays: 8,
      spotsLeft: 2,
      totalSpots: 8,
      budgetPerPerson: 800,
      organizer: {
        name: "Alice Smith",
        avatarUrl: "https://picsum.photos/id/434/200/300",
      },
      tripType: "Family",
      coverImageUrl: "https://picsum.photos/id/54/200/300",
      isTrending: true,
    },
    {
      id: "3",
      tripName: "Cultural Journey",
      destination: "Kyoto, Japan",
      startDate: "2024-11-01",
      endDate: "2024-11-07",
      numberOfDays: 7,
      spotsLeft: 5,
      totalSpots: 12,
      budgetPerPerson: 1200,
      organizer: {
        name: "Hiro Tanaka",
        avatarUrl: "https://picsum.photos/id/876/200/300",
      },
      tripType: "Cultural",
      coverImageUrl: "https://picsum.photos/id/12/200/300",
      isTrending: false,
    },
    {
      id: "4",
      tripName: "Desert Safari",
      destination: "Dubai, UAE",
      startDate: "2024-12-15",
      endDate: "2024-12-20",
      numberOfDays: 6,
      spotsLeft: 1,
      totalSpots: 4,
      budgetPerPerson: 600,
      organizer: {
        name: "Mohammed Ali",
        avatarUrl: "https://picsum.photos/id/645/200/300",
      },
      tripType: "Adventure",
      coverImageUrl: "https://picsum.photos/id/23/200/300",
      isTrending: true,
    },
  ],
};
