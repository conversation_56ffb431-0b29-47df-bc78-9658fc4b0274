export type OnBoardingStep = {
  title: string;
  description: string;
  image: any;
};

export const ONBOARDING_STEPS_INFO: OnBoardingStep[] = [
  {
    title: "Find Local Hangouts",
    description: "Discover fun meetups happening around you and join like-minded people.",
    image: require("../../assets/images/onboarding-1.jpg"),
  },
  {
    title: "Plan Amazing Journeys",
    description: "Embark on weekend adventures and travel experiences with new friends.",
    image: require("../../assets/images/onboarding-2.jpg"),
  },
  {
    title: "Connect Offline",
    description: "Build real connections and friendships through shared experiences.",
    image: require("../../assets/images/onboarding-3.jpg"),
  },
];

export const AUTH_SCREEN_IMAGES: Record<string, any> = {
  signup_bg: require("../../assets/images/signup-page-bg.png"),
};
