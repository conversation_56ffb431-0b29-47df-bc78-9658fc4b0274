export const HANGOUT_PARTICIPANT_STATUS = {
  PENDING: "pending",
  APPROVED: "approved",
  REJECTED: "rejected",
  WAITLISTED: "waitlisted",
  NO_SHOW: "noShow",
};

// Define the type for the `icons` object
export const icons: { [key: string]: number } = {
  google: require("../assets/images/google-icon.png"),
  primaryLogo: require("../assets/images/main-logo.png"),
  apple: require("../assets/images/apple-logo.png"),
};

// Define the type for `statusColorMap`
export const statusbgColorMap: { [key: string]: string } = {
  draft: "gray-500/20",
  published: "green-500/20",
  cancelled: "red-500/20",
  completed: "blue-500/20",
  soldOut: "orange-500/20",
  upcoming: "primary/20",
  ongoing: "accent/20",
  approved: "green-500/20",
  rejected: "red-500/20",
  waitlisted: "orange-500/20",
  noShow: "red-500/20",
  confirmed: "green-500/20",
  pending: "yellow-500/20",
};

export const statusColorMap: { [key: string]: string } = {
  draft: "gray-500",
  published: "green-500",
  cancelled: "red-500",
  completed: "blue-500",
  soldOut: "orange-500",
  upcoming: "primary",
  ongoing: "accent",
  approved: "green-500",
  rejected: "red-500",
  waitlisted: "orange-500",
  noShow: "red-500",
  confirmed: "green-500",
  pending: "yellow-500",
  approvalpending: "yellow-500",
};

export const payUCheckoutProConfig = {
  merchantName: "Logoutloud",
  primaryColor: "#FFDE59", // your brand color
  secondaryColor: "#008000",
};

export const APP_UNIVERSAL_LINK_BASE = "https://logoutloud.app";
export const APP_SCHEME = "logoutloud://";

export function getHangoutLink(id: string) {
  return `${APP_UNIVERSAL_LINK_BASE}/hangouts/${id}`;
}

export function getJourneyLink(id: string) {
  return `${APP_UNIVERSAL_LINK_BASE}/journeys/${id}`;
}

export function getInviteLink() {
  return `${APP_UNIVERSAL_LINK_BASE}/settings/invite`;
}

export function getHangoutTicketLink(id: string) {
  return `${APP_UNIVERSAL_LINK_BASE}/hangouts/${id}/ticket`;
}

export function getJourneyTicketLink(id: string) {
  return `${APP_UNIVERSAL_LINK_BASE}/journeys/${id}/ticket`;
}
