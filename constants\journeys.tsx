// Constants for journey filters
export const JOURNEY_CATEGORIES = [
  { id: "adventure", label: "Adventure" },
  { id: "cultural", label: "Cultural" },
  { id: "wildlife", label: "Wildlife" },
  { id: "beach", label: "Beach" },
  { id: "mountain", label: "Mountain" },
  { id: "city", label: "City" },
  { id: "historical", label: "Historical" },
  { id: "food", label: "Food" },
  { id: "wellness", label: "Wellness" },
];

export const JOURNEY_TAGS = [
  { id: "family", label: "Family Friendly" },
  { id: "solo", label: "Solo Travel" },
  { id: "budget", label: "Budget" },
  { id: "luxury", label: "Luxury" },
  { id: "offbeat", label: "Off the Beaten Path" },
  { id: "photography", label: "Photography" },
  { id: "wildlife", label: "Wildlife" },
  { id: "hiking", label: "Hiking" },
];

export const JOURNEY_TRAVEL_MODES = [
  { id: "flight", label: "Flight" },
  { id: "train", label: "Train" },
  { id: "bus", label: "Bus" },
  { id: "car", label: "Car" },
  { id: "ship", label: "Ship" },
  { id: "walking", label: "Walking" },
  { id: "bicycle", label: "Bicycle" },
  { id: "motorcycle", label: "Motorcycle" },
  { id: "boat", label: "Boat" },
  { id: "jeep", label: "Jeep" },
  { id: "other", label: "Other" },
];

export const JOURNEY_DIFFICULTY_LEVELS = [
  { id: "easy", label: "Easy" },
  { id: "moderate", label: "Moderate" },
  { id: "challenging", label: "Challenging" },
  { id: "difficult", label: "Difficult" },
  { id: "extreme", label: "Extreme" },
];

export const JOURNEY_FILTERS = [
  { id: "featured", label: "Featured" },
  { id: "upcoming", label: "Upcoming" },
  { id: "popular", label: "Popular" },
  { id: "trending", label: "Trending" },
  { id: "new", label: "New" },
  { id: "budget", label: "Budget" },
  { id: "luxury", label: "Luxury" },
];

export const JOURNEY_SORT_OPTIONS = [
  { id: "startDate", label: "Date" },
  { id: "basePrice", label: "Price" },
  { id: "duration", label: "Duration" },
  { id: "averageRating", label: "Rating" },
];

export const JOURNEY_PARTICIPANT_STATUS = {
  PENDING: "pending",
  CONFIRMED: "confirmed",
  CANCELLED: "cancelled",
  WAITLISTED: "waitlisted",
  COMPLETED: "completed",
  NO_SHOW: "noShow",
  REJECTED: "rejected",
  APPROVAL_PENDING: "approvalPending",
};
