import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { onAuthStateChanged, User } from 'firebase/auth';
import { auth } from '../config/firebase';
import { useAppDispatch, useAppSelector } from '../reduxStore/hooks';
import { getUserProfile } from '../api/authService';
import { logoutUser } from '../reduxStore/userSlice';
import { isObjectEmpty } from '../lib/utils/commonUtils';
import AsyncStorage from '@react-native-async-storage/async-storage';

const ONBOARDING_COMPLETED_KEY = 'onboarding_completed';

export type AuthState = 'loading' | 'authenticated' | 'unauthenticated' | 'onboarding' | 'interests-incomplete';

interface AuthContextType {
  authState: AuthState;
  firebaseUser: User | null;
  isLoading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [authState, setAuthState] = useState<AuthState>('loading');
  const [firebaseUser, setFirebaseUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  const dispatch = useAppDispatch();
  const userState = useAppSelector((state) => state.user);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      console.log('Auth state changed:', user ? 'User logged in' : 'User logged out');
      setFirebaseUser(user);
      
      if (user) {
        try {
          // User is authenticated, get their profile
          const userProfile = await getUserProfile(dispatch);
          
          // Check if user has completed interests selection
          const hasHangoutInterests = !isObjectEmpty(userProfile?.profile?.hangoutInterests);
          const hasJourneyInterests = !isObjectEmpty(userProfile?.profile?.journeyInterests);
          
          if (hasHangoutInterests && hasJourneyInterests) {
            setAuthState('authenticated');
          } else {
            setAuthState('interests-incomplete');
          }
        } catch (error) {
          console.error('Error getting user profile:', error);
          // If there's an error getting profile, treat as unauthenticated
          dispatch(logoutUser());
          setAuthState('unauthenticated');
        }
      } else {
        // User is not authenticated
        dispatch(logoutUser());
        
        // Check if onboarding has been completed
        try {
          const onboardingCompleted = await AsyncStorage.getItem(ONBOARDING_COMPLETED_KEY);
          if (onboardingCompleted === 'true') {
            setAuthState('unauthenticated');
          } else {
            setAuthState('onboarding');
          }
        } catch (error) {
          console.error('Error checking onboarding status:', error);
          setAuthState('onboarding');
        }
      }
      
      setIsLoading(false);
    });

    return () => unsubscribe();
  }, [dispatch]);

  const value: AuthContextType = {
    authState,
    firebaseUser,
    isLoading,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Helper function to mark onboarding as completed
export const markOnboardingCompleted = async (): Promise<void> => {
  try {
    await AsyncStorage.setItem(ONBOARDING_COMPLETED_KEY, 'true');
  } catch (error) {
    console.error('Error saving onboarding status:', error);
  }
};
