import React, { createContext, useContext, useEffect, useState, ReactNode } from "react";
import { onAuthStateChanged, User } from "firebase/auth";
import { auth } from "../config/firebase";
import { useAppDispatch } from "../reduxStore/hooks";
import { getUserProfile } from "../api/authService";
import { logoutUser } from "../reduxStore/userSlice";
import { isObjectEmpty } from "../lib/utils/commonUtils";
import AsyncStorage from "@react-native-async-storage/async-storage";

const ONBOARDING_COMPLETED_KEY = "onboarding_completed";

export type AuthState =
  | "loading"
  | "authenticated"
  | "unauthenticated"
  | "onboarding"
  | "interests-incomplete"
  | "email-unverified";

interface AuthContextType {
  authState: AuthState;
  firebaseUser: User | null;
  isLoading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [authState, setAuthState] = useState<AuthState>("loading");
  const [firebaseUser, setFirebaseUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const dispatch = useAppDispatch();

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      console.log("Auth state changed:", user ? "User logged in" : "User logged out");
      setFirebaseUser(user);

      if (user) {
        try {
          console.log("Firebase user authenticated, checking email verification...");

          // Check if email is verified (skip for Google/Apple providers)
          const isEmailVerified =
            user.emailVerified ||
            user.providerData.some(
              (provider) => provider.providerId === "google.com" || provider.providerId === "apple.com"
            );

          if (!isEmailVerified) {
            console.log("Email not verified, showing verification screen");
            setAuthState("email-unverified");
            setIsLoading(false);
            return;
          }

          console.log("Email verified, fetching profile...");
          // User is authenticated and email verified, get their profile with retry logic for new users
          const userProfile = await getUserProfileWithRetry(dispatch);

          // Check if user has completed interests selection
          const hasHangoutInterests = !isObjectEmpty(userProfile?.profile?.hangoutInterests);
          const hasJourneyInterests = !isObjectEmpty(userProfile?.profile?.journeyInterests);

          if (hasHangoutInterests && hasJourneyInterests) {
            setAuthState("authenticated");
          } else {
            setAuthState("interests-incomplete");
          }
        } catch (error) {
          console.error("Error getting user profile:", error);
          // If there's an error getting profile, treat as unauthenticated
          dispatch(logoutUser());
          setAuthState("unauthenticated");
        }
      } else {
        // User is not authenticated
        dispatch(logoutUser());

        // Check if onboarding has been completed
        try {
          const onboardingCompleted = await AsyncStorage.getItem(ONBOARDING_COMPLETED_KEY);
          if (onboardingCompleted === "true") {
            setAuthState("unauthenticated");
          } else {
            setAuthState("onboarding");
          }
        } catch (error) {
          console.error("Error checking onboarding status:", error);
          setAuthState("onboarding");
        }
      }

      setIsLoading(false);
    });

    return () => unsubscribe();
  }, [dispatch]);

  const value: AuthContextType = {
    authState,
    firebaseUser,
    isLoading,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

// Helper function to get user profile with retry logic for new users
const getUserProfileWithRetry = async (dispatch: any, maxRetries = 5, delay = 1000): Promise<any> => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`Attempting to get user profile (attempt ${attempt}/${maxRetries})`);
      const userProfile = await getUserProfile(dispatch);
      console.log("Successfully retrieved user profile");
      return userProfile;
    } catch (error: any) {
      console.log(`Profile fetch attempt ${attempt} failed:`, error.response?.status || error.message);

      // If it's a 403 error (user not found) and we haven't reached max retries, wait and try again
      if (error.response?.status === 403 && attempt < maxRetries) {
        console.log(`User not found in backend yet, waiting ${delay}ms before retry...`);
        await new Promise((resolve) => setTimeout(resolve, delay));
        // Increase delay for next attempt (exponential backoff)
        delay = Math.min(delay * 1.5, 5000);
        continue;
      }

      // If it's not a 403 error or we've exhausted retries, throw the error
      throw error;
    }
  }
};

// Helper function to mark onboarding as completed
export const markOnboardingCompleted = async (): Promise<void> => {
  try {
    await AsyncStorage.setItem(ONBOARDING_COMPLETED_KEY, "true");
  } catch (error) {
    console.error("Error saving onboarding status:", error);
  }
};
