import * as AppleAuthentication from "expo-apple-authentication";
import { signInWithCredential, OAuthProvider, User } from "firebase/auth";
import { useState } from "react";
import { auth } from "../config/firebase";

import { useAppDispatch } from "../reduxStore/hooks";
import { registerUser } from "../api/authService";
import * as Crypto from "expo-crypto";

const useAppleAuth = () => {
  const [loading, setLoading] = useState(false);
  const dispatch = useAppDispatch();

  const handleAppleSignUp = async (user: User) => {
    // Extract name from Apple response or use empty strings
    // Apple only provides name on first login
    // TODO: handle edge case where user has no name
    const userData = {
      firstName: user.displayName?.split(" ")[0] || "apple",
      lastName: user.displayName?.split(" ")[1] || "user",
      email: user.email!,
      phone: "", // Apple doesn't provide phone number
      firebaseUID: user.uid,
      profilePic: user.photoURL || "",
      role: ["user"],
    };

    // Register user in the backend
    await registerUser(userData, dispatch);
    // Note: Redirection is now handled by AuthContext
  };

  // Note: Auth state management is now handled by AuthContext
  // This hook only handles the Apple sign-in process

  const signInWithApple = async () => {
    console.log("Signing in with Apple");
    setLoading(true);

    try {
      // Generate a random nonce
      const rawNonce = await Crypto.randomUUID();
      // Hash the nonce with SHA256
      const hashedNonce = await Crypto.digestStringAsync(Crypto.CryptoDigestAlgorithm.SHA256, rawNonce);

      const credential = await AppleAuthentication.signInAsync({
        requestedScopes: [
          AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
          AppleAuthentication.AppleAuthenticationScope.EMAIL,
        ],
        nonce: hashedNonce, // Pass the hashed nonce to Apple
      });

      // Create OAuthProvider for Apple
      const provider = new OAuthProvider("apple.com");

      // Create credential with the token and raw nonce
      const authCredential = provider.credential({
        idToken: credential.identityToken!,
        rawNonce, // Pass the raw nonce to Firebase
      });

      // Sign in with Firebase
      const userCredential = await signInWithCredential(auth, authCredential);
      console.log("Signed in with Apple!", userCredential);
      await handleAppleSignUp(userCredential.user);
    } catch (error) {
      console.error("Apple Sign-In Error", error);
    } finally {
      setLoading(false);
    }
  };

  return { signInWithApple, loading };
};

export default useAppleAuth;
