import { useState, useEffect } from "react";
import { <PERSON><PERSON>, Linking } from "react-native";
import * as Location from "expo-location";
import { useAppSelector, useAppDispatch } from "../reduxStore/hooks";
import { RootState } from "../reduxStore/store";
import { getCurrentLocation } from "../services/locationService";
import { updateUserProfile } from "../api/userService";
import { updateUser } from "../reduxStore/userSlice";
import AsyncStorage from "@react-native-async-storage/async-storage";

const LOCATION_PERMISSION_ASKED_KEY = "location_permission_asked";
const LOCATION_PERMISSION_DENIED_KEY = "location_permission_denied";

export type LocationPermissionState =
  | "checking"
  | "not-requested"
  | "requesting"
  | "granted"
  | "denied"
  | "denied-permanently";

export const useAutoLocation = () => {
  const [permissionState, setPermissionState] = useState<LocationPermissionState>("checking");
  const [isUpdatingLocation, setIsUpdatingLocation] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const user = useAppSelector((state: RootState) => state.user.user);
  const dispatch = useAppDispatch();

  // Check if user already has location
  const hasLocation = !!(user?.location?.coordinates && user.location.coordinates.length > 0);

  // Check permission status and handle auto-request
  useEffect(() => {
    checkAndRequestLocationPermission();
  }, []);

  const checkAndRequestLocationPermission = async () => {
    try {
      setError(null);

      // If user already has location, no need to request
      if (hasLocation) {
        setPermissionState("granted");
        return;
      }

      // Check current permission status
      const { status } = await Location.getForegroundPermissionsAsync();

      if (status === "granted") {
        setPermissionState("granted");
        // Auto-get location if permission is granted but no location stored
        await getCurrentLocationAndUpdate();
        return;
      }

      // Check if we've already asked for permission before
      const hasAskedBefore = await AsyncStorage.getItem(LOCATION_PERMISSION_ASKED_KEY);

      if (hasAskedBefore === "true") {
        // We've asked before, don't ask again - let user use app normally
        setPermissionState("denied-permanently");
        return;
      }

      // Auto-request permission for first-time users only
      await requestLocationPermission();
    } catch (error) {
      console.error("Error checking location permission:", error);
      setError("Failed to check location permission");
      setPermissionState("denied");
    }
  };

  const requestLocationPermission = async (): Promise<boolean> => {
    try {
      setPermissionState("requesting");
      setError(null);

      // Mark that we've asked for permission
      await AsyncStorage.setItem(LOCATION_PERMISSION_ASKED_KEY, "true");

      const { status } = await Location.requestForegroundPermissionsAsync();

      if (status === "granted") {
        setPermissionState("granted");
        await AsyncStorage.removeItem(LOCATION_PERMISSION_DENIED_KEY);
        // Auto-get location after permission granted
        await getCurrentLocationAndUpdate();
        return true;
      } else {
        // Permission denied
        await AsyncStorage.setItem(LOCATION_PERMISSION_DENIED_KEY, "true");
        setPermissionState("denied");
        return false;
      }
    } catch (error) {
      console.error("Error requesting location permission:", error);
      setError("Failed to request location permission");
      setPermissionState("denied");
      return false;
    }
  };

  const getCurrentLocationAndUpdate = async (): Promise<boolean> => {
    try {
      setIsUpdatingLocation(true);
      setError(null);

      const locationData = await getCurrentLocation();

      if (locationData) {
        // Update user profile in backend
        await updateUserProfile({ location: locationData });
        if (user) {
          const updatedUser = {
            ...user,
            location: {
              coordinates: [locationData.coordinates.longitude, locationData.coordinates.latitude],
              address: locationData.address,
              city: locationData.city,
              country: locationData.country,
              state: locationData.state,
              zipCode: locationData.zipCode,
              countryCode: locationData.countryCode,
            },
          };

          // Update Redux store
          dispatch(updateUser(updatedUser));
        }

        console.log("Location updated successfully:", locationData);
        return true;
      } else {
        setError("Could not get current location");
        return false;
      }
    } catch (error) {
      console.error("Error getting current location:", error);
      setError("Failed to get current location");
      return false;
    } finally {
      setIsUpdatingLocation(false);
    }
  };

  const showLocationSettings = () => {
    Alert.alert(
      "Location Permission Required",
      "To provide you with personalized content and nearby recommendations, we need access to your location. You can enable this in your device settings.",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Open Settings",
          onPress: () => Linking.openSettings(),
        },
      ]
    );
  };

  const retryLocationRequest = async () => {
    // Reset denied state and try again
    await AsyncStorage.removeItem(LOCATION_PERMISSION_DENIED_KEY);
    await checkAndRequestLocationPermission();
  };

  return {
    permissionState,
    isUpdatingLocation,
    error,
    hasLocation,
    requestLocationPermission,
    getCurrentLocationAndUpdate,
    showLocationSettings,
    retryLocationRequest,
  };
};
