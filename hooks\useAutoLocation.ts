import { useState, useEffect } from "react";
import { <PERSON><PERSON>, Linking } from "react-native";
import * as Location from "expo-location";
import { useAppSelector, useAppDispatch } from "../reduxStore/hooks";
import { RootState } from "../reduxStore/store";
import { getCurrentLocation } from "../services/locationService";
import { updateUserProfile } from "../api/userService";
import { updateUser } from "../reduxStore/userSlice";
import AsyncStorage from "@react-native-async-storage/async-storage";

const LOCATION_LAST_ASKED_DATE_KEY = "location_last_asked_date";

export type LocationPermissionState =
  | "checking"
  | "not-requested"
  | "requesting"
  | "granted"
  | "denied"
  | "denied-permanently";

export const useAutoLocation = () => {
  const [permissionState, setPermissionState] = useState<LocationPermissionState>("checking");
  const [isUpdatingLocation, setIsUpdatingLocation] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isRequestingPermission, setIsRequestingPermission] = useState(false);

  const user = useAppSelector((state: RootState) => state.user.user);
  const dispatch = useAppDispatch();

  // Check if user already has location
  const hasLocation = !!(user?.location?.coordinates && user.location.coordinates.length > 0);

  // Helper function to determine if we should request permission
  const shouldRequestLocationPermission = async (): Promise<boolean> => {
    try {
      const lastAskedDate = await AsyncStorage.getItem(LOCATION_LAST_ASKED_DATE_KEY);

      if (!lastAskedDate) {
        // Never asked before, should ask
        return true;
      }

      const lastAsked = new Date(lastAskedDate);
      const now = new Date();
      const daysDifference = Math.floor((now.getTime() - lastAsked.getTime()) / (1000 * 60 * 60 * 24));

      // Ask again if it's been at least 1 day
      return daysDifference >= 1;
    } catch (error) {
      console.error("Error checking last asked date:", error);
      return true; // Default to asking if there's an error
    }
  };

  // Check permission status and handle auto-request (only once)
  useEffect(() => {
    let isMounted = true;

    const initializeLocation = async () => {
      if (isMounted && !isRequestingPermission) {
        await checkAndRequestLocationPermission();
      }
    };

    initializeLocation();

    return () => {
      isMounted = false;
    };
  }, []); // Empty dependency array ensures this runs only once

  const checkAndRequestLocationPermission = async () => {
    try {
      setError(null);

      // Prevent multiple simultaneous requests
      if (isRequestingPermission) {
        console.log("Permission request already in progress, skipping");
        return;
      }

      // If user already has location, no need to request
      if (hasLocation) {
        setPermissionState("granted");
        return;
      }

      // Check current permission status
      const { status } = await Location.getForegroundPermissionsAsync();

      if (status === "granted") {
        setPermissionState("granted");
        // Auto-get location if permission is granted but no location stored
        await getCurrentLocationAndUpdate();
        return;
      }

      // Check if we should ask for permission
      const shouldAskForPermission = await shouldRequestLocationPermission();

      if (shouldAskForPermission) {
        // Request permission (native dialog only)
        setIsRequestingPermission(true);
        await requestLocationPermission();
        setIsRequestingPermission(false);
      } else {
        // Don't ask, user can use app normally
        setPermissionState("denied-permanently");
      }
    } catch (error) {
      console.error("Error checking location permission:", error);
      setError("Failed to check location permission");
      setPermissionState("denied");
      setIsRequestingPermission(false);
    }
  };

  const requestLocationPermission = async (): Promise<boolean> => {
    try {
      console.log("🔍 Requesting location permission...");
      setPermissionState("requesting");
      setError(null);

      // Mark when we asked for permission
      await AsyncStorage.setItem(LOCATION_LAST_ASKED_DATE_KEY, new Date().toISOString());

      const { status } = await Location.requestForegroundPermissionsAsync();
      console.log("📍 Location permission result:", status);

      if (status === "granted") {
        console.log("✅ Location permission granted");
        setPermissionState("granted");
        // Auto-get location after permission granted
        await getCurrentLocationAndUpdate();
        return true;
      } else {
        console.log("❌ Location permission denied - will ask again tomorrow");
        setPermissionState("denied");
        return false;
      }
    } catch (error) {
      console.error("Error requesting location permission:", error);
      setError("Failed to request location permission");
      setPermissionState("denied");
      return false;
    }
  };

  const getCurrentLocationAndUpdate = async (): Promise<boolean> => {
    try {
      setIsUpdatingLocation(true);
      setError(null);

      const locationData = await getCurrentLocation();

      if (locationData) {
        // Update user profile in backend
        await updateUserProfile({ location: locationData });
        if (user) {
          const updatedUser = {
            ...user,
            location: {
              coordinates: [locationData.coordinates.longitude, locationData.coordinates.latitude],
              address: locationData.address,
              city: locationData.city,
              country: locationData.country,
              state: locationData.state,
              zipCode: locationData.zipCode,
              countryCode: locationData.countryCode,
            },
          };

          // Update Redux store
          dispatch(updateUser(updatedUser));
        }

        console.log("Location updated successfully:", locationData);
        return true;
      } else {
        setError("Could not get current location");
        return false;
      }
    } catch (error) {
      console.error("Error getting current location:", error);
      setError("Failed to get current location");
      return false;
    } finally {
      setIsUpdatingLocation(false);
    }
  };

  const showLocationSettings = () => {
    Alert.alert(
      "Location Permission Required",
      "To provide you with personalized content and nearby recommendations, we need access to your location. You can enable this in your device settings.",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Open Settings",
          onPress: () => Linking.openSettings(),
        },
      ]
    );
  };

  const retryLocationRequest = async () => {
    // Reset last asked date and try again
    await AsyncStorage.removeItem(LOCATION_LAST_ASKED_DATE_KEY);
    await checkAndRequestLocationPermission();
  };

  return {
    permissionState,
    isUpdatingLocation,
    error,
    hasLocation,
    requestLocationPermission,
    getCurrentLocationAndUpdate,
    showLocationSettings,
    retryLocationRequest,
  };
};
