import * as <PERSON> from "expo-auth-session/providers/google";
import * as <PERSON><PERSON>rowser from "expo-web-browser";
import { signInWithCredential, GoogleAuthProvider, onAuthStateChanged, signOut, User } from "firebase/auth";
import { useEffect, useState } from "react";
import { auth } from "../config/firebase";
import * as AuthSession from "expo-auth-session";
import { useRouter } from "expo-router";
import { useAppDispatch, useAppSelector } from "../reduxStore/hooks";
import { logoutUser } from "../reduxStore/userSlice";
import { getUserProfile, registerUser } from "../api/authService";
import { isObjectEmpty } from "../lib/utils/commonUtils";

WebBrowser.maybeCompleteAuthSession(); // Required for web auth session

const redirectUri = AuthSession.makeRedirectUri({
  scheme: "com.logoutloud.app",
  path: "/google-signin",
});

const useGoogleAuth = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const userState = useAppSelector((state) => state.user);
  const dispatch = useAppDispatch();

  const [request, response, promptAsync] = Google.useAuthRequest({
    clientId: process.env.EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID,
    redirectUri,
    iosClientId: process.env.EXPO_PUBLIC_GOOGLE_IOS_CLIENT_ID,
    androidClientId: process.env.EXPO_PUBLIC_GOOGLE_ANDROID_CLIENT_ID,
  });
  useEffect(() => {
    const handleGoogleSignUp = async (user: User) => {
      const userData = {
        firstName: user.displayName?.split(" ")[0] || "",
        lastName: user.displayName?.split(" ")[1] || "",
        email: user.email!,
        phone: user.phoneNumber || "", // phone number is required
        firebaseUID: user.uid,
        profilePic: user.photoURL || "",
        role: ["user"],
      };

      // Register user in the backend
      await registerUser(userData, dispatch);
      router.replace("/home");
    };

    if (response?.type === "success") {
      const { id_token } = response.params!;
      const credential = GoogleAuthProvider.credential(id_token);
      signInWithCredential(auth, credential)
        .then((UserCredential) => {
          console.log("Signed in!", UserCredential);
          handleGoogleSignUp(UserCredential.user);
        })
        .catch((error) => console.error("Sign in error", error));
    }
  }, [response]);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setLoading(true);
      if (user && !userState.isLoading) {
        const userProfile = await getUserProfile(dispatch);
        if (
          !isObjectEmpty(userProfile?.profile?.hangoutInterests) &&
          !isObjectEmpty(userProfile?.profile?.journeyInterests)
        ) {
          router.replace("/home");
        } else {
          router.replace("/onboarding/hangout-interests");
        }
      } else {
        console.log("User is signed out");
        dispatch(logoutUser());
      }
      setLoading(false);
    });

    return () => unsubscribe();
  }, [userState.isLoading]);

  const signInWithGoogle = async () => {
    console.log("Signing in with Google");

    setLoading(true);
    try {
      await promptAsync();
    } catch (error) {
      console.error("Google Sign-In Error", error);
    } finally {
      setLoading(false);
    }
  };

  return { signInWithGoogle, loading };
};

export default useGoogleAuth;
