import * as <PERSON> from "expo-auth-session/providers/google";
import * as WebBrowser from "expo-web-browser";
import { signInWithCredential, GoogleAuthProvider, signOut, User } from "firebase/auth";
import { useEffect, useState } from "react";
import { auth } from "../config/firebase";
import * as AuthSession from "expo-auth-session";
import { useRouter } from "expo-router";
import { useAppDispatch } from "../reduxStore/hooks";
import { registerUser } from "../api/authService";

WebBrowser.maybeCompleteAuthSession(); // Required for web auth session

const redirectUri = AuthSession.makeRedirectUri({
  scheme: "com.logoutloud.app",
  path: "/google-signin",
});

const useGoogleAuth = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  const dispatch = useAppDispatch();

  const [request, response, promptAsync] = Google.useAuthRequest({
    clientId: process.env.EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID,
    redirectUri,
    iosClientId: process.env.EXPO_PUBLIC_GOOGLE_IOS_CLIENT_ID,
    androidClientId: process.env.EXPO_PUBLIC_GOOGLE_ANDROID_CLIENT_ID,
  });
  useEffect(() => {
    const handleGoogleSignUp = async (user: User) => {
      const userData = {
        firstName: user.displayName?.split(" ")[0] || "",
        lastName: user.displayName?.split(" ")[1] || "",
        email: user.email!,
        phone: user.phoneNumber || "", // phone number is required
        firebaseUID: user.uid,
        profilePic: user.photoURL || "",
        role: ["user"],
      };

      // Register user in the backend
      await registerUser(userData, dispatch);
      router.replace("/home");
    };

    if (response?.type === "success") {
      const { id_token } = response.params!;
      const credential = GoogleAuthProvider.credential(id_token);
      signInWithCredential(auth, credential)
        .then((UserCredential) => {
          console.log("Signed in!", UserCredential);
          handleGoogleSignUp(UserCredential.user);
        })
        .catch((error) => console.error("Sign in error", error));
    }
  }, [response]);

  // Note: Auth state management is now handled by AuthContext
  // This hook only handles the Google sign-in process

  const signInWithGoogle = async () => {
    console.log("Signing in with Google");

    setLoading(true);
    try {
      await promptAsync();
    } catch (error) {
      console.error("Google Sign-In Error", error);
    } finally {
      setLoading(false);
    }
  };

  return { signInWithGoogle, loading };
};

export default useGoogleAuth;
