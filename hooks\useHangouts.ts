import { useInfiniteQuery } from "@tanstack/react-query";
import { HangoutFilters } from "../app/types/hangout";
import { hangoutsService } from "../api/hangoutsService";

export const useHangouts = (filters: HangoutFilters = {}) => {
  return useInfiniteQuery({
    queryKey: ["hangouts", filters],
    queryFn: ({ pageParam = 1 }) => hangoutsService.getHangouts({ ...filters, page: pageParam }),
    initialPageParam: 1,
    getNextPageParam: (lastPage, allPages) => {
      // Check if we've reached the last page based on pagination info
      if (!lastPage.pagination || lastPage.pagination.page >= lastPage.pagination.pages) {
        return undefined;
      }
      // Return the next page number from the pagination info
      return lastPage.pagination.page + 1;
    },
    staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
  });
};
