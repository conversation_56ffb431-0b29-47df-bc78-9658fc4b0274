import { useMutation, useQueryClient } from "@tanstack/react-query";
import { hangoutsService } from "../api/hangoutsService";
import { JoinHangoutResponse } from "../app/types/hangout";

export const useJoinHangout = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (hangoutId: string) => hangoutsService.joinHangout(hangoutId),
    onSuccess: (data: JoinHangoutResponse, variables) => {
      // Invalidate the hangout details query to refetch with updated data
      queryClient.invalidateQueries({ queryKey: ["hangout", variables] });
    },
  });
};
