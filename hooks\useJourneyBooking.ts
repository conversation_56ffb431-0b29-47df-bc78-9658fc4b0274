import { useState } from "react";
import { Alert, Platform } from "react-native";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { journeysService } from "../api/journeysService";
import { showToast } from "../lib/utils/showToast";

export const useJourneyBooking = () => {
  const [loading, setLoading] = useState(false);
  const queryClient = useQueryClient();

  const joinJourneyMutation = useMutation({
    mutationFn: (journeyId: string) => journeysService.bookJourney(journeyId),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ["journey-details", variables] });
    },
  });

  const initiateBooking = async (journeyId: string) => {
    setLoading(true);
    try {
      const response = await joinJourneyMutation.mutateAsync(journeyId);
      return response;
    } catch (error) {
      console.error("Error joining journey:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to join journey. Please try again.";
      
      if (Platform.OS === "ios") {
        showToast("error", errorMessage);
      } else {
        Alert.alert("Booking Failed", errorMessage);
      }
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return {
    initiateBooking,
    loading: loading || joinJourneyMutation.isPending,
  };
};