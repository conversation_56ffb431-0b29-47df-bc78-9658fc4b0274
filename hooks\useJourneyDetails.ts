import { useQuery } from "@tanstack/react-query";
import { journeysService } from "../api/journeysService";

export const useJourneyDetails = (journeyId: string) => {
  const query = useQuery({
    queryKey: ["journey", journeyId],
    queryFn: () => journeysService.getJourneyById(journeyId),
    // staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
  });

  return {
    data: query.data?.data,
    userBookingStatus: query.data?.userBookingStatus,
    participantCount: query.data?.participantCount,
    isLoading: query.isLoading,
    error: query.error,
    refetch: query.refetch,
  };
};
