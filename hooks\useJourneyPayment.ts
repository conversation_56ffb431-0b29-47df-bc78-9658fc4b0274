import { useState } from "react";
import { useAppSelector } from "../reduxStore/hooks";
import { RootState } from "../reduxStore/store";
import PayUService from "../services/payuService";
import { paymentService } from "../api/paymentService";
import { journeysService } from "../api/journeysService";

export const useJourneyPayment = () => {
  const [loading, setLoading] = useState(false);
  const [promoCodeValidation, setPromoCodeValidation] = useState<{
    valid: boolean;
    discountPercentage: number;
    message: string;
  } | null>(null);
  const { user } = useAppSelector((state: RootState) => state.user);
  const [upiPaymentLoading, setUpiPaymentLoading] = useState(false);
  const [upiPaymentError, setUpiPaymentError] = useState<string | null>(null);

  const clearPromoValidation = () => {
    setPromoCodeValidation(null);
  };

  const validatePromoCode = async (journeyId: string, promoCode: string) => {
    setLoading(true);
    try {
      const response = await paymentService.validatePromoCode(journeyId, promoCode);

      console.log("Promo code validation response:", response);

      // Check if the response has a data property
      const validationResult = response.data || response;

      if (validationResult.valid) {
        setPromoCodeValidation({
          valid: true,
          discountPercentage: validationResult.discountPercentage,
          message: validationResult.message || "Promo code applied successfully!",
        });
      } else {
        setPromoCodeValidation({
          valid: false,
          discountPercentage: 0,
          message: validationResult.message || "Invalid promo code",
        });
      }

      return validationResult;
    } catch (error) {
      console.error("Error validating promo code:", error);
      setPromoCodeValidation({
        valid: false,
        discountPercentage: 0,
        message: "Failed to validate promo code",
      });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const initiatePayment = async (
    journeyId: string,
    journeyTitle: string,
    pricingTierId: string,
    promoCode?: string
  ) => {
    setLoading(true);
    try {
      // Step 1: Create payment order
      console.log(
        "Creating payment order for journey:",
        journeyId,
        "with tier:",
        pricingTierId,
        "and promo:",
        promoCode
      );
      const orderData = await paymentService.createJourneyPaymentOrder(journeyId, pricingTierId, promoCode, user);

      // Handle both response structures - direct data or wrapped in data property
      const responseData = orderData?.data || orderData;

      if (!responseData?.orderId || !responseData?.key) {
        throw new Error("Invalid order data received: missing required fields");
      }

      // Use PayU service
      const paymentResponse = await PayUService.initiatePayment({ payuData: responseData.payuData });

      // Step 3: Verify payment
      if (paymentResponse.result === "success") {
        const verificationData = await paymentService.verifyJourneyPayment({
          paymentId: responseData.paymentId,
          orderId: responseData.orderId,
          signature: "",
          paymentDetails: {
            status: "success",
            message: "Payment successful",
          },
        });

        if (!verificationData.success) {
          throw new Error(verificationData.message || "Payment verification failed");
        }

        return verificationData;
      } else {
        throw new Error("Payment failed");
      }
    } catch (error) {
      console.error("Payment error:", error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const checkPaymentStatus = async (journeyId: string) => {
    try {
      const response = await paymentService.getJourneyPaymentStatus(journeyId);
      return response.data;
    } catch (error) {
      console.error("Error checking payment status:", error);
      throw error;
    }
  };

  const submitUpiPayment = async (
    journeyId: string,
    upiPaymentScreenshotUrl: string,
    pricingTier: string,
    promoCode?: string
  ) => {
    setUpiPaymentLoading(true);
    setUpiPaymentError(null);
    try {
      const response = await journeysService.submitJourneyUpiPayment(
        journeyId,
        upiPaymentScreenshotUrl,
        pricingTier,
        promoCode
      );
      return response;
    } catch (error: any) {
      setUpiPaymentError(error?.response?.data?.error || error.message || "Failed to submit UPI payment");
      throw error;
    } finally {
      setUpiPaymentLoading(false);
    }
  };

  return {
    initiatePayment,
    checkPaymentStatus,
    validatePromoCode,
    promoCodeValidation,
    clearPromoValidation,
    loading,
    submitUpiPayment,
    upiPaymentLoading,
    upiPaymentError,
  };
};
