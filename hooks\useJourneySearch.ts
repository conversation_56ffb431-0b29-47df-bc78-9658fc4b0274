import { useInfiniteQuery } from "@tanstack/react-query";
import { journeysService } from "../api/journeysService";

export interface JourneySearchFilters {
  // Search & Location
  search?: string;
  destination?: string;
  destinationCountry?: string;
  destinationCity?: string;

  // Dates & Duration
  startDate?: string;
  endDate?: string;
  maxDuration?: number;

  // Categories & Tags
  categories?: string | string[];
  tags?: string | string[];

  // Travel Details
  travelMode?: string | string[];

  // Price
  minBudget?: number;
  maxBudget?: number;

  // Difficulty
  difficultyLevel?: string;

  // Ratings & Organizer
  minRating?: number;
  organizerId?: string;

  // Pagination & Sorting
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";

  // Quick filters
  filter?: string;
}

export const useJourneySearch = (filters: JourneySearchFilters = {}) => {
  const baseFilters = {
    page: filters.page || 1,
    limit: filters.limit || 10,
    ...filters,
  };

  const query = useInfiniteQuery({
    queryKey: ["journey-search", filters],
    queryFn: ({ pageParam = 1 }) => journeysService.searchJourneys({ ...baseFilters, page: pageParam }),
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      if (!lastPage.pagination) return undefined;
      const { page, pages } = lastPage.pagination;
      return page < pages ? page + 1 : undefined;
    },
    staleTime: 2 * 60 * 1000, // Consider data fresh for 2 minutes
  });

  return {
    data: query.data?.pages.flatMap((page) => page.data) || [],
    pagination: query.data?.pages.length ? query.data.pages[query.data.pages.length - 1].pagination : null,
    isLoading: query.isLoading,
    error: query.error,
    refetch: query.refetch,
    fetchNextPage: query.fetchNextPage,
    hasNextPage: query.hasNextPage,
    isFetchingNextPage: query.isFetchingNextPage,
  };
};
