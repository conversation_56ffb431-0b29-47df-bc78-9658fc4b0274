import { useInfiniteQuery, useQuery } from "@tanstack/react-query";
import { journeysService } from "../api/journeysService";

export const useJourneys = () => {
  const featuredQuery = useQuery({
    queryKey: ["featured-journeys"],
    queryFn: () => journeysService.getFeaturedJourneys(5),
    staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
  });

  const upcomingQuery = useInfiniteQuery({
    queryKey: ["upcoming-journeys"],
    queryFn: ({ pageParam = 1 }) => journeysService.getUpcomingJourneys({ page: pageParam, limit: 5 }),
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      if (!lastPage.pagination) return undefined;
      const { page, pages } = lastPage.pagination;
      return page < pages ? page + 1 : undefined;
    },
    staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
  });

  return {
    featured: {
      data: featuredQuery.data?.data || [],
      isLoading: featuredQuery.isLoading,
      error: featuredQuery.error,
      refetch: featuredQuery.refetch,
    },
    upcoming: {
      data: upcomingQuery.data?.pages.flatMap((page) => page.data) || [],
      isLoading: upcomingQuery.isLoading,
      error: upcomingQuery.error,
      hasNextPage: upcomingQuery.hasNextPage,
      fetchNextPage: upcomingQuery.fetchNextPage,
      isFetchingNextPage: upcomingQuery.isFetchingNextPage,
      refetch: upcomingQuery.refetch,
    },
  };
};
