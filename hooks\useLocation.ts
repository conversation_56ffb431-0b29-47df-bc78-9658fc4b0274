import { useState } from "react";
import { Alert } from "react-native";
import { getCurrentLocation, requestLocationPermission, UserLocation } from "../services/locationService";
import { updateUserProfile } from "../api/userService";

export const useLocation = () => {
  const [location, setLocation] = useState<UserLocation | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Request location permission from the user
   */
  const requestPermission = async (): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      const granted = await requestLocationPermission();

      if (!granted) {
        Alert.alert(
          "Location Permission Required",
          "This feature requires location access. Please enable location permissions in your device settings.",
          [{ text: "OK" }]
        );
      }

      return granted;
    } catch (err) {
      setError("Failed to request location permission");
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Get the user's current location
   */
  const getLocation = async (): Promise<UserLocation | null> => {
    setIsLoading(true);
    setError(null);

    try {
      const locationData = await getCurrentLocation();

      if (locationData) {
        setLocation(locationData);
      } else {
        setError("Could not get location");
      }

      updateUserProfile({
        location: locationData,
      });

      return locationData;
    } catch (err) {
      setError("Failed to get location");
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Update the user's profile with their current location
   */
  const updateLocation = async (): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      // First get the current location
      const locationData = await getLocation();

      if (!locationData) {
        return false;
      }

      // Then update the user profile with the location
      await updateUserProfile({
        location: locationData,
      });
      return true;
    } catch (err) {
      setError("Failed to update location");
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    location,
    isLoading,
    error,
    requestPermission,
    getLocation,
    updateLocation,
  };
};
