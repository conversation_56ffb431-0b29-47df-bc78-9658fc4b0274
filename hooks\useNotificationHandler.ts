import { useEffect, useRef, useState } from 'react';
import * as Notifications from 'expo-notifications';
import { useRouter } from 'expo-router';
import { initializeNotifications } from '../services/firebaseMessagingService';
import { useQueryClient } from '@tanstack/react-query';

export const useNotificationHandler = () => {
  const [notification, setNotification] = useState<Notifications.Notification | null>(null);
  const notificationListener = useRef<{ removeListeners: () => void } | null>(null);
  const router = useRouter();
  const queryClient = useQueryClient();

  useEffect(() => {
    // Initialize notification handlers
    notificationListener.current = initializeNotifications(
      // <PERSON>le received notification
      (notification) => {
        setNotification(notification);
        // Invalidate notification queries to refresh counts
        queryClient.invalidateQueries({ queryKey: ['notifications'] });
        queryClient.invalidateQueries({ queryKey: ['notification-count'] });
      },
      // Handle notification response (when user taps notification)
      (response) => {
        const data = response.notification.request.content.data;
        
        // Handle navigation based on notification type
        if (data.actionUrl) {
          router.push(data.actionUrl as string);
        } else if (data.type === 'message') {
          router.push('/(tabs)/messages');
        } else if (data.type === 'hangout_invite') {
          router.push('/(tabs)/hangouts');
        } else if (data.type === 'friend_request') {
          router.push('/(tabs)/profile');
        } else {
          // Default to notifications screen
          router.push('/(notifications)/notifications');
        }
        
        // Invalidate notification queries to refresh counts
        queryClient.invalidateQueries({ queryKey: ['notifications'] });
        queryClient.invalidateQueries({ queryKey: ['notification-count'] });
      }
    );

    // Cleanup listeners on unmount
    return () => {
      if (notificationListener.current) {
        notificationListener.current.removeListeners();
      }
    };
  }, [router, queryClient]);

  return {
    lastNotification: notification
  };
};