import { useState, useEffect, useCallback } from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { requestNotificationPermission, setupPushNotifications } from "../services/firebaseMessagingService";
import { useAppSelector, useAppDispatch } from "../reduxStore/hooks";
import { RootState } from "../reduxStore/store";
import { updateUser } from "../reduxStore/userSlice";
import { updateNotificationPreferencesInBackend } from "../api/userService";

// Storage keys
const NOTIFICATION_LAST_ASKED_KEY = "notification_last_asked_date";
const NOTIFICATION_ASKED_COUNT_KEY = "notification_asked_count";
const TWO_DAYS_IN_MS = 2 * 24 * 60 * 60 * 1000;
const MAX_ASK_COUNT = 3; // Maximum number of times to ask before giving up

export const useNotificationPermission = () => {
  // State
  const [permissionStatus, setPermissionStatus] = useState<"granted" | "denied" | "undetermined">("undetermined");
  const [isLoading, setIsLoading] = useState(false);
  const [shouldPrompt, setShouldPrompt] = useState(false);

  // Redux
  const { user } = useAppSelector((state: RootState) => state.user);
  const dispatch = useAppDispatch();

  // Get notification preferences
  const notificationPrefs = user?.profile?.preferences?.notifications || {
    enabled: true,
    pushNotifications: true,
    emailNotifications: true,
  };

  /**
   * Check if the user should be prompted for notification permissions
   */
  const checkIfShouldPrompt = useCallback(async () => {
    try {
      // Get current permission status
      const granted = await requestNotificationPermission(true); // Just check, don't request
      setPermissionStatus(granted ? "granted" : "denied");

      // If already granted, no need to prompt
      if (granted) {
        setShouldPrompt(false);
        return;
      }

      // Get last asked date and count
      const lastAskedStr = await AsyncStorage.getItem(NOTIFICATION_LAST_ASKED_KEY);
      const askedCountStr = await AsyncStorage.getItem(NOTIFICATION_ASKED_COUNT_KEY);
      const askedCount = askedCountStr ? parseInt(askedCountStr, 10) : 0;

      // If we've asked too many times, don't prompt again
      if (askedCount >= MAX_ASK_COUNT) {
        setShouldPrompt(false);
        return;
      }

      // For new users (never asked before), prompt immediately
      if (!lastAskedStr) {
        setShouldPrompt(true);
        return;
      }

      // For existing users who denied, check if enough time has passed
      const lastAsked = new Date(lastAskedStr).getTime();
      const now = new Date().getTime();
      setShouldPrompt(now - lastAsked > TWO_DAYS_IN_MS);
    } catch (error) {
      console.error("Error checking notification prompt status:", error);
      setShouldPrompt(false);
    }
  }, []);

  /**
   * Request notification permission from the user
   */
  const requestPermission = useCallback(async () => {
    setIsLoading(true);

    try {
      // Get current ask count
      const askedCountStr = await AsyncStorage.getItem(NOTIFICATION_ASKED_COUNT_KEY);
      const askedCount = askedCountStr ? parseInt(askedCountStr, 10) : 0;

      // Update last asked time and count
      await AsyncStorage.setItem(NOTIFICATION_LAST_ASKED_KEY, new Date().toISOString());
      await AsyncStorage.setItem(NOTIFICATION_ASKED_COUNT_KEY, (askedCount + 1).toString());

      // Don't prompt again right away
      setShouldPrompt(false);

      // Request permission
      const granted = await requestNotificationPermission();
      setPermissionStatus(granted ? "granted" : "denied");

      // Update preferences in backend
      if (user) {
        // Update backend
        await updateNotificationPreferencesInBackend(notificationPrefs, granted);

        // Update local state
        dispatch(
          updateUser({
            ...user,
            profile: {
              ...user.profile,
              preferences: {
                ...(user.profile?.preferences || {}),
                notifications: {
                  ...(user.profile?.preferences?.notifications || {}),
                  pushNotifications: granted,
                },
              },
            },
          })
        );

        // Setup push notifications if granted
        if (granted) {
          await setupPushNotifications(user?.fcmTokens);
        }
      }

      return granted;
    } catch (error) {
      console.error("Error requesting notification permission:", error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [dispatch, notificationPrefs, user]);

  // Initialize on mount
  useEffect(() => {
    checkIfShouldPrompt();
  }, [checkIfShouldPrompt]);

  // Setup FCM when user is available and permission is granted
  useEffect(() => {
    if (user && permissionStatus === "granted" && notificationPrefs.pushNotifications) {
      setupPushNotifications(user?.fcmTokens);
    }
  }, [user, permissionStatus, notificationPrefs.pushNotifications]);

  return {
    permissionStatus,
    isLoading,
    shouldPrompt,
    requestPermission,
  };
};
