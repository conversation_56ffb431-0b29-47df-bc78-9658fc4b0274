import { useState } from "react";
import { paymentService } from "../api/paymentService";
import PayUService from "../services/payuService";
import { useAppSelector } from "../reduxStore/hooks";

export const usePayment = () => {
  const [loading, setLoading] = useState(false);
  const { user } = useAppSelector((state) => state.user);

  const verifyPayment = async (paymentId: string, orderId: string) => {
    try {
      const verificationData = await paymentService.verifyPayment({
        paymentId,
        orderId,
        signature: "",
        paymentDetails: {
          status: "success",
          message: "Payment successful",
        },
      });

      if (!verificationData.success) {
        throw new Error(verificationData.message || "Payment verification failed");
      }

      return verificationData.data;
    } catch (error) {
      console.error("Payment verification error:", error);
      throw error;
    }
  };

  const initiatePayment = async (hangoutId: string, hangoutTitle: string) => {
    setLoading(true);
    try {
      // Step 1: Create payment order
      const orderData = await paymentService.createPaymentOrder(hangoutId, undefined, user);

      // Handle both response structures - direct data or wrapped in data property
      const responseData = orderData?.data || orderData;

      if (!responseData?.orderId || !responseData?.key) {
        throw new Error("Invalid order data received: missing required fields");
      }

      console.log(orderData, "orderData");

      // Use our PayU service
      const paymentResponse = await PayUService.initiatePayment({ payuData: responseData.payuData });

      // Step 3: Verify payment with backend
      if (paymentResponse.result === "success") {
        await verifyPayment(responseData.paymentId, responseData.orderId);
      }

      return {
        success: paymentResponse.result === "success",
        result: paymentResponse.result,
        orderId: responseData.orderId,
        paymentResponse,
      };
    } catch (error) {
      console.error("Payment error:", error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const checkPaymentStatus = async (hangoutId: string) => {
    try {
      if (!hangoutId) {
        throw new Error("Hangout ID is required");
      }
      const response = await paymentService.getPaymentStatus(hangoutId);
      return response.data;
    } catch (error) {
      console.error("Error checking payment status:", error);
      throw error;
    }
  };

  const checkJourneyPaymentStatus = async (journeyId: string) => {
    try {
      if (!journeyId) {
        throw new Error("Journey ID is required");
      }
      const response = await paymentService.getJourneyPaymentStatus(journeyId);
      return response.data;
    } catch (error) {
      console.error("Error checking payment status:", error);
      throw error;
    }
  };

  // Cleanup function to remove callbacks
  const cleanup = () => {
    // PayU service handles its own cleanup
  };

  return {
    initiatePayment,
    checkPaymentStatus,
    checkJourneyPaymentStatus,
    loading,
    cleanup,
  };
};
