export interface User {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  firebaseUID: string;
  profilePic?: string;
  location?: {
    coordinates: number[];
    address: string;
    city: string;
    country: string;
    state: string;
    zipCode: string;
    countryCode: string;
  };
  role?: string[];
  isOrganizer?: boolean;
  profile?: {
    languages?: string[];
    coverPic?: string;
    bio?: string;
    dob?: string;
    gender?: string;
    hangoutInterests?: Record<string, string[]>;
    journeyInterests?: Record<string, string[]>;
    stats?: {
      totalHangoutsAttended: number;
      totalJourneysAttended: number;
      totalPoints: number;
      level: number;
      experience: number;
      experienceToNextLevel: number;
    };
    badges?: any[];
    achievements?: any[];
    preferences?: {
      theme?: string;
      notifications?: Record<string, boolean>;
      showBadges?: boolean;
      [key: string]: any;
    };
    [key: string]: any;
  };
  fcmTokens?: string[];
}

export interface UserState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}
