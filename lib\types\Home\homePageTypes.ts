export interface HomeSectionItem {
  id: string;
  component: JSX.Element;
}

// Define types for the AdCarousel props
export interface AdItem {
  src: any; // 'any' because it's a require statement for images
}

export interface AdCarouselProps {
  data: AdItem[]; // Array of items passed to the carousel
}

// Define props for CarouselItem component
export interface CarouselItemProps {
  item: AdItem;
}

// Define the props types for the HeadingWithCTA component
export interface HeadingWithCTAProps {
  heading: string;
  cta_text: string;
  cta_link: string;
}

export interface TrendingRoom {
  id: string;
  tripName: string;
  destination: string;
  startDate: string;
  endDate: string;
  numberOfDays: number;
  spotsLeft: number;
  totalSpots: number;
  budgetPerPerson: number;
  organizer: {
    name: string;
    avatarUrl: string;
  };
  tripType: string;
  coverImageUrl: string;
  isTrending: boolean;
}

export interface TrendingRoomCardProps {
  item: TrendingRoom;
}
