import { AntDesign } from "@expo/vector-icons";
import { Notification as ApiNotification } from "../../api/notificationService";

export interface NotificationProps {
  icon?: keyof typeof AntDesign.glyphMap;
  title: string;
  description: string;
  isRead?: boolean;
  createdAt?: string;
  onPress?: () => void;
  apiData?: ApiNotification;
}

export interface NotificationIconProps {
  iconName: string;
  notificationCount?: number;
  iconColor?: string;
  notificationColor: string;
  size?: number;
  showDot?: boolean;
  darkBackground?: boolean;
}
