import { GestureResponderEvent } from "react-native";

export type NavigationProp = {
  navigate: (route: string, params?: Record<string, unknown>) => void;
  reset: (state: { index: number; routes: Array<{ name: string; params?: Record<string, unknown> }> }) => void;
};

export interface PrimaryButtonProps {
  buttonText: string;
  onPressHandler: (event: GestureResponderEvent) => void;
  disabled?: boolean;
  bgColor?: string;
  icon?: any;
  borderRadius?: string;
  height?: number;
  width?: number;
}

export interface PageHeaderProps {
  title?: string; // title is optional
  color?: string; // color is optional, default will be "white"
  showIcon?: boolean; // showIcon is optional, default will be true
}

export interface HorizontalLineProps {
  text: string;
  textColor?: string;
  linebg?: string;
}

export interface ExpandableTextProps {
  content: string;
  maxLines?: number; // Optional prop, defaults to 3
  textSize?: number; // Optional prop, defaults to 14
}

export interface RatingsProps {
  rating: number; // Rating number (e.g., 4 for 4 stars)
  size?: number; // Size of the stars (optional)
  ratingText?: string; // Text to display after the stars (optional)
  ratingTextColor?: string; // Color for the rating text (optional)
}

// Define types for the avatar data and props
export interface Avatar {
  uri?: string;
  user?: {
    _id: string;
    profilePic: string;
  };
}
