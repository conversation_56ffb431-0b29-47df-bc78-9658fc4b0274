export const truncate = (string: string, length: number, end = "...") => {
  return string.length < length ? string : string.substring(0, length) + end;
};

export const toTitleCase = (str: string) => {
  return str.replace(/\b\w/g, (char) => char.toUpperCase());
};

export const formatAddress = (address?: string) => {
  if (!address) return "Unknown location";
  const parts = address.split(",").map((part) => part.trim());
  const locality = parts.find((part) => part.includes("Layout") || part.includes("Palya") || part.includes("Colony"));
  if (locality) {
    return locality;
  }
  return address;
};

export const isObjectEmpty = (obj: Record<string, any>) => {
  return Object.keys(obj).length === 0;
};

export const formatNotificationTime = (dateInput: Date | string): string => {
  try {
    // Handle string dates (ISO format)
    const date = typeof dateInput === "string" ? new Date(dateInput) : dateInput;

    // Check if date is valid
    if (isNaN(date.getTime())) {
      return "";
    }

    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    // Just now (less than 30 seconds)
    if (diffInSeconds < 30) {
      return "Just now";
    }

    // Seconds
    if (diffInSeconds < 60) {
      return `${diffInSeconds}s ago`;
    }

    // Minutes
    const diffInMinutes = Math.floor(diffInSeconds / 60);
    if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`;
    }

    // Hours
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    }

    // Days
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) {
      return `${diffInDays}d ago`;
    }

    // Format date for older notifications
    return formatDate(date);
  } catch (error) {
    console.error("Error formatting date:", error);
    return "";
  }
};

export const formatDate = (dateInput: Date | string): string => {
  try {
    const date = typeof dateInput === "string" ? new Date(dateInput) : dateInput;

    // Check if date is valid
    if (isNaN(date.getTime())) {
      return "";
    }

    const now = new Date();
    const day = date.getDate();
    const month = date.toLocaleString("default", { month: "short" });
    const year = date.getFullYear();
    const currentYear = now.getFullYear();

    // If same year, just show month and day
    if (year === currentYear) {
      return `${month} ${day}`;
    }

    // Otherwise show month, day and year
    return `${month} ${day}, ${year}`;
  } catch (error) {
    console.error("Error formatting date:", error);
    return "";
  }
};

export const formatTime = (dateInput: Date | string): string => {
  try {
    const date = typeof dateInput === "string" ? new Date(dateInput) : dateInput;

    // Check if date is valid
    if (isNaN(date.getTime())) {
      return "";
    }

    return date.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    });
  } catch (error) {
    console.error("Error formatting time:", error);
    return "";
  }
};

export const extractInstagramUsername = (url: string) => {
  const parts = url.split("/");
  return parts[parts.length - 1];
};
