import { User } from "../types/Auth/userTypes";

/**
 * Validates if a user profile is complete with all required fields
 * @param user The user object to validate
 * @returns An object with validation result and missing fields
 */
export const validateUserProfile = (
  user: User | null
): {
  isComplete: boolean;
  missingFields: string[];
} => {
  if (!user) return { isComplete: false, missingFields: ["User data"] };

  const missing: string[] = [];

  // Check required fields
  if (!user.firstName || user.firstName.length < 2) missing.push("First Name");
  if (!user.lastName || user.lastName.length < 1) missing.push("Last Name");
  if (!user.phone || !/^(\+\d{1,3}[- ]?)?\d{10}$/.test(user.phone)) missing.push("Phone Number");
  if (!user.profilePic) missing.push("Profile Picture");

  // Check profile fields
  if (!user.profile?.bio || user.profile.bio.length < 10) missing.push("Bio");
  if (!user.profile?.dob) missing.push("Date of Birth");
  if (!user.profile?.gender) missing.push("Gender");
  if (!user.profile?.languages || user.profile.languages.length === 0) missing.push("Languages");

  return {
    isComplete: missing.length === 0,
    missingFields: missing,
  };
};
