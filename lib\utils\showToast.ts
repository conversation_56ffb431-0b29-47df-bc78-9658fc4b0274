import { ToastType } from "../../components/Common/Toast";

// This is a global reference that will be set by the ToastProvider
let globalShowToast: ((type: ToastType, message: string, duration?: number) => void) | null = null;

export const setToastRef = (showToastFn: (type: ToastType, message: string, duration?: number) => void) => {
  globalShowToast = showToastFn;
};

export const showToast = (type: ToastType, message: string, duration?: number) => {
  if (globalShowToast) {
    globalShowToast(type, message, duration);
  } else {
    // Fallback to Alert if Toast component is not yet initialized
    console.warn("Toast not initialized, falling back to Alert");
    import("react-native").then(({ Alert }) => {
      Alert.alert(
        type === "success" ? "Success" : type === "error" ? "Error" : type === "warning" ? "Warning" : "Info",
        message,
        [{ text: "OK" }]
      );
    });
  }
};
