import { z } from "zod";

// User profile validation schema
export const userProfileSchema = z.object({
  firstName: z
    .string()
    .min(2, "First name must be at least 2 characters")
    .max(50, "First name cannot exceed 50 characters"),
  lastName: z
    .string()
    .min(1, "Last name must be at least 1 character")
    .max(50, "Last name cannot exceed 50 characters"),
  phone: z
    .string()
    .min(10, "Phone number must be at least 10 digits")
    .refine((phone) => !phone || /^(\+\d{1,3}[- ]?)?\d{10}$/.test(phone), {
      message: "Please enter a valid phone number",
    }),
  profilePic: z.string().url("Profile picture is required.").optional(),
  profile: z.object({
    bio: z.string().min(10, "Bio must be at least 10 characters").max(500, "Bio cannot exceed 500 characters"),
    dob: z.string().refine(
      (dob) => {
        const birthDate = new Date(dob);
        const today = new Date();
        const age = today.getFullYear() - birthDate.getFullYear();
        const m = today.getMonth() - birthDate.getMonth();
        const isOldEnough = age > 13 || (age === 13 && m >= 0 && today.getDate() >= birthDate.getDate());
        return isOldEnough;
      },
      {
        message: "You must be at least 13 years old",
      }
    ),
    gender: z.enum(["male", "female", "other", "prefer not to say"]),
    languages: z.array(z.string()).min(1, "Please select at least one language"),
    coverPic: z.string().url("Cover picture is required.").optional(),
    preferences: z
      .object({
        showBadges: z.boolean(),
      })
      .optional(),
  }),
});

// Function to validate user profile data
export const validateUserProfile = (data: any) => {
  try {
    userProfileSchema.parse(data);
    return { success: true, errors: null };
  } catch (error) {
    if (error instanceof z.ZodError) {
      // Convert Zod errors to a more usable format
      const formattedErrors = error.errors.reduce((acc, curr) => {
        const path = curr.path.join(".");
        acc[path] = curr.message;
        return acc;
      }, {} as Record<string, string>);

      return { success: false, errors: formattedErrors };
    }
    return { success: false, errors: { general: "Validation failed" } };
  }
};
