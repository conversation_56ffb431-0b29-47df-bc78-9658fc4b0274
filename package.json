{"name": "logoutloud", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~4.0.0", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/datetimepicker": "8.2.0", "@react-native-picker/picker": "2.9.0", "@reduxjs/toolkit": "^2.5.1", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.67.1", "@types/jest": "^29.5.14", "@types/react-test-renderer": "^19.0.0", "axios": "^1.7.9", "date-fns": "^4.1.0", "expo": "~52.0.47", "expo-apple-authentication": "~7.1.3", "expo-application": "~6.0.2", "expo-auth-session": "~6.0.3", "expo-av": "~15.0.2", "expo-blur": "^14.0.3", "expo-camera": "~16.0.18", "expo-constants": "~17.0.3", "expo-crypto": "~14.0.2", "expo-dev-client": "~5.0.15", "expo-device": "~7.0.3", "expo-file-system": "^18.0.11", "expo-font": "~13.0.4", "expo-image-picker": "~16.0.6", "expo-linear-gradient": "~14.0.1", "expo-linking": "~7.0.3", "expo-location": "~18.0.8", "expo-media-library": "^17.0.6", "expo-notifications": "~0.29.14", "expo-router": "~4.0.21", "expo-sharing": "^13.0.1", "expo-splash-screen": "^0.29.22", "expo-status-bar": "~2.0.0", "expo-system-ui": "~4.0.8", "expo-updates": "~0.27.4", "expo-web-browser": "~14.0.2", "firebase": "^11.3.1", "nativewind": "^2.0.11", "payu-non-seam-less-react": "^3.3.2", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "^0.76.7", "react-native-gesture-handler": "~2.20.2", "react-native-get-random-values": "^1.11.0", "react-native-google-places-autocomplete": "^2.5.7", "react-native-maps": "1.18.0", "react-native-razorpay": "^2.3.0", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-url-polyfill": "^2.0.0", "react-native-view-shot": "^4.0.3", "react-native-web": "~0.19.13", "react-redux": "^9.2.0", "uuid": "^11.1.0", "zod": "^3.24.3"}, "devDependencies": {"@babel/core": "^7.20.0", "@react-native-community/cli": "latest", "@tsconfig/react-native": "^3.0.5", "@types/react": "^18.3.18", "@types/react-native-razorpay": "^2.2.6", "@types/react-native-vector-icons": "^6.4.18", "tailwindcss": "3.3.2", "typescript": "^5.7.3"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"listUnknownPackages": false, "exclude": ["react-native-razorpay"]}}}, "private": true}