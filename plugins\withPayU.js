const { withAndroidManifest, withAppBuildGradle } = require("@expo/config-plugins");

const withPayU = (config) => {
  // Handle AndroidManifest.xml conflicts
  config = withAndroidManifest(config, (config) => {
    const androidManifest = config.modResults;

    // Add tools namespace if not present
    if (!androidManifest.manifest.$["xmlns:tools"]) {
      androidManifest.manifest.$["xmlns:tools"] = "http://schemas.android.com/tools";
    }

    // Add tools:replace to application element
    if (androidManifest.manifest.application && androidManifest.manifest.application[0]) {
      const application = androidManifest.manifest.application[0];
      if (!application.$) {
        application.$ = {};
      }
      application.$["tools:replace"] = "android:theme";
    }

    return config;
  });

  // Handle build.gradle conflicts
  config = withAppBuildGradle(config, (config) => {
    if (
      config.modResults.contents.includes("android {") &&
      !config.modResults.contents.includes("packagingOptions {")
    ) {
      // Add packagingOptions to handle resource conflicts
      const packagingOptions = `
    packagingOptions {
        pickFirsts += [
            '**/libc++_shared.so',
            '**/libjsc.so',
            '**/libfolly_runtime.so',
            '**/libreactnativejni.so',
            '**/libturbomodulejsijni.so',
            '**/libyoga.so'
        ]
        excludes += [
            'META-INF/DEPENDENCIES',
            'META-INF/LICENSE',
            'META-INF/LICENSE.txt',
            'META-INF/license.txt',
            'META-INF/NOTICE',
            'META-INF/NOTICE.txt',
            'META-INF/notice.txt',
            'META-INF/ASL2.0',
            'META-INF/*.kotlin_module'
        ]
    }`;

      // Insert packagingOptions before androidResources
      const androidResourcesIndex = config.modResults.contents.indexOf("androidResources {");
      if (androidResourcesIndex !== -1) {
        config.modResults.contents =
          config.modResults.contents.slice(0, androidResourcesIndex) +
          packagingOptions +
          "\n    " +
          config.modResults.contents.slice(androidResourcesIndex);
      }
    }

    return config;
  });

  return config;
};

module.exports = withPayU;
