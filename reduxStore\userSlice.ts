import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { UserState } from "../lib/types/Auth/userTypes";

const initialState: UserState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
};

export const userSlice = createSlice({
  name: "user",
  initialState,
  reducers: {
    setUser: (state, action: PayloadAction<UserState["user"]>) => {
      state.user = action.payload;
      state.isAuthenticated = true;
      state.isLoading = false;
    },
    updateUser: (state, action: PayloadAction<UserState["user"]>) => {
      state.user = action.payload;
      state.isLoading = false;
    },
    setAuthLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    logoutUser: (state) => {
      state.user = null;
      state.isAuthenticated = false;
    },
  },
});

export const { setUser, updateUser, logoutUser, setAuthLoading } = userSlice.actions;
export default userSlice.reducer;
