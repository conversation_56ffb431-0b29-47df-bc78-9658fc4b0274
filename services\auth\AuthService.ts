import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  sendEmailVerification,
} from "firebase/auth";
import { auth } from "../../config/firebase";
import { registerUser } from "../../api/authService";
import { Dispatch } from "@reduxjs/toolkit";
import { logoutUser, setAuthLoading } from "../../reduxStore/userSlice";

// Sign Up
export const signUpWithEmail = async (
  firstName: string,
  lastName: string,
  email: string,
  password: string,
  phone: string,
  dispatch: any
) => {
  console.log("Signing Up...");
  dispatch(setAuthLoading(true));
  const userCredential = await createUserWithEmailAndPassword(auth, email, password);
  console.log(userCredential, userCredential.user);

  const user = userCredential.user;

  // Send email verification
  try {
    await sendEmailVerification(user);
    console.log("Email verification sent to:", email);
  } catch (verificationError) {
    console.error("Failed to send verification email:", verificationError);
    // Don't throw error here, let the user proceed to verification screen
  }

  // Prepare user data for backend
  const userData = {
    firstName,
    lastName,
    email,
    phone,
    firebaseUID: user.uid,
    profilePic: user.photoURL || "",
    role: ["user"],
  };

  // Call backend to register user
  await registerUser(userData, dispatch);
  dispatch(setAuthLoading(false));
  console.log("User registered and stored in Redux!");
  return user;
};

// Sign In
export const signInWithEmail = async (email: string, password: string) => {
  const userCredential = await signInWithEmailAndPassword(auth, email, password);
  console.log("login sucess", userCredential.user);
};

export const signOutUser = async (dispatch: Dispatch) => {
  try {
    await signOut(auth);
    dispatch(logoutUser());
    console.log("Signed out successfully");
  } catch (error) {
    console.error("Sign out error", error);
  }
};
