import { Platform } from "react-native";
import * as Device from "expo-device";
import * as Notifications from "expo-notifications";
import { getAuth } from "firebase/auth";
import apiClient from "../api/apiClient";

// Configure how notifications appear when the app is in the foreground
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

interface RegisterTokenResponse {
  success: boolean;
  message: string;
}

// Setup notification categories/actions if needed
export const setupNotificationCategories = () => {
  Notifications.setNotificationCategoryAsync("message", [
    {
      identifier: "view",
      buttonTitle: "View",
      options: {
        opensAppToForeground: true,
      },
    },
  ]);
};

/**
 * Initialize notification handlers and listeners
 * @param onNotificationReceived Callback for when notification is received
 * @param onNotificationResponse Callback for when user interacts with notification
 */
export const initializeNotifications = (
  onNotificationReceived?: (notification: Notifications.Notification) => void,
  onNotificationResponse?: (response: Notifications.NotificationResponse) => void
) => {
  // Set up notification received listener
  const notificationListener = Notifications.addNotificationReceivedListener((notification) => {
    console.log("Notification received:", notification);
    if (onNotificationReceived) {
      onNotificationReceived(notification);
    }
  });

  // Set up notification response listener
  const responseListener = Notifications.addNotificationResponseReceivedListener((response) => {
    console.log("Notification response:", response);
    if (onNotificationResponse) {
      onNotificationResponse(response);
    }
  });

  // Setup notification categories
  setupNotificationCategories();

  return {
    removeListeners: () => {
      Notifications.removeNotificationSubscription(notificationListener);
      Notifications.removeNotificationSubscription(responseListener);
    },
  };
};

/**
 * Request notification permission from the user
 * @param checkOnly If true, only checks the permission without requesting
 * @returns Promise<boolean> True if permission is granted
 */
export const requestNotificationPermission = async (checkOnly: boolean = false): Promise<boolean> => {
  try {
    // For iOS, we need to request permission first
    if (Platform.OS === "ios") {
      const { status: existingStatus } = await Notifications.getPermissionsAsync();

      // Just check without requesting if checkOnly is true
      if (checkOnly) return existingStatus === "granted";

      // Request permission if not already granted
      if (existingStatus !== "granted") {
        const { status } = await Notifications.requestPermissionsAsync();
        return status === "granted";
      }
      return existingStatus === "granted";
    }

    // For Android, check if permission is granted
    if (Platform.OS === "android") {
      // For Android 13+ (API level 33+), we need to request permission
      if (Platform.Version >= 33) {
        const { status: existingStatus } = await Notifications.getPermissionsAsync();

        console.log(existingStatus, "existingStatus");

        // Just check without requesting if checkOnly is true
        if (checkOnly) return existingStatus === "granted";

        // Request permission if not already granted
        if (existingStatus !== "granted") {
          const { status } = await Notifications.requestPermissionsAsync();
          return status === "granted";
        }
        return existingStatus === "granted";
      }

      // For Android < 13, permissions are granted at install time
      return true;
    }

    return false;
  } catch (error) {
    console.error("Error requesting notification permission:", error);
    return false;
  }
};

/**
 * Register FCM token with the backend
 * @param token FCM token from Firebase
 * @returns Response from the API
 */
export const registerFCMToken = async (token: string): Promise<RegisterTokenResponse> => {
  try {
    const deviceName = Device.modelName || "Unknown Device";
    const platform = Platform.OS;

    const response = await apiClient.post<RegisterTokenResponse>("/fcm/register", {
      token,
      device: deviceName,
      platform,
    });

    return response.data;
  } catch (error) {
    console.error("Error registering FCM token:", error);
    throw error;
  }
};

/**
 * Get the FCM token and register it with the backend
 */
export const setupPushNotifications = async (fcmTokens?: string[]): Promise<void> => {
  try {
    // Check if user is authenticated
    const auth = getAuth();
    if (!auth.currentUser) {
      console.log("User not authenticated, skipping FCM token registration");
      return;
    }

    // Request permission
    const hasPermission = await requestNotificationPermission();
    if (!hasPermission) {
      console.log("Notification permission not granted");
      return;
    }

    // Get the token
    const token = await Notifications.getExpoPushTokenAsync({
      projectId: process.env.EXPO_PUBLIC_PROJECT_ID,
    });

    if (fcmTokens?.includes(token.data)) {
      return;
    }

    // Register token with backend
    await registerFCMToken(token.data);
    console.log("FCM token registered successfully");
  } catch (error) {
    console.error("Error setting up push notifications:", error);
  }
};

/**
 * Schedule a local notification
 * @param title Notification title
 * @param body Notification body
 * @param data Additional data to include with the notification
 */
export const scheduleLocalNotification = async (
  title: string,
  body: string,
  data: Record<string, any> = {}
): Promise<string> => {
  try {
    const notificationId = await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        data,
        sound: true,
        badge: 1,
      },
      trigger: null, // null means show immediately
    });

    return notificationId;
  } catch (error) {
    console.error("Error scheduling local notification:", error);
    throw error;
  }
};

/**
 * Cancel all scheduled notifications
 */
export const cancelAllNotifications = async (): Promise<void> => {
  await Notifications.cancelAllScheduledNotificationsAsync();
};

/**
 * Get all delivered notifications
 */
export const getDeliveredNotifications = async (): Promise<Notifications.Notification[]> => {
  return await Notifications.getPresentedNotificationsAsync();
};

/**
 * Dismiss all delivered notifications
 */
export const dismissAllNotifications = async (): Promise<void> => {
  await Notifications.dismissAllNotificationsAsync();
};

/**
 * Debug function to log the current push token
 */
export const logPushToken = async (): Promise<string | undefined> => {
  try {
    const token = await Notifications.getExpoPushTokenAsync({
      projectId: process.env.EXPO_PUBLIC_PROJECT_ID,
    });

    console.log("Push Token:", token.data);

    // Check if token is registered with backend
    const auth = getAuth();
    if (auth.currentUser) {
      console.log("User is authenticated, token should be registered");
    } else {
      console.log("User is not authenticated, token will not be registered");
    }

    return token.data;
  } catch (error) {
    console.error("Error getting push token:", error);
    return undefined;
  }
};

/**
 * Test function to verify notification setup
 */
export const testNotification = async (): Promise<void> => {
  try {
    const notificationId = await scheduleLocalNotification(
      "Test Notification",
      "This is a test notification to verify your setup is working correctly.",
      { type: "test" }
    );
    console.log("Test notification scheduled with ID:", notificationId);
  } catch (error) {
    console.error("Error sending test notification:", error);
  }
};
