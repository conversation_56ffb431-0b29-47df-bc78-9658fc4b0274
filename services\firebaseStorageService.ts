import "react-native-get-random-values";
import { v4 as uuidv4 } from "uuid";
import { getStorage, ref, uploadBytes, getDownloadURL } from "firebase/storage";

export const generateUniqueFileName = (folder: string): string => {
  const timestamp = new Date().getTime();
  const randomChars = Math.random().toString(36).substring(2, 8);
  return `${folder}/file_${timestamp}_${randomChars}`;
};

/**
 * Upload a file to Firebase Storage and get the public URL.
 * @param fileUri - The URI of the file to upload.
 * @param folder - The folder in Firebase Storage where the file will be stored.
 * @returns The public URL of the uploaded file.
 */
export const uploadFileToFirebase = async (fileUri: string, folder: string): Promise<string | null> => {
  try {
    const storage = getStorage();
    let fileName;

    try {
      fileName = `${folder}/${uuidv4()}`;
    } catch (uuidError) {
      console.error("UUID generation failed:", uuidError);
      fileName = generateUniqueFileName(folder);
    }

    const storageRef = ref(storage, fileName);

    // Fetch the file from the URI and convert it to a Blob
    const response = await fetch(fileUri);
    const fileBlob = await response.blob();

    // Upload the file to Firebase Storage
    await uploadBytes(storageRef, fileBlob);

    // Get the public URL of the uploaded file
    const downloadUrl = await getDownloadURL(storageRef);
    return downloadUrl;
  } catch (error) {
    console.error("Error uploading file to Firebase Storage:", error);
    return null;
  }
};

/**
 * Upload text or JSON data to Firebase Storage.
 * @param data - The text or JSON data to upload.
 * @param folder - The folder in Firebase Storage where the file will be stored.
 * @param extension - The file extension (default: 'txt').
 * @returns The public URL of the uploaded file.
 */
export const uploadTextToFirebase = async (
  data: string | object,
  folder: string,
  filename?: string,
  extension: string = "txt"
): Promise<string | null> => {
  try {
    const storage = getStorage();
    const fileName = filename || `${folder}/${uuidv4()}.${extension}`;
    const storageRef = ref(storage, fileName);

    // Convert data to string if it's an object
    const textContent = typeof data === "object" ? JSON.stringify(data) : data;

    // Create a blob from the text content - fix the type issue
    const textBlob = new Blob([textContent], { type: `text/${extension}` } as any);

    // Upload the text blob to Firebase Storage
    await uploadBytes(storageRef, textBlob);

    // Get the public URL of the uploaded file
    const downloadUrl = await getDownloadURL(storageRef);
    return downloadUrl;
  } catch (error) {
    console.error("Error uploading text to Firebase Storage:", error);
    return null;
  }
};
