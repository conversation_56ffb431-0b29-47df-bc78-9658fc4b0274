import * as Location from "expo-location";

export interface UserLocation {
  coordinates: {
    longitude: number;
    latitude: number;
  };
  address: string;
  city: string;
  country: string;
  state: string;
  zipCode: string;
  countryCode: string;
}

/**
 * Request location permissions from the user
 * @returns true if permissions were granted, false otherwise
 */
export const requestLocationPermission = async (): Promise<boolean> => {
  try {
    const { status } = await Location.requestForegroundPermissionsAsync();
    return status === "granted";
  } catch (error) {
    console.error("Error requesting location permission:", error);
    return false;
  }
};

/**
 * Get the user's current location including coordinates and address
 * @returns UserLocation object or null if location couldn't be determined
 */
export const getCurrentLocation = async (): Promise<UserLocation | null> => {
  try {
    const hasPermission = await requestLocationPermission();

    if (!hasPermission) {
      console.log("Location permission not granted");
      return null;
    }

    // Get current position
    const location = await Location.getCurrentPositionAsync({
      accuracy: Location.Accuracy.Balanced,
    });

    const { latitude, longitude } = location.coords;

    // Reverse geocode to get address
    const [geocodeResult] = await Location.reverseGeocodeAsync({
      latitude,
      longitude,
    });

    // Format address from geocode result
    console.log(geocodeResult, "geocodeResult");

    const address = geocodeResult ? geocodeResult.formattedAddress || "Unknown location" : "Unknown location";

    return {
      coordinates: {
        latitude,
        longitude,
      },
      address,
      city: geocodeResult?.city || "Unknown city",
      country: geocodeResult?.country || "Unknown country",
      state: geocodeResult?.region || "Unknown state",
      zipCode: geocodeResult?.postalCode || "Unknown zip code",
      countryCode: geocodeResult?.isoCountryCode || "Unknown country code",
    };
  } catch (error) {
    console.error("Error getting current location:", error);
    return null;
  }
};
