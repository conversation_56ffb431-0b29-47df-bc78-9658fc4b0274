import PayUBizSdk from "payu-non-seam-less-react";
import { NativeEventEmitter } from "react-native";
import { paymentService } from "../api/paymentService";
import { showToast } from "../lib/utils/showToast";
import { payUCheckoutProConfig } from "../constants/common";

// Define the interface for PayU options
export interface PayUOptions {
  orderId: string;
  paymentId: string;
  key: string;
  amount: number;
  currency: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  description: string;
  payuData: {
    key: string;
    txnid: string;
    amount: string;
    productinfo: string;
    firstname: string;
    email: string;
    phone: string;
    surl: string;
    furl: string;
    hash: string;
    udf1: string;
    udf2: string;
    udf3: string;
    udf4: string;
    udf5: string;
  };
}

// Define the interface for PayU response
export interface PayUResponse {
  result: "success" | "failure" | "cancelled";
  payuResponse?: string;
  merchantResponse?: string;
  error?: string;
}

export interface PayUParams {
  payuData: any;
}

class PayUService {
  private isProcessing = false;
  private eventListeners: Array<{ event: string; subscription: any }> = [];
  private nativeEventEmitter = new NativeEventEmitter();
  private hashListener: any = null;

  async initiatePayment(payUParams: PayUParams): Promise<PayUResponse> {
    if (this.isProcessing) {
      throw new Error("Payment already in progress");
    }

    this.isProcessing = true;

    try {
      return new Promise((resolve, reject) => {
        const payuData = payUParams.payuData;
        // Always map the three required hashes and UDFs into additionalParam
        const additionalParam = {
          udf1: payuData.udf1 ? String(payuData.udf1) : "",
          udf2: payuData.udf2 ? String(payuData.udf2) : "",
          udf3: payuData.udf3 ? String(payuData.udf3) : "",
          udf4: payuData.udf4 ? String(payuData.udf4) : "",
          udf5: payuData.udf5 ? String(payuData.udf5) : "",
          payment_related_details_for_mobile_sdk: payuData.payment_related_details_for_mobile_sdk
            ? String(payuData.payment_related_details_for_mobile_sdk)
            : "",
          vas_for_mobile_sdk: payuData.vas_for_mobile_sdk ? String(payuData.vas_for_mobile_sdk) : "",
          payment: payuData.payment ? String(payuData.payment) : "",
        };
        // Check for missing hashes
        if (
          !additionalParam.payment_related_details_for_mobile_sdk ||
          !additionalParam.vas_for_mobile_sdk ||
          !additionalParam.payment
        ) {
          console.error("PayU: One or more required hashes are missing in additionalParam:", additionalParam);
          throw new Error("PayU integration error: One or more required hashes are missing.");
        }
        // Map all fields to camelCase as required by the SDK
        const payUPaymentParams = {
          key: payuData.key ? String(payuData.key) : "",
          transactionId: payuData.txnid ? String(payuData.txnid) : "",
          amount: payuData.amount ? String(payuData.amount) : "",
          productInfo: payuData.productinfo ? String(payuData.productinfo) : "",
          firstName: payuData.firstname ? String(payuData.firstname) : "",
          email: payuData.email ? String(payuData.email) : "",
          phone: payuData.phone ? String(payuData.phone) : "",
          android_surl: payuData.surl ? String(payuData.surl) : "",
          android_furl: payuData.furl ? String(payuData.furl) : "",
          environment: process.env.EXPO_PUBLIC_NODE_ENV === "production" ? "0" : "1",
          userCredential: payuData.userCredential
            ? String(payuData.userCredential)
            : payuData?.email
            ? `${payuData.key}:${payuData.email}`
            : "default",
          userToken: payuData.userToken ? String(payuData.userToken) : undefined,
          additionalParam,
        };
        // Remove undefined fields (userCredential/userToken if not present)
        const filteredPayUPaymentParams = Object.fromEntries(
          Object.entries(payUPaymentParams).filter(([_, v]) => v !== undefined)
        );
        const paymentParams = { payUPaymentParams: filteredPayUPaymentParams, payUCheckoutProConfig };
        // Set up event listeners
        const successListener = (event: any) => {
          this.cleanup();
          resolve({
            result: "success",
            payuResponse: event.CP_PAYU_RESPONSE,
            merchantResponse: event.CP_MERCHANT_RESPONSE,
          });
        };
        const failureListener = (event: any) => {
          this.cleanup();
          resolve({
            result: "failure",
            payuResponse: event.CP_PAYU_RESPONSE,
            merchantResponse: event.CP_MERCHANT_RESPONSE,
          });
        };
        const cancelListener = (event: any) => {
          this.cleanup();
          showToast("info", "Payment cancelled by user");
          console.warn("PayU: Payment cancelled by user:", event);
          resolve({
            result: "cancelled",
            error: "Payment cancelled by user",
          });
        };
        const errorListener = (event: any) => {
          this.cleanup();
          reject(new Error(`Payment error: ${event.errorMsg || "Unknown error"}`));
        };
        this.eventListeners = [
          {
            event: "onPaymentSuccess",
            subscription: this.nativeEventEmitter.addListener("onPaymentSuccess", successListener),
          },
          {
            event: "onPaymentFailure",
            subscription: this.nativeEventEmitter.addListener("onPaymentFailure", failureListener),
          },
          {
            event: "onPaymentCancel",
            subscription: this.nativeEventEmitter.addListener("onPaymentCancel", cancelListener),
          },
          { event: "onError", subscription: this.nativeEventEmitter.addListener("onError", errorListener) },
        ];
        this.hashListener = this.nativeEventEmitter.addListener("generateHash", async (e) => {
          try {
            const hashValue = await this.fetchHashFromServer(e.hashName, e.hashString, e.postSalt);
            const result = { [e.hashName]: hashValue };
            (PayUBizSdk as any).hashGenerated(result);
          } catch (err) {
            showToast("error", "Failed to generate hash");
          }
        });
        if (
          !filteredPayUPaymentParams.productInfo ||
          typeof filteredPayUPaymentParams.productInfo !== "string" ||
          filteredPayUPaymentParams.productInfo.trim() === ""
        ) {
          console.error("PayU: productInfo is missing or empty in payUPaymentParams:", filteredPayUPaymentParams);
          throw new Error("PayU integration error: productInfo is missing or empty.");
        }
        PayUBizSdk.openCheckoutScreen(paymentParams);
      });
    } catch (error) {
      this.isProcessing = false;
      throw error;
    }
  }

  private async fetchHashFromServer(hashName: string, hashString: string, postSalt?: string): Promise<string> {
    return await paymentService.generateResponseHash(hashName, hashString, postSalt);
  }

  private cleanup() {
    this.isProcessing = false;
    this.eventListeners.forEach(({ subscription }) => {
      subscription.remove();
    });
    this.eventListeners = [];
    if (this.hashListener) {
      this.hashListener.remove();
      this.hashListener = null;
    }
  }

  isPaymentInProgress(): boolean {
    return this.isProcessing;
  }
}

export default new PayUService();
