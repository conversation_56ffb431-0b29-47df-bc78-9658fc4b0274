/** @type {import('tailwindcss').Config} */

module.exports = {
  content: ["./app/**/*.{js,jsx,ts,tsx}", "./components/**/*.{js,jsx,ts,tsx}"],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: "#FFDE59", // Bright Yellow – positivity, optimism,
          100: "#FEF9D8",
          200: "#FEF3B2",
          300: "#FEE98C",
          400: "#FDE06F",
          500: "#FFDE59",
          600: "#D8AE2E",
          700: "#B58D20",
          800: "#926D14",
          900: "#78560C",
        },
        secondary: "#D72638", //"#FF914D",
        accent: "#FF6B6B", // Coral Red – passion, warmth
        background: "#F5F5F5", // Light Gray – subtle, minimal
        textColor: "#2E2E2E", // Deep Charcoal – professional, readable
        link: "#1E4AE9", // Vibrant Blue – highlights & links
        button: "#346AFF", // Stronger blue for buttons
        midnightBlue: "#0A0F1D",

        black: {
          DEFAULT: "#000000",
          100: "#1E1E2D",
          200: "#232533",
        },
        gray: {
          100: "#D9D9D9", // Light gray
          200: "#A9B4CD", // Blue-gray tone
        },
      },
      fontSize: {
        // Headline styles
        "headline-900": ["48px", { fontWeight: "600" }], // Hero section title
        "headline-800": ["36px", { fontWeight: "600" }], // Main titles
        "headline-700": ["24px", { fontWeight: "600" }], // Top level headers
        "headline-600": ["20px", { fontWeight: "600" }], // Key functionality headings
        "headline-500": ["18px", { fontWeight: "600" }], // Sub-section headings
        "headline-400": ["16px", { fontWeight: "500" }], // Deep headings
        "headline-300": ["14px", { fontWeight: "500" }], // Low level headings
        "heading-200": ["13px", { fontWeight: "600" }], // Dropdown and table headings
        "heading-100": ["12px", { fontWeight: "500" }], // Lowest level headings

        // Body text styles
        "body-300": ["16px", { fontWeight: "400" }], // Large paragraph
        "body-200": ["14px", { fontWeight: "400" }], // Normal paragraph
        "body-100": ["12px", { fontWeight: "400" }], // Small paragraph
      },
      fontFamily: {
        // Heading fonts (Poppins)
        heading: ["Poppins-SemiBold"],
        "heading-bold": ["Poppins-Bold"],

        // Subheading fonts (Sora)
        subheading: ["Sora-Medium"],
        "subheading-regular": ["Sora-Regular"],
        "subheading-light": ["Sora-Light"],

        // Body fonts (Sora)
        body: ["Sora-Regular"],
        "body-light": ["Sora-Light"],
        "body-medium": ["Sora-Medium"],
      },
    },
  },
  safelist: ["rounded-xl"],
  plugins: [],
};
