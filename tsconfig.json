{"extends": "@tsconfig/react-native/tsconfig.json", "compilerOptions": {"noImplicitAny": true, "strictNullChecks": true, "jsx": "react-native", "allowJs": true, "skipLibCheck": true, "types": ["node"], "paths": {"@firebase/auth": ["./node_modules/@firebase/auth/dist/index.rn.d.ts"]}}, "include": ["app", "hooks", "services", "lib", "api", "config", "constants", "components", "reduxStore"], "exclude": ["node_modules", "babel.config.js", "metro.config.js"]}